name: Migrate

on:
  push:
    branches:
      - main
    paths:
      - packages/database/databases/transactional/migrations/**/*
      - packages/database/databases/analytics/migrations/**/*

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.get-environment.outputs.environment }}
      machine: ${{ steps.get-environment.outputs.machine }}

    steps:
      - name: Get Environment
        id: get-environment
        run: |
          if [ "${{github.event_name}}" = "push" ]; then
            if [ "${{ github.ref }}" = "refs/heads/main" ]; then
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "machine=env-production" >> $GITHUB_OUTPUT
            fi

            if [ "${{ github.ref }}" = "refs/heads/dev" ]; then
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "machine=env-development" >> $GITHUB_OUTPUT
            fi
          fi

          if [ "${{github.event_name}}" = "workflow_dispatch" ]; then
            if [ "${{ inputs.environment }}" = "production" ]; then
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "machine=env-production" >> $GITHUB_OUTPUT
            elif [ "${{ inputs.environment }}" = "staging" ]; then
              echo "environment=staging" >> $GITHUB_OUTPUT
              echo "machine=env-staging" >> $GITHUB_OUTPUT
            elif [ "${{ inputs.environment }}" = "development" ]; then
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "machine=env-development" >> $GITHUB_OUTPUT
            fi
          fi

  install-az-cli:
    runs-on: ${{ needs.prepare.outputs.machine }}
    needs: [prepare]
    steps:
      - name: Azure CLI Install
        uses: elstudio/action-install-azure-cli@v1

  transactional-migration:
    runs-on: ${{ needs.prepare.outputs.machine }}
    needs: [prepare, install-az-cli]
    environment: ${{ needs.prepare.outputs.environment }}
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Azure Login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.DEPLOY_SPN_PROD }}

      - name: Get Transactional Db Credentials
        id: transactional-credentials
        run: |
          echo "MysqlConnectionString=$(az keyvault secret show --name MysqlConnectionString --vault-name ${{ vars.KEYVAULT_NAME }} --query value -o tsv)" >> $GITHUB_OUTPUT

      - name: Deploy migrations for transactional database
        working-directory: packages/database
        run: pnpm db:migrate:transactional
        env:
          MysqlConnectionString: ${{ steps.transactional-credentials.outputs.MysqlConnectionString }}

  analytics-migration:
    runs-on: ${{ needs.prepare.outputs.machine }}
    needs: [prepare, install-az-cli]
    environment: ${{ needs.prepare.outputs.environment }}
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Azure Login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.DEPLOY_SPN_PROD }}

      - name: Get Analytics Db Credentials
        id: analytics-credentials
        run: |
          echo "AnalyticsDbConnectionString=$(az keyvault secret show --name AnalyticsDbConnectionString --vault-name ${{ vars.KEYVAULT_NAME }} --query value -o tsv)" >> $GITHUB_OUTPUT

      - name: Deploy migrations for analytics database
        working-directory: packages/database
        run: pnpm db:migrate:analytics
        env:
          AnalyticsDbConnectionString: ${{ steps.analytics-credentials.outputs.AnalyticsDbConnectionString }}
