name: Check PRs

on:
  pull_request:
    types: [opened, synchronize, edited]

jobs:
  lint-pr:
    name: Validate PR title
    runs-on: ubuntu-latest
    if: github.actor != 'graphite-app'
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  linting:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Lint code
        run: pnpm lint

  tests:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run tests (excluding API integration tests)
        run: pnpm test --recursive --filter=\!@nclarity/api

  api-integration-tests:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: super-secret-password
          MYSQL_USER: nclarityuser
          MYSQL_PASSWORD: secret-password
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost -u root -psuper-secret-password"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Initialize MySQL databases
        run: |
          mysql -h 127.0.0.1 -P 3306 -u root -psuper-secret-password -e "CREATE DATABASE IF NOT EXISTS \`nclarity-db-development-01\`;"
          mysql -h 127.0.0.1 -P 3306 -u root -psuper-secret-password -e "CREATE DATABASE IF NOT EXISTS \`analytics-development-01\`;"
          mysql -h 127.0.0.1 -P 3306 -u root -psuper-secret-password -e "GRANT ALL PRIVILEGES ON \`nclarity-db-development-01\`.* TO 'nclarityuser'@'%';"
          mysql -h 127.0.0.1 -P 3306 -u root -psuper-secret-password -e "GRANT ALL PRIVILEGES ON \`analytics-development-01\`.* TO 'nclarityuser'@'%';"
          mysql -h 127.0.0.1 -P 3306 -u root -psuper-secret-password -e "FLUSH PRIVILEGES;"

      - name: Run database migrations
        working-directory: packages/database
        env:
          MysqlConnectionString: mysql://nclarityuser:secret-password@127.0.0.1:3306/nclarity-db-development-01
          AnalyticsDbConnectionString: mysql://nclarityuser:secret-password@127.0.0.1:3306/analytics-development-01
        run: |
          pnpm prisma migrate reset --force --schema databases/analytics/analytics.prisma
          pnpm prisma migrate reset --force --schema databases/transactional/schema.prisma
          pnpm tsx databases/transactional/migrations/41-add-equipment-profiles/defaults.ts

      - name: Run API integration tests
        working-directory: apps/api
        run: pnpm test

  typechecks:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run typechecks
        run: pnpm typecheck
