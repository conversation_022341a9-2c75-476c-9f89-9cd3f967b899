name: <PERSON>App Deploy

on:
  workflow_call:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: string

      branchref:
        description: 'Branch to deploy'
        required: true
        type: string

      appname:
        description: 'App name that will be deployed'
        required: true
        type: string

      working-directory:
        description: 'Working directory of the app'
        required: true
        type: string

      resource-group:
        description: 'Resource group of the app in Azure'
        required: true
        type: string

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branchref }}

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Build code
        run: pnpm build
        working-directory: ${{inputs.working-directory}}

      - name: Upload artifact
        uses: actions/upload-artifact@v4.3.3
        with:
          name: ${{inputs.appname}}-${{inputs.environment}}-${{github.sha}}
          path: ${{inputs.working-directory}}/dist
          retention-days: 1

  deploy:
    runs-on: ubuntu-latest
    needs: build
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    environment:
      name: ${{ inputs.environment }}
      url: ${{ steps.deploy-to-azure.outputs.webapp-url }}

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4.1.7
        with:
          name: ${{inputs.appname}}-${{inputs.environment}}-${{github.sha}}

      - name: Archive Release
        uses: thedoctor0/zip-release@main
        with:
          type: 'zip'
          filename: 'release.zip'
          directory: .

      - name: Azure Login
        uses: Azure/login@v2
        with:
          creds: ${{ secrets.DEPLOY_SPN_PROD }}

      - name: Get Public IP of Github runner
        id: ip
        run: |
          ip=$(curl -s https://api.ipify.org)
          echo "ipv4=${ip}" >> $GITHUB_OUTPUT

      - name: Allow Github runner public IP to deploy code
        shell: pwsh
        run: |
          az webapp config access-restriction add -g ${{ inputs.resource-group }} -n ${{ inputs.appname }} --rule-name GithubRunner --action Allow --ip-address ${{ steps.ip.outputs.ipv4 }}/32 --priority 200 --scm-site true

      - name: Set Web App profile
        shell: pwsh
        id: webapp-profile
        run: |
          $profile = az webapp deployment list-publishing-profiles --resource-group  ${{ inputs.resource-group }} --name ${{ inputs.appname }}
          $profile = $profile.Replace("`r", "").Replace("`n", "")
          echo "AZURE_WEBAPP_PUBLISH_PROFILE=${profile}" >> $GITHUB_OUTPUT

      - name: 'Deploy to Azure WebApp'
        uses: azure/webapps-deploy@v3
        id: deploy-to-azure
        with:
          app-name: ${{ inputs.appname }}
          resource-group-name: ${{ inputs.resource-group }}
          publish-profile: ${{ steps.webapp-profile.outputs.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./release.zip

      - name: Remove Github runner public IP
        run: |
          az webapp config access-restriction remove -g ${{ inputs.resource-group }} -n ${{ inputs.appname }} --rule-name GithubRunner --scm-site true
