name: Deploy Nexus App

on:
  push:
    branches:
      - dev
      - main
      - staging
    paths:
      - apps/nexus/**/*
      - packages/database/**/*

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.main.outputs.matrix }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.ref }}

      - name: main
        id: main
        shell: bash
        run: |
          if [ "${{github.event_name}}" = "push" ]; then
            if [ "${{ github.ref }}" = "refs/heads/main" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/nexus/prod.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi

            if [ "${{ github.ref }}" = "refs/heads/dev" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/nexus/dev.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi
          fi

          if [ "${{github.event_name}}" = "workflow_dispatch" ]; then
            if [ "${{ inputs.environment }}" = "production" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/nexus/prod.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            elif [ "${{ inputs.environment }}" = "staging" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/nexus/staging.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            elif [ "${{ inputs.environment }}" = "development" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/nexus/dev-only.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi
          fi

  deploy-function-app:
    needs: prepare
    uses: ./.github/workflows/functionApps.yml
    secrets: 'inherit'
    strategy:
      matrix: ${{ fromJson(needs.prepare.outputs.matrix) }}
    with:
      appname: ${{ matrix.app-name }}
      branchref: ${{ github.ref_name }}
      environment: ${{ matrix.environment }}
      resource-group: ${{ matrix.resource-group }}
      working-directory: ${{ matrix.workdir }}
