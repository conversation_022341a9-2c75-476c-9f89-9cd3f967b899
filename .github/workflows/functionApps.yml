name: Function App Deployment

on:
  workflow_call:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: string

      branchref:
        description: 'Branch to deploy'
        required: true
        type: string

      appname:
        description: 'App name that will be deployed'
        required: true
        type: string

      working-directory:
        description: 'Working directory of the app'
        required: true
        type: string

      resource-group:
        description: 'Resource group of the app in Azure'
        required: true
        type: string

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install
        working-directory: ${{inputs.working-directory}}

      - name: Build code
        run: pnpm build
        working-directory: ${{inputs.working-directory}}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4.3.3
        with:
          name: ${{inputs.appname}}-${{github.sha}}
          retention-days: 1
          path: |
            ${{inputs.working-directory}}/dist/**/*
            !${{inputs.working-directory}}/dist/**/*.settings.json

  deploy:
    runs-on: ubuntu-latest
    needs: [build]
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    environment:
      name: ${{ inputs.environment }}

    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4.1.7
        with:
          name: ${{inputs.appname}}-${{github.sha}}

      - name: Azure Login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.DEPLOY_SPN_PROD }}

      - name: Get Public IP of Github runner
        id: ip
        run: |
          ip=$(curl -s https://api.ipify.org)
          echo "ipv4=${ip}" >> $GITHUB_OUTPUT

      - name: Allow Github runner public IP to deploy code
        shell: pwsh
        run: |
          az functionapp config access-restriction add -g ${{ inputs.resource-group }} -n ${{ inputs.appname }} --rule-name GithubRunner --action Allow --ip-address ${{ steps.ip.outputs.ipv4 }}/32 --priority 200 --scm-site true

      - name: Get WebApp publish profile
        shell: pwsh
        id: publish-profile
        run: |
          $profile = az functionapp deployment list-publishing-profiles --resource-group  ${{ inputs.resource-group }} --name ${{ inputs.appname }}
          $profile = $profile.Replace("`r", "").Replace("`n", "")
          echo "PublishProfile=${profile}" >> $GITHUB_OUTPUT

      - name: Install Azure Functions Core Tool
        shell: pwsh
        run: |
          wget -q https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb &&
          sudo dpkg -i packages-microsoft-prod.deb &&
          sudo apt-get update &&
          sudo apt-get install azure-functions-core-tools-4 -y

      - name: 'Deploy the Function App'
        id: deploy-to-azure
        shell: pwsh
        run: |
          func azure functionapp publish ${{ inputs.appname }} --nozip --no-build --javascript

      - name: Remove Github runner public IP
        shell: pwsh
        run: |
          az webapp config access-restriction remove -g ${{ inputs.resource-group }} -n ${{ inputs.appname }} --rule-name GithubRunner --scm-site true
