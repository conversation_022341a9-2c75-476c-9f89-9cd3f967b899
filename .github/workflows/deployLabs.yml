name: Deploy Labs API

on:
  push:
    branches:
      - dev
      - main
      - staging
    paths:
      - apps/labs/**/*
      - packages/database/**/*

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.main.outputs.matrix }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.ref }}

      - name: main
        id: main
        shell: bash
        run: |
          if [ "${{github.event_name}}" = "push" ]; then
            if [ "${{ github.ref }}" = "refs/heads/main" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/labs/prod.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi

            if [ "${{ github.ref }}" = "refs/heads/dev" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/labs/dev.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi
          fi

          if [ "${{github.event_name}}" = "workflow_dispatch" ]; then
            if [ "${{ inputs.environment }}" = "production" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/labs/prod.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            elif [ "${{ inputs.environment }}" = "staging" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/labs/staging.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            elif [ "${{ inputs.environment }}" = "development" ]; then
              echo "matrix=$(cat ./.github/workflows/templates/labs/dev-only.json | jq -r tostring)" >> "$GITHUB_OUTPUT"
            fi
          fi

  deploy-webapp:
    needs: prepare
    uses: ./.github/workflows/webapps.yml
    secrets: 'inherit'
    strategy:
      matrix: ${{ fromJson(needs.prepare.outputs.matrix) }}
    with:
      appname: ${{ matrix.app-name }}
      branchref: ${{ github.ref_name }}
      environment: ${{ matrix.environment }}
      resource-group: ${{ matrix.resource-group }}
      working-directory: ${{ matrix.workdir }}
