import { randomUUID } from 'node:crypto';
import { xEquipmentProfiles } from '@nclarity/database';

import type { RelationalDatabaseClient } from '../../../prismaClient.js';

export async function getEquipmentProfileById(
  db: RelationalDatabaseClient,
  params: {
    profileId: string;
    deviceId?: string;
  },
) {
  const { profileId, deviceId } = params;

  if (!profileId) {
    const defaultSystemProfile = await db.systemProfile.findUniqueOrThrow({
      where: {
        id: '90005',
      },
    });

    const equipmentId = randomUUID() as string;
    const defaultProfile: xEquipmentProfiles.EquipmentProfile = {
      id: randomUUID() as string,
      isDefault: true,
      oaSensorOptional: false,
      voltsSensorOptional: false,
      inServiceDate: null,
      equipmentId,
      updatedAt: new Date(),
      createdAt: new Date(),
      data: {
        atmPres: 14,
        equipmentInfo: {
          kind: xEquipmentProfiles.ProfileDataKind.Packaged,
          System: {
            systemType: xEquipmentProfiles.EquipmentInfoSystemType.Package,
            frequency: xEquipmentProfiles.EquipmentInfoSystemFrequency['60Hz'],
            Name: deviceId ?? equipmentId,
          },
          Condenser: {
            NominalVoltage:
              xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage[
                '208V/230'
              ],
            Phase: xEquipmentProfiles.EquipmentInfoCondenserPhase.SinglePhase,
          },
          equipmentId,
        },
      },
      systemProfileId: '90005',
      systemProfile: defaultSystemProfile,
    };

    return defaultProfile;
  }

  const result = await db.equipmentProfile.findUnique({
    where: {
      id: profileId,
    },
    include: {
      systemProfile: true,
    },
  });

  return result;
}
