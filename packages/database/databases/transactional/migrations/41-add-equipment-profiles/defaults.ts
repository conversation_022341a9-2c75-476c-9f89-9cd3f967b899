/* eslint-disable no-await-in-loop, @typescript-eslint/no-unsafe-enum-comparison */

import { randomUUID } from 'node:crypto';
import chalk from 'chalk';

import { xEquipmentProfiles } from '../../extensions/xEquipmentProfiles.js';
import {
  DEFAULT_REFRIGERANTS,
  DEFAULT_SYSTEM_PROFILES,
  createRelationalClient,
} from '../../index.js';

const relationalClient = createRelationalClient();

async function executor() {
  console.log(chalk.green('Starting migration for equipment profiles'));

  const defaultSystemProfile = DEFAULT_SYSTEM_PROFILES.find(
    (profile) => profile.id === '90005',
  );

  if (!defaultSystemProfile) {
    throw new Error('Default system profile not found');
  }

  const refrigerantsTx = relationalClient.refrigerant.createMany({
    data: DEFAULT_REFRIGERANTS.map(
      // @ts-expect-error - __v is an internal field for mongoose
      ({ _id, refrigerantId, __v, temperatureInfo, img, ...refrigerant }) => {
        // @ts-expect-error - _id is an internal field for mongoose
        delete temperatureInfo._id;

        return {
          ...refrigerant,
          id: randomUUID() as string,
          refrigerantId: refrigerantId,
          temperatureInfo,
        } as unknown as xEquipmentProfiles.Refrigerant;
      },
    ),
  });

  console.log();
  process.stdout.write(
    chalk.green(`\rCreated ${DEFAULT_REFRIGERANTS.length} refrigerants`),
  );

  const defaultSystemProfilesBulk = relationalClient.systemProfile.createMany({
    data: DEFAULT_SYSTEM_PROFILES,
  });

  process.stdout.write(
    chalk.green(
      `\rCreated ${DEFAULT_SYSTEM_PROFILES.length} default system profiles`,
    ),
  );

  await relationalClient.$transaction([
    refrigerantsTx,
    defaultSystemProfilesBulk,
  ]);

  process.stdout.write(
    chalk.green('\r ✔ All transactions completed successfully\n\n'),
  );
}

async function main() {
  try {
    await executor();
  } finally {
    await relationalClient.$disconnect();
  }
}

main().catch((error) => {
  console.error(chalk.red('❌ Migration failed'));
  console.error(error);
  process.exit(1);
});
