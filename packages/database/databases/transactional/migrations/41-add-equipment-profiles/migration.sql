-- CreateTable
CREATE TABLE `EquipmentProfile` (
    `id` VARCHAR(191) NOT NULL,
    `isDefault` BOOLEAN NOT NULL DEFAULT true,
    `inServiceDate` DATETIME(3) NULL,
    `oaSensorOptional` BO<PERSON>EAN NOT NULL DEFAULT false,
    `voltsSensorOptional` BOOLEAN NOT NULL DEFAULT false,
    `systemProfileId` VARCHAR(191) NOT NULL,
    `equipmentId` VARCHAR(191) NOT NULL,
    `data` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `EquipmentProfile_equipmentId_key`(`equipmentId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SystemProfile` (
    `id` VARCHAR(191) NOT NULL,
    `isDefault` BOOLEAN NOT NULL DEFAULT false,
    `corporateId` INTEGER NULL,
    `profileName` VARCHAR(191) NULL,
    `systemType` ENUM('AirConditioning', 'HeatPump', 'Refrigerator') NULL,
    `heatingMode` BOOLEAN NULL,
    `meteringDeviceType` ENUM('StandardTXV', 'EXV', 'FixedOrifice') NULL,
    `refrigerantId` INTEGER NOT NULL,
    `condenserName` VARCHAR(191) NULL,
    `customCondenser` BOOLEAN NULL DEFAULT false,
    `condenserEfficiencyValue` ENUM('SEER68', 'SEER1012', 'SEER1316', 'SEER1822') NULL,
    `condenserDTDCTOA` INTEGER NULL,
    `headPressureControlId` INTEGER NULL,
    `minCondTemperature` INTEGER NULL,
    `minCondPressure` INTEGER NULL,
    `pressureDifferential` INTEGER NULL,
    `targetSubCooling` INTEGER NULL,
    `targetSystemSubCooling` INTEGER NULL,
    `evaporatorName` VARCHAR(191) NULL,
    `evaporatorEfficiencyValue` ENUM('StandardOperation', 'Class1', 'Class2', 'Class3', 'Class4', 'HighLatentCapacity', 'HighSensibleCapacity') NULL,
    `evaporatorDTD` INTEGER NULL,
    `boxTemperature` INTEGER NULL,
    `targetSuperheat` INTEGER NULL,
    `targetSystemSuperheat` INTEGER NULL,
    `compressorCount` INTEGER NULL,
    `evaporatorCount` INTEGER NULL,
    `condensorCount` INTEGER NULL,
    `customEvaporator` BOOLEAN NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Tag` (
    `id` VARCHAR(191) NOT NULL,
    `description` VARCHAR(250) NOT NULL,
    `category` ENUM('Accessories', 'Location', 'AreaServed') NOT NULL,
    `corporateId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Tag_description_key`(`description`),
    INDEX `description`(`description`),
    INDEX `corporateId`(`corporateId`),
    INDEX `category`(`category`),
    INDEX `description_category`(`description`, `category`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Refrigerant` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `refrigerantId` INTEGER NOT NULL,
    `filtered` BOOLEAN NOT NULL DEFAULT false,
    `ARIColor` VARCHAR(50) NOT NULL,
    `color` VARCHAR(50) NOT NULL,
    `limLow` DOUBLE NOT NULL,
    `limHi` DOUBLE NOT NULL,
    `lol` DOUBLE NOT NULL,
    `hil` DOUBLE NOT NULL,
    `value` DOUBLE NOT NULL,
    `pressureInfo` JSON NOT NULL,
    `temperatureInfo` JSON NOT NULL,
    `optPTh` ENUM('FifthOrderInverse') NULL,
    `optPTl` ENUM('FifthOrderInverse') NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Refrigerant_name_key`(`name`),
    UNIQUE INDEX `Refrigerant_refrigerantId_key`(`refrigerantId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `_EquipmentProfileToTag` (
    `A` VARCHAR(191) NOT NULL,
    `B` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `_EquipmentProfileToTag_AB_unique`(`A`, `B`),
    INDEX `_EquipmentProfileToTag_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `Equipment_documentId_key` ON `Equipment`(`documentId`);

-- AddForeignKey
ALTER TABLE `EquipmentProfile` ADD CONSTRAINT `EquipmentProfile_systemProfileId_fkey` FOREIGN KEY (`systemProfileId`) REFERENCES `SystemProfile`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `SystemProfile` ADD CONSTRAINT `SystemProfile_refrigerantId_fkey` FOREIGN KEY (`refrigerantId`) REFERENCES `Refrigerant`(`refrigerantId`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `Tag` ADD CONSTRAINT `Tag_corporateId_fkey` FOREIGN KEY (`corporateId`) REFERENCES `accounts`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_EquipmentProfileToTag` ADD CONSTRAINT `_EquipmentProfileToTag_A_fkey` FOREIGN KEY (`A`) REFERENCES `EquipmentProfile`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_EquipmentProfileToTag` ADD CONSTRAINT `_EquipmentProfileToTag_B_fkey` FOREIGN KEY (`B`) REFERENCES `Tag`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

