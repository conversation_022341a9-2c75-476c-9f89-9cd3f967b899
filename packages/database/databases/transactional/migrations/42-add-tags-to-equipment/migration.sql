-- DropForeignKey
ALTER TABLE `_EquipmentProfileToTag` DROP FOREIGN KEY `_EquipmentProfileToTag_A_fkey`;

-- DropForeignKey
ALTER TABLE `_EquipmentProfileToTag` DROP FOREIGN KEY `_EquipmentProfileToTag_B_fkey`;

-- DropTable
DROP TABLE `_EquipmentProfileToTag`;

-- CreateTable
CREATE TABLE `_EquipmentToTag` (
    `A` VARCHAR(191) NOT NULL,
    `B` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `_EquipmentToTag_AB_unique`(`A`, `B`),
    INDEX `_EquipmentToTag_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- DropIndex
DROP INDEX `Tag_description_key` ON `Tag`;

-- CreateIndex
CREATE UNIQUE INDEX `description_corporateId` ON `Tag`(`description`, `corporateId`);

-- AddForeignKey
ALTER TABLE `_EquipmentToTag` ADD CONSTRAINT `_EquipmentToTag_A_fkey` FOREIGN KEY (`A`) REFERENCES `Equipment`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_EquipmentToTag` ADD CONSTRAINT `_EquipmentToTag_B_fkey` FOREIGN KEY (`B`) REFERENCES `Tag`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
