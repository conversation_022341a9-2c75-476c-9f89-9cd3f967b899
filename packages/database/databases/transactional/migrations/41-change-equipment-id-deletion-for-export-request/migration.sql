-- DropForeign<PERSON>ey
ALTER TABLE `ExportRequestEquipmentIds` DROP FOREIGN KEY `ExportRequestEquipmentIds_equipmentId_fkey`;

-- AlterTable
ALTER TABLE `ExportRequestEquipmentIds` MODIFY `equipmentId` VARCHAR(191) NULL;

-- AddFore<PERSON>Key
ALTER TABLE `ExportRequestEquipmentIds` ADD CONSTRAINT `ExportRequestEquipmentIds_equipmentId_fkey` FOREIGN KEY (`equipmentId`) REFERENCES `Equipment`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
