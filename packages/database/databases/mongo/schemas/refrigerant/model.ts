import logger from '@nclarity/logger';
import {
  Document,
  Model,
  Schema,
  SchemaTypes,
  model as mongooseModel,
} from 'mongoose';

import {
  RefrigerantOptPT,
  type Refrigerant,
} from '../../../../shared/entities/mongo.js';
import { DEFAULT_REFRIGERANTS } from './defaults.js';

/** @deprecated Kept for backward compatibility */
const pressureInfoSchema = new Schema({
  ah: {
    type: SchemaTypes.Number,
    required: false,
  },
  bh: {
    type: SchemaTypes.Number,
    required: false,
  },
  ch: {
    type: SchemaTypes.Number,
    required: false,
  },
  dh: {
    type: SchemaTypes.Number,
    required: false,
  },
  eh: {
    type: SchemaTypes.Number,
    required: false,
  },
  fh: {
    type: SchemaTypes.Number,
    required: false,
  },
  gh: {
    type: SchemaTypes.Number,
    required: false,
  },
  hh: {
    type: SchemaTypes.Number,
    required: false,
  },
  ih: {
    type: SchemaTypes.Number,
    required: false,
  },
  al: {
    type: SchemaTypes.Number,
    required: false,
  },
  bl: {
    type: SchemaTypes.Number,
    required: false,
  },
  cl: {
    type: SchemaTypes.Number,
    required: false,
  },
  dl: {
    type: SchemaTypes.Number,
    required: false,
  },
  el: {
    type: SchemaTypes.Number,
    required: false,
  },
  fl: {
    type: SchemaTypes.Number,
    required: false,
  },
  gl: {
    type: SchemaTypes.Number,
    required: false,
  },
  hl: {
    type: SchemaTypes.Number,
    required: false,
  },
  il: {
    type: SchemaTypes.Number,
    required: false,
  },
});

/** @deprecated Kept for backward compatibility */
const temperatureInfoSchema = new Schema({
  aht: { type: SchemaTypes.Number, required: false },
  bht: { type: SchemaTypes.Number, required: false },
  cht: { type: SchemaTypes.Number, required: false },
  dht: { type: SchemaTypes.Number, required: false },
  eht: { type: SchemaTypes.Number, required: false },
  fht: { type: SchemaTypes.Number, required: false },
  ght: { type: SchemaTypes.Number, required: false },
  hht: { type: SchemaTypes.Number, required: false },
  iht: { type: SchemaTypes.Number, required: false },
  alt: { type: SchemaTypes.Number, required: false },
  blt: { type: SchemaTypes.Number, required: false },
  clt: { type: SchemaTypes.Number, required: false },
  dlt: { type: SchemaTypes.Number, required: false },
  elt: { type: SchemaTypes.Number, required: false },
  flt: { type: SchemaTypes.Number, required: false },
  glt: { type: SchemaTypes.Number, required: false },
  hlt: { type: SchemaTypes.Number, required: false },
  ilt: { type: SchemaTypes.Number, required: false },
});

/** @deprecated Kept for backward compatibility */
const refrigerantSchema = new Schema<Refrigerant>(
  {
    name: {
      type: SchemaTypes.String,
      required: true,
      unique: true,
      index: true,
    },
    refrigerantId: {
      type: SchemaTypes.Number,
      required: true,
      unique: true,
      index: true,
    },
    filtered: {
      type: SchemaTypes.Boolean,
      required: true,
    },
    ARIColor: {
      type: SchemaTypes.String,
      required: true,
    },
    color: {
      type: SchemaTypes.String,
      required: true,
    },
    limLow: {
      type: SchemaTypes.Number,
      required: true,
    },
    limHi: {
      type: SchemaTypes.Number,
      required: true,
    },
    lol: {
      type: SchemaTypes.Number,
      required: true,
    },
    hil: {
      type: SchemaTypes.Number,
      required: true,
    },
    value: {
      type: SchemaTypes.Number,
      required: true,
    },
    pressureInfo: {
      type: pressureInfoSchema,
      required: false,
    },
    temperatureInfo: {
      type: temperatureInfoSchema,
      required: false,
    },
    optPTh: {
      type: SchemaTypes.String,
      enum: RefrigerantOptPT,
    },
    optPTl: {
      type: SchemaTypes.String,
      enum: RefrigerantOptPT,
    },
  },
  {
    timestamps: true,
    validateBeforeSave: true,
  },
);

export const RefrigerantModel = mongooseModel<Refrigerant>(
  'Refrigerant',
  refrigerantSchema,
);

export async function createDefaultRefrigerants(): Promise<void> {
  const documents = await RefrigerantModel.find(
    {},
    {
      _id: 1,
      refrigerantId: 1,
    },
  ).exec();

  if (DEFAULT_REFRIGERANTS.length > documents.length) {
    logger.info('Not all default refrigerants are present in the database');

    const filtered = DEFAULT_REFRIGERANTS.filter(
      (refrigerant) =>
        !documents.some(
          (document) => document.refrigerantId === refrigerant.refrigerantId,
        ),
    );

    await RefrigerantModel.create(filtered, {
      validateBeforeSave: true,
    });

    logger.info('Refrigerants seeded');

    return;
  }

  if (documents.length !== 0) {
    logger.info('Refrigerants already seeded');

    return;
  }

  await RefrigerantModel.create([...DEFAULT_REFRIGERANTS], {
    validateBeforeSave: true,
  });

  logger.info('Refrigerants seeded');
}

export type RefrigerantModelType = Model<
  Refrigerant,
  unknown,
  unknown,
  unknown,
  Refrigerant
>;

export type RefrigerantDocument = Document<string, unknown, Refrigerant> &
  Refrigerant &
  Required<{
    _id: string;
  }>;

export {
  type Refrigerant,
  RefrigerantOptPT,
} from '../../../../shared/entities/mongo.js';
export { DEFAULT_REFRIGERANTS } from './defaults.js';
