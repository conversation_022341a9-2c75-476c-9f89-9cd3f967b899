import { Document, Model, Schema, model as mongooseModel } from 'mongoose';

import type { PulseAllData } from '../../../../shared/entities/mongo.js';

/** @deprecated Kept for backward compatibility */
const PulseAllDataSchema = new Schema<PulseAllData>({
  _id: {
    type: String,
    required: true,
  },
  year: {
    type: String,
    required: true,
  },
  month: {
    type: String,
    required: true,
  },
  partMonth: {
    type: String,
    required: true,
  },
  pulseID: {
    type: String,
    required: true,
  },
  data: {
    type: {},
    default: {},
  },
  lastUpdateAt: {
    type: Date,
    required: false,
  },
});

/** @deprecated Kept for backward compatibility */
export const PulseAllDataModel = mongooseModel<PulseAllData>(
  'PulseAllData',
  PulseAllDataSchema,
  'pulsealldata',
);

/**
 * @deprecated Kept for backward compatibility
 */
export type PulseAllDataDocument = Document<string, unknown, PulseAllData> &
  PulseAllData &
  Required<{
    _id?: string;
  }>;

/**
 * @deprecated Kept for backward compatibility
 * */
export type PulseAllDataModelType = Model<
  PulseAllData,
  unknown,
  unknown,
  unknown,
  PulseAllData
>;

export type { PulseAllData } from '../../../../shared/entities/mongo.js';
