import { Schema, model as mongooseModel } from 'mongoose';

/** @deprecated Kept for backward compatibility */
const TagSchema = new Schema(
  {
    description: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    equipments: {
      type: [String],
      required: true,
    },
    category: {
      type: String,
      required: true,
      enum: ['accesories', 'location', 'areaServed'],
      index: true,
    },
    accountId: {
      type: Number,
      required: true,
      index: true,
    },
  },
  {
    timestamps: true,
  },
);

/** @deprecated Kept for backward compatibility */
export const TagModel = mongooseModel('Tag', TagSchema);
