import {
  SystemProfileCondenserEfficiencyValue,
  SystemProfileEvaporatorEfficiencyValue,
  SystemProfileMeteringDeviceType,
  SystemProfileSystemType,
  type SystemProfile,
} from '../../../../shared/entities/mongo.js';

export const DEFAULT_SYSTEM_PROFILES: SystemProfile[] = [
  {
    id: 90001,
    _id: 90001,
    profileId: 90001,
    isDefault: true,
    profileName: '13 SEER A/C, R410A, TXV',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserDTDCTOA: [20],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1316SEER'],
    ],
    headPressureControlId: [0],
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorDTD: [35],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    boxTemperature: [0],
    targetSuperheat: [14.5],
    targetSystemSuperheat: 14.5,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90002,
    _id: 90002,
    profileId: 90002,
    isDefault: true,
    profileName: '10 SEER A/C, R22, Fixed',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.FixedOrifice,
    refrigerantId: 1,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserDTDCTOA: [25],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1012SEER'],
    ],
    headPressureControlId: [0], // control type
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [0],
    targetSystemSubCooling: 0,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorDTD: [35],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    boxTemperature: [0],
    targetSuperheat: [-1],
    targetSystemSuperheat: -1,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90003,
    _id: 90003,
    profileId: 90003,
    isDefault: true,
    profileName: '16 SEER A/C, R410A, TXV',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    customCondenser: [false],
    condenserName: ['Condenser 1'],
    condenserDTDCTOA: [20],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1316SEER'],
    ],
    headPressureControlId: [0], // control type
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorDTD: [35],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    boxTemperature: [0],
    targetSuperheat: [14.5],
    targetSystemSuperheat: 14.5,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90004,
    _id: 90004,
    profileId: 90004,
    isDefault: true,
    profileName: '18 SEER A/C, R410A, TXV',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserDTDCTOA: [15],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1822SEER'],
    ],
    headPressureControlId: [0], // control type
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorDTD: [30],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.HighSensibleCapacity,
    ],
    boxTemperature: [0],
    targetSuperheat: [14.5],
    targetSystemSuperheat: 14.5,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90005,
    _id: 90005,
    profileId: 90005,
    isDefault: true,
    profileName: '10 SEER A/C, R410A, Fixed',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.FixedOrifice,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserDTDCTOA: [25],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1012SEER'],
    ],
    headPressureControlId: [0], // control type
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [0],
    targetSystemSubCooling: 0,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorDTD: [35],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    boxTemperature: [0],
    targetSuperheat: [-1],
    targetSystemSuperheat: -1,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90006,
    _id: 90006,
    profileId: 90006,
    isDefault: true,
    profileName: '13-16 SEER 410A, TXV, COOLING',
    systemType: SystemProfileSystemType.HeatPump,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1316SEER'],
    ],
    condenserDTDCTOA: [20],
    headPressureControlId: [0],
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    evaporatorDTD: [35],
    boxTemperature: [0],
    targetSuperheat: [14.5],
    targetSystemSuperheat: 14.5,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90007,
    _id: 90007,
    profileId: 90007,
    isDefault: true,
    profileName: 'REFRIGERATOR, WALK IN, R22',
    systemType: SystemProfileSystemType.Refrigerator,
    heatingMode: true,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 1,
    applicationRefrigerationId: 17,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1012SEER'],
    ],
    condenserDTDCTOA: [25],
    headPressureControlId: [0],
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: ['Evaporator 1'],
    customEvaporator: [false],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.StandardOperation,
    ],
    evaporatorDTD: [40],
    boxTemperature: [0],
    targetSuperheat: [12],
    targetSystemSuperheat: 12,
    compressorCount: [1],
    evaporatorCount: [1],
    condensorCount: [1],
  },
  {
    id: 90008,
    _id: 90008,
    profileId: 90008,
    isDefault: true,
    profileName: 'Multiple Superheat AC R410A, TXV',
    systemType: SystemProfileSystemType.AirConditioning,
    heatingMode: false,
    meteringDeviceType: SystemProfileMeteringDeviceType.StandardTXV,
    refrigerantId: 22,
    applicationRefrigerationId: -1,
    condenserName: ['Condenser 1'],
    customCondenser: [false],
    condenserEfficiencyValue: [
      SystemProfileCondenserEfficiencyValue['1012SEER'],
    ],
    condenserDTDCTOA: [25],
    headPressureControlId: [0],
    minCondTemperature: [0],
    minCondPressure: [0],
    pressureDifferential: [0],
    targetSubCooling: [10],
    targetSystemSubCooling: 10,
    evaporatorName: [
      'Evaporator 1',
      'Evaporator 2',
      'Evaporator 3',
      'Evaporator 4',
    ],
    customEvaporator: [false, false, false, false],
    evaporatorEfficiencyValue: [
      SystemProfileEvaporatorEfficiencyValue.HighLatentCapacity,
      SystemProfileEvaporatorEfficiencyValue.HighLatentCapacity,
      SystemProfileEvaporatorEfficiencyValue.HighLatentCapacity,
      SystemProfileEvaporatorEfficiencyValue.HighLatentCapacity,
    ],
    evaporatorDTD: [40, 40, 40, 40],
    boxTemperature: [0, 0, 0, 0],
    targetSuperheat: [14.5, 14.5, 14.5, 14.5],
    targetSystemSuperheat: 14.5,
    compressorCount: [1],
    evaporatorCount: [4],
    condensorCount: [1],
  },
];
