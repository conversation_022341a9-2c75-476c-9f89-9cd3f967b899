import {
  SystemProfileCondenserEfficiencyValue,
  SystemProfileEvaporatorEfficiencyValue,
  SystemProfileMeteringDeviceType,
  SystemProfileSystemType,
} from '../../../../shared/entities/mongo.js';

const enums = {
  condenserEfficiency: SystemProfileCondenserEfficiencyValue,
  evaporatorEfficiency: SystemProfileEvaporatorEfficiencyValue,
  meteringDeviceType: SystemProfileMeteringDeviceType,
  systemType: SystemProfileSystemType,
};

export default function validateEnums(enumName: keyof typeof enums) {
  return (toValidate: string | string[], valueIndex?: number) => {
    const validValues = Object.values(enums[enumName]);

    if (Array.isArray(toValidate)) {
      toValidate.some((item, index) => validateEnums(enumName)(item, index));

      return true;
    }

    const index = validValues.findIndex(
      (validValue) => validValue === toValidate,
    );

    if (index < 0) {
      throw new Error(
        `Invalid value for ${enumName}${
          valueIndex ? `[${valueIndex}]` : ''
        }. Value ${toValidate} must be one of ${validValues.join(', ')}`,
      );
    }

    return true;
  };
}
