import { default as logger } from '@nclarity/logger';
import {
  Document,
  Model,
  Schema,
  SchemaTypes,
  model as mongooseModel,
} from 'mongoose';

import { type SystemProfile } from '../../../../shared/entities/mongo.js';
import { DEFAULT_SYSTEM_PROFILES } from './defaults.js';
import validateEnums from './validateEnums.js';

/** @deprecated Kept for backward compatibility */
const schema = new Schema<SystemProfile>(
  {
    _id: {
      type: SchemaTypes.String,
      alias: 'profileId',
      required: true,
    },
    isDefault: {
      type: SchemaTypes.Boolean,
      default: false,
      required: true,
    },
    corporateId: {
      type: SchemaTypes.Number,
      required: false,
    },
    profileName: SchemaTypes.String,
    systemType: {
      type: SchemaTypes.String,
      required: true,
      validate: validateEnums('systemType'),
    },
    heatingMode: SchemaTypes.Boolean,
    meteringDeviceType: {
      type: SchemaTypes.String,
      validate: validateEnums('meteringDeviceType'),
    },
    refrigerantId: SchemaTypes.Number,
    applicationRefrigerationId: SchemaTypes.Number,
    condenserName: [SchemaTypes.String],
    customCondenser: [SchemaTypes.Boolean],
    condenserEfficiencyValue: {
      type: [SchemaTypes.String],
      validate: validateEnums('condenserEfficiency'),
    },
    condenserDTDCTOA: {
      type: [SchemaTypes.Number],
      required: true,
    },
    headPressureControlId: [SchemaTypes.Number],
    minCondTemperature: [SchemaTypes.Number],
    minCondPressure: [SchemaTypes.Number],
    pressureDifferential: [SchemaTypes.Number],
    targetSubCooling: {
      type: [SchemaTypes.Number],
      required: true,
    },
    targetSystemSubCooling: {
      type: SchemaTypes.Number,
      required: true,
    },
    evaporatorName: [SchemaTypes.String],
    evaporatorEfficiencyValue: {
      type: [SchemaTypes.String],
      validate: validateEnums('evaporatorEfficiency'),
    },
    evaporatorDTD: [SchemaTypes.Number],
    boxTemperature: [SchemaTypes.Number],
    targetSuperheat: [SchemaTypes.Number],
    targetSystemSuperheat: {
      type: SchemaTypes.Number,
      required: true,
    },
    compressorCount: {
      type: [SchemaTypes.Number],
      required: true,
    },
    evaporatorCount: {
      type: [SchemaTypes.Number],
      required: true,
    },
    condensorCount: [SchemaTypes.Number],
    customEvaporator: [SchemaTypes.Boolean],
  },
  {
    toJSON: {
      virtuals: true,
      aliases: true,
    },
    toObject: {
      virtuals: true,
      aliases: true,
    },
  },
);

/** @deprecated Kept for backward compatibility */
export const SystemProfileModel = mongooseModel<SystemProfile>(
  'SystemProfile',
  schema,
);

export async function createDefaultSystemProfiles(): Promise<void> {
  const count = await SystemProfileModel.countDocuments().exec();

  if (count !== 0) {
    logger.info('System profiles already seeded');

    return;
  }

  await SystemProfileModel.create(DEFAULT_SYSTEM_PROFILES);

  logger.info('System profiles seeded');
}

export type SystemProfileModelType = Model<
  SystemProfile,
  unknown,
  unknown,
  unknown,
  SystemProfile
>;

export type SystemProfileDocument = Document<string, unknown, SystemProfile> &
  SystemProfile &
  Required<{
    _id?: string;
  }>;

export {
  type SystemProfile,
  SystemProfileCondenserEfficiencyValue,
  SystemProfileEvaporatorEfficiencyValue,
  SystemProfileMeteringDeviceType,
  SystemProfileSystemType,
} from '../../../../shared/entities/mongo.js';
export { DEFAULT_SYSTEM_PROFILES } from './defaults.js';
