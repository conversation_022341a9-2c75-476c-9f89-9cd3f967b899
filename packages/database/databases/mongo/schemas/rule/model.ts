import {
  Document,
  Model,
  Schema,
  SchemaTypes,
  model as mongooseModel,
} from 'mongoose';

import {
  RuleAlertSeverity,
  RuleConditions,
  RuleDispatcherActions,
  RuleInputSourceKeys,
  RuleSetConditionalOperators,
  RuleSetModifier,
  RuleTransformerKeywords,
  RuleType,
  type RuleAlert,
  type RuleInput,
  type RuleSet,
  type RuleSetRuleGroupItem,
  type RuleSetToEquipment,
  type RuleSetValue,
} from '../../../transactional/scripts/types.js';

/** @deprecated Kept for backward compatibility */
const ruleInputSchema = new Schema<RuleInput>({
  source: {
    type: SchemaTypes.String,
    required: true,
    enum: RuleInputSourceKeys,
  },
  label: {
    type: SchemaTypes.Mixed,
    required: true,
  },
  transformer: {
    type: SchemaTypes.String,
    required: false,
    enum: RuleTransformerKeywords,
  },
  transformerSource: {
    type: SchemaTypes.String,
    required: false,
    enum: RuleInputSourceKeys,
  },
  transformerLabel: {
    type: SchemaTypes.String,
    required: false,
  },
});

/** @deprecated Kept for backward compatibility */
const ruleGroupSchema = new Schema<RuleSetRuleGroupItem>({
  condition: {
    type: SchemaTypes.String,
    required: true,
    enum: RuleConditions,
  },
  input: ruleInputSchema,
  conditionInput: ruleInputSchema,
});

/** @deprecated Kept for backward compatibility */
const alertSchema = new Schema<RuleAlert>({
  name: {
    type: SchemaTypes.String,
    required: true,
  },
  severity: {
    type: SchemaTypes.String,
    enum: RuleAlertSeverity,
    index: true,
  },
  dispatcherAction: {
    type: SchemaTypes.String,
    enum: RuleDispatcherActions,
  },
  recommendations: {
    type: SchemaTypes.String,
    text: true,
  },
});

/** @deprecated Kept for backward compatibility */
const setSchema = new Schema<RuleSetValue>({
  setModifier: {
    type: SchemaTypes.String,
    required: true,
    enum: RuleSetModifier,
  },
  group: {
    type: [ruleGroupSchema],
    required: true,
    minlength: 1,
  },
});

/** @deprecated Kept for backward compatibility */
const ruleSchema = new Schema<RuleSet>(
  {
    accountId: {
      type: SchemaTypes.String,
      required: true,
    },
    type: {
      type: SchemaTypes.String,
      required: true,
      enum: RuleType,
      default: RuleType.Static,
    },
    conditionalOperator: {
      type: SchemaTypes.String,
      required: true,
      enum: RuleSetConditionalOperators,
    },
    sets: {
      type: [setSchema],
      required: true,
    },
    alert: alertSchema,
    isActive: {
      type: SchemaTypes.Boolean,
      required: true,
      default: true,
    },
    isDefault: {
      type: SchemaTypes.Boolean,
      required: true,
      default: true,
    },
    deletedAt: {
      type: SchemaTypes.Date,
      required: false,
    },
  },
  {
    timestamps: true,
    validateBeforeSave: true,
  },
);

/** @deprecated Kept for backward compatibility */
export const RuleModel = mongooseModel<RuleSet>('RuleSet', ruleSchema);

/** @deprecated Kept for backward compatibility */
export type RuleDocument = Document<string, unknown, RuleSet> &
  RuleSet &
  Required<{
    _id: string;
  }>;

/** @deprecated Kept for backward compatibility */
export type RuleModelType = Model<RuleSet, unknown, unknown, unknown, RuleSet>;

/** @deprecated Kept for backward compatibility */
export const RuleSetToEquipmentSchema = new Schema<RuleSetToEquipment>(
  {
    equipmentId: {
      type: SchemaTypes.String,
      required: true,
    },
    ruleSet: {
      type: String,
      required: true,
      ref: 'RuleSet',
    },
    isActive: {
      type: SchemaTypes.Boolean,
      required: true,
      default: true,
    },
  },
  {
    timestamps: true,
  },
);

/** @deprecated Kept for backward compatibility */
export const RuleSetToEquipmentModel = mongooseModel(
  'RuleSetToEquipment',
  RuleSetToEquipmentSchema,
);

/** @deprecated Kept for backward compatibility */
export type RuleSetToEquipmentDocument = Document<
  string,
  unknown,
  RuleSetToEquipment
> &
  RuleSetToEquipment &
  Required<{
    _id: string;
  }>;

export {
  type RuleValueEquipmentInfoValueKey,
  type RuleValueTargetValueKey,
  type RuleValueTelemetryValueKey,
  CalculatedValueLabelKeys,
  RuleValueEquipmentInfoValueKeys,
  ruleValueEquipmentInfoValueKeysMap,
  RuleValueTargetValueKeys,
  RuleValueTelemetryValueKeys,
} from '../../../../shared/entities/mongo.js';
