[{"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "condenserNominalVoltage", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "460", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "deltaT", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "5", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "deltaH", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "3", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "spc-1", "transformer": "SubtractOf", "transformerSource": "telemetryValue", "transformerLabel": "llpc-1"}, "conditionInput": {"source": "customValue", "label": "50", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformer": "DivideOf", "transformerSource": "equipmentInfoValue", "transformerLabel": "nominalTonnage"}, "conditionInput": {"source": "customValue", "label": "0.5", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "condenserNominalVoltage", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "208/230v", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "deltaT", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "5", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "deltaH", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "3", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "spc-1", "transformer": "SubtractOf", "transformerSource": "telemetryValue", "transformerLabel": "llpc-1"}, "conditionInput": {"source": "customValue", "label": "3", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformer": "DivideOf", "transformerSource": "equipmentInfoValue", "transformerLabel": "nominalTonnage"}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Cooling Failure", "severity": "critical", "dispatcherAction": "truckDispatch", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T16:42:22.339Z", "updatedAt": "2023-06-29T16:42:22.339Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-2", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-3", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-2", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-2", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-3", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-3", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-3", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "2", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-2", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Single Phase Event", "severity": "critical", "dispatcherAction": "truckDispatch", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T22:36:50.631Z", "updatedAt": "2023-06-29T22:36:50.631Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "0.25", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-2", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "0.25", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "amps-3", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "0.25", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Low/ No Amp", "severity": "critical", "dispatcherAction": "truckDispatch", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T22:41:04.412Z", "updatedAt": "2023-06-29T22:41:04.412Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "spc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "30", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "telemetryValue", "label": "tw", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "spc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "30", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Low/ No Pressure", "severity": "critical", "dispatcherAction": "truckDispatch", "recommendations": "• Potentially low ambient situation"}, "createdAt": "2023-06-29T22:46:44.171Z", "updatedAt": "2023-06-29T22:46:44.171Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "llpc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "450", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "telemetryValue", "label": "tw", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "llpc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "185", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "High Pressure (R410)", "severity": "critical", "dispatcherAction": "truckDispatch", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T22:54:40.320Z", "updatedAt": "2023-06-29T22:54:40.320Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "or", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "llpc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "350", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}, {"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "tw", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "False", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "equalTo", "input": {"source": "equipmentInfoValue", "label": "systemType", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "A/C", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "telemetryValue", "label": "llpc-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "125", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "High Pressure (R22)", "severity": "general", "dispatcherAction": "truckDispatch", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T23:10:05.780Z", "updatedAt": "2023-06-29T23:10:05.780Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "and", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "LLCSubCooling-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "1", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Low/ No Subcooling", "severity": "moderate", "dispatcherAction": "serviceManagerReview", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T23:25:22.828Z", "updatedAt": "2023-06-29T23:25:22.828Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "and", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "LLCSubCooling-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "12", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "High Subcooling", "severity": "moderate", "dispatcherAction": "serviceManagerReview", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T23:26:49.311Z", "updatedAt": "2023-06-29T23:26:49.311Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "and", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "is<PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "SLCSuperHeat-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "5", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "Low/ No Superheat", "severity": "moderate", "dispatcherAction": "serviceManagerReview", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T23:28:17.670Z", "updatedAt": "2023-06-29T23:28:17.670Z", "__v": 0}, {"accountId": "90", "type": "static", "conditionalOperator": "and", "sets": [{"setModifier": "allOf", "group": [{"condition": "equalTo", "input": {"source": "telemetryValue", "label": "ty", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "True", "transformerSource": "telemetryValue", "transformerLabel": ""}}, {"condition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "input": {"source": "calculatedValue", "label": "SLCSuperHeat-1", "transformerSource": "telemetryValue", "transformerLabel": ""}, "conditionInput": {"source": "customValue", "label": "25", "transformerSource": "telemetryValue", "transformerLabel": ""}}]}], "alert": {"name": "High Superheat", "severity": "general", "dispatcherAction": "scheduleWithNextPM", "recommendations": "• Placeholder text"}, "createdAt": "2023-06-29T23:30:58.018Z", "updatedAt": "2023-06-29T23:30:58.018Z", "__v": 0}]