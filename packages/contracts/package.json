{"name": "@nclarity/contracts", "version": "2.48.0", "description": "Entities definition for nClarity", "author": "nClarity LLC", "homepage": "https://nclarity.com", "license": "SEE LICENSE IN LICENSE.md", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/nclarity/backend.git"}, "devDependencies": {"@nclarity/crafter": "workspace:*", "@nclarity/eslint-config": "workspace:*", "@semantic-release/changelog": "6.0.2", "@semantic-release/git": "10.0.1", "@types/node": "18.x", "eslint": "7.28.0", "prettier": "2.8.1", "semantic-release": "20.1.0", "typescript": "5.3.3"}, "type": "module", "main": "dist/index.js", "types": "dist/contracts/index.d.ts", "browser": "dist/index.js", "files": ["dist/**/*", "!dist/**/*.map", "!dist/**/query-engine*", "!dist/**/runtime/*.js"], "publishConfig": {"registry": "https://npm.pkg.github.com", "tag": "latest", "access": "restricted"}, "scripts": {"build": "rm -rf dist && tsc && crafter packages build contracts", "watch:packages": "tsc -w", "test": "echo \"No tests yet...\"", "lint:fix": "eslint  --fix \"**/*.ts\"", "lint:check": "eslint \"**/*.ts\"", "fix": "pnpm format:fix && pnpm lint:fix", "typecheck:watch": "tsc --watch", "typecheck": "tsc", "release": "pnpm build && semantic-release"}}