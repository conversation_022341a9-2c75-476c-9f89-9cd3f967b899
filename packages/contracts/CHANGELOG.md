# [2.48.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.47.0...@nclarity/contracts-v2.48.0) (2025-05-23)


### Features

* [DEV-1291] add mapping for chiller machines ([#771](https://github.com/nclarity/backend/issues/771)) ([9ab82f5](https://github.com/nclarity/backend/commit/9ab82f596e767be138daedffb1371676ec08346e)), closes [/#diff-3fb7495b4124c69da4e0cbe6fd430845924269abe6271170bdc7b2d6f7031b1bR1-R88](https://github.com///issues/diff-3fb7495b4124c69da4e0cbe6fd430845924269abe6271170bdc7b2d6f7031b1bR1-R88) [/#diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8L27-R27](https://github.com///issues/diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8L27-R27) [/#diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8R48-R58](https://github.com///issues/diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8R48-R58) [/#diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8R88-R104](https://github.com///issues/diff-9e8ed0c1aadf4439c1e067f862f81a06940a3ff47e0407867913881e8708d1d8R88-R104) [/#diff-fb2ef3f03a28e5809061330a62292e4455871d391696387aadd480b7f1fa79d7R1-R3](https://github.com///issues/diff-fb2ef3f03a28e5809061330a62292e4455871d391696387aadd480b7f1fa79d7R1-R3) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR778](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR778) [/#diff-49781f495d1544e879b3ce678e1ef71ff9df39f6fad37390c936087b2c2a4a46R1-R220](https://github.com///issues/diff-49781f495d1544e879b3ce678e1ef71ff9df39f6fad37390c936087b2c2a4a46R1-R220)

# [2.47.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.46.0...@nclarity/contracts-v2.47.0) (2025-05-23)


### Bug Fixes

* improve equipment creation controllers and use cases ([#767](https://github.com/nclarity/backend/issues/767)) ([c217175](https://github.com/nclarity/backend/commit/c21717504932eeaa2c668482854e9a85392b333a)), closes [/#diff-5256500628a362fe38ddbf8c421687ae74bc014cc3bd69adc6838ef3a2fd9008L1-L51](https://github.com///issues/diff-5256500628a362fe38ddbf8c421687ae74bc014cc3bd69adc6838ef3a2fd9008L1-L51) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10) [/#diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8L1-L151](https://github.com///issues/diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8L1-L151) [/#diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45L1-L117](https://github.com///issues/diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45L1-L117) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2R22-R24](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2R22-R24) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L38-L70](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L38-L70) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR8-L11](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR8-L11) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dL69-L70](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dL69-L70) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL11](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL11)
* reverse order of CBM relapses to show newest first ([#769](https://github.com/nclarity/backend/issues/769)) ([8157326](https://github.com/nclarity/backend/commit/8157326ce6197859ff808ca538b9b9aa7ad0ee69))


### Features

* [DEV-1289] add new telemetry measures ([#768](https://github.com/nclarity/backend/issues/768)) ([d6976ca](https://github.com/nclarity/backend/commit/d6976ca626218a64a1dec6ce744a029f46a29ac4)), closes [/#diff-5256500628a362fe38ddbf8c421687ae74bc014cc3bd69adc6838ef3a2fd9008L1-L51](https://github.com///issues/diff-5256500628a362fe38ddbf8c421687ae74bc014cc3bd69adc6838ef3a2fd9008L1-L51) [/#diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8L1-L151](https://github.com///issues/diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8L1-L151) [/#diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45L1-L117](https://github.com///issues/diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45L1-L117) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L1-L10) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2R22-R24](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2R22-R24) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L38-L70](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L38-L70) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL11](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL11) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL44](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL44) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR8-L11](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR8-L11) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dL46-R47](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dL46-R47) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R83-R86](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R83-R86) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R142](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R142) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R166](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R166)
* add chiller system type and related profile fields to equipment system ([#766](https://github.com/nclarity/backend/issues/766)) ([9163ec5](https://github.com/nclarity/backend/commit/9163ec5a3d132ee2a6b4b73c1d50b43c00a15d31))

# [2.46.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.45.0...@nclarity/contracts-v2.46.0) (2025-05-19)


### Features

* re-export equipment type ([3324fb0](https://github.com/nclarity/backend/commit/3324fb06012f64876673af645fdb185f4f44b683))

# [2.45.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.44.0...@nclarity/contracts-v2.45.0) (2025-05-19)


### Features

* [DEV-1275] equipment profile migration feature flag cleanup ([#762](https://github.com/nclarity/backend/issues/762)) ([b0e14fb](https://github.com/nclarity/backend/commit/b0e14fb59753ad376984653a782e384b38ef6192))

# [2.44.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.43.0...@nclarity/contracts-v2.44.0) (2025-05-14)


### Bug Fixes

* [DEV-1271] add disconnect in equipment deletion v1 ([#754](https://github.com/nclarity/backend/issues/754)) ([2c94122](https://github.com/nclarity/backend/commit/2c94122038245d2cdffed1941c2eb242ace488cf)), closes [/#diff-1f8310a730052344dc8d0dcbe7f4b3450a506dd00f4755d193f2103c1a7cfc7aL39-L48](https://github.com///issues/diff-1f8310a730052344dc8d0dcbe7f4b3450a506dd00f4755d193f2103c1a7cfc7aL39-L48) [/#diff-1f8310a730052344dc8d0dcbe7f4b3450a506dd00f4755d193f2103c1a7cfc7aR69-R78](https://github.com///issues/diff-1f8310a730052344dc8d0dcbe7f4b3450a506dd00f4755d193f2103c1a7cfc7aR69-R78) [/#diff-0672aee513d8ddc665a224e6777116df81cd9f8034bcb7942fb1224dbf5f7cb2L6-R8](https://github.com///issues/diff-0672aee513d8ddc665a224e6777116df81cd9f8034bcb7942fb1224dbf5f7cb2L6-R8) [/#diff-0672aee513d8ddc665a224e6777116df81cd9f8034bcb7942fb1224dbf5f7cb2L55-R53](https://github.com///issues/diff-0672aee513d8ddc665a224e6777116df81cd9f8034bcb7942fb1224dbf5f7cb2L55-R53)
* [DEV-1271] add referential actions in equipment model ([#756](https://github.com/nclarity/backend/issues/756)) ([81e38bc](https://github.com/nclarity/backend/commit/81e38bc071b4d5ce721fa087006ddf111e65a011)), closes [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL494-R496](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL494-R496) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL654-R656](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL654-R656) [/#diff-af4c2ebece98fc182fdfe4cb764a6ba635e635c6f000087316f99d88470c5606R1-R35](https://github.com///issues/diff-af4c2ebece98fc182fdfe4cb764a6ba635e635c6f000087316f99d88470c5606R1-R35) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR674](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR674) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL688](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL688)
* [DEV-1307] remove couch db deletion ([#755](https://github.com/nclarity/backend/issues/755)) ([6c70c12](https://github.com/nclarity/backend/commit/6c70c1261de6e9ed37eea09a8ed796c775bc2dc5)), closes [/#diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL1-L7](https://github.com///issues/diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL1-L7) [/#diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL1-L7](https://github.com///issues/diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL1-L7) [/#diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL33-L192](https://github.com///issues/diff-7fc3ac7b43e23286f10c01024f0270fbc7b3a3dd8d64b28c98b6cab59cb3dfbcL33-L192) [/#diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L1-L4](https://github.com///issues/diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L1-L4) [/#diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L1-L4](https://github.com///issues/diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L1-L4) [/#diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L30-L37](https://github.com///issues/diff-38f80db65f1b28d84ab343710eb6c10a6b24ae8808c130fa59be7b6f9b9edfc5L30-L37)
* [DEV-1320] improve aaon mapping and device profile ([#761](https://github.com/nclarity/backend/issues/761)) ([24343b5](https://github.com/nclarity/backend/commit/24343b5a85dcaca6d198e71cfe205683eee2f42f))
* [DEV-1325] improve device addition/update on equipment ([#758](https://github.com/nclarity/backend/issues/758)) ([3528356](https://github.com/nclarity/backend/commit/3528356b39916a2173f04e50175ca39bcb53115d))
* [DEV-1329] add status record update ([8c9fed4](https://github.com/nclarity/backend/commit/8c9fed4b62716b4c53fa5019c8d99d5b764bf0dc))
* [DEV-1340]ensure systemProfile is properly parsed and included in update payload ([#759](https://github.com/nclarity/backend/issues/759)) ([a845c56](https://github.com/nclarity/backend/commit/a845c56712c5b1eb61aaf91442992646b38f2f6b))
* [DEV-1346] include system profile data in building equipment query ([#760](https://github.com/nclarity/backend/issues/760)) ([01158d6](https://github.com/nclarity/backend/commit/01158d6f7e89b58bbf9f2c93825218369a8105f2))
* add missing await keyword when fetching equipment by ID ([#763](https://github.com/nclarity/backend/issues/763)) ([120cd4e](https://github.com/nclarity/backend/commit/120cd4eb8c72ece4f15ba2aeb2802e2034243f21))
* remove casting for circuit in aaon mapper ([f00f7eb](https://github.com/nclarity/backend/commit/f00f7ebea4769d8a036e39ba850006e8d36b02ae))


### Features

* [DEV-1280] Create use case to create an equipment for Chiller systems ([#753](https://github.com/nclarity/backend/issues/753)) ([54c2055](https://github.com/nclarity/backend/commit/54c205559c67d1de80c9187892ced15bc9ffdfa9)), closes [/#diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8R1-R172](https://github.com///issues/diff-bbbdbb1ecb682e9d0e5b51dbbcb87a6087e779517cdebdf4df4f1c272d56efd8R1-R172) [/#diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45R1-R115](https://github.com///issues/diff-e8ca09d4a70922e061d7903842ec8eeef51dffa8a7288d5eae064217e0628b45R1-R115) [/#diff-fc97ac72df7feb38cc014b1f27f34e7eb7c84cb6449d42583058bac0d9e352c9R1-R2](https://github.com///issues/diff-fc97ac72df7feb38cc014b1f27f34e7eb7c84cb6449d42583058bac0d9e352c9R1-R2) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R182-R204](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R182-R204) [/#diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aR290-R348](https://github.com///issues/diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aR290-R348) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L229-R262](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L229-R262) [/#diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aL220-R230](https://github.com///issues/diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aL220-R230) [/#diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9R69](https://github.com///issues/diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9R69) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR93-R95](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR93-R95) [/#diff-7bf81e620c965504f24c5680a7fb28e7de0b3174234f60f37e7df972a12df77dR51-R53](https://github.com///issues/diff-7bf81e620c965504f24c5680a7fb28e7de0b3174234f60f37e7df972a12df77dR51-R53)

# [2.43.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.42.0...@nclarity/contracts-v2.43.0) (2025-04-29)


### Features

* [DEV-1278] add EquipmentType enum and update Equipment model to include type field ([#748](https://github.com/nclarity/backend/issues/748)) ([8cd1bd3](https://github.com/nclarity/backend/commit/8cd1bd3b386759cddfeb15a35d8cd94acd348e46)), closes [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR688) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR875-R879](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR875-R879) [/#diff-fc97ac72df7feb38cc014b1f27f34e7eb7c84cb6449d42583058bac0d9e352c9R1-R2](https://github.com///issues/diff-fc97ac72df7feb38cc014b1f27f34e7eb7c84cb6449d42583058bac0d9e352c9R1-R2) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R118-R122](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R118-R122) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R118-R122](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R118-R122) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L257-R263](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L257-R263) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R281-R291](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R281-R291)
* improve equipment profile creation ([1e7c9bc](https://github.com/nclarity/backend/commit/1e7c9bcec6d14df0dadef70189cd7d85afd9c4fb))

# [2.42.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.41.0...@nclarity/contracts-v2.42.0) (2025-04-25)


### Bug Fixes

* [DEV-1307] enhance deleteUserUseCase to remove user permissions before deletion ([#744](https://github.com/nclarity/backend/issues/744)) ([0690aa6](https://github.com/nclarity/backend/commit/0690aa6675d8ed229f9cb62cb5950b2de152c8e4))
* [DEV-1312] enhance alert processing with additional debug logging and status handling ([#746](https://github.com/nclarity/backend/issues/746)) ([41a1a12](https://github.com/nclarity/backend/commit/41a1a1246455fe47d72fbd9eb87b7f20a598a429)), closes [/#diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adR17-R18](https://github.com///issues/diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adR17-R18) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R44-R47](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R44-R47) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R116-R119](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R116-R119) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L78-R95](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L78-R95) [/#diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL157-R162](https://github.com///issues/diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL157-R162)
* add env variables for posthog in api ([e9ab7ff](https://github.com/nclarity/backend/commit/e9ab7ff0005c34be994784c8c1d03f59ae1e86a5))
* **api:** [DEV-1267] Three dots need to be removed & Timestamp is incorrect ([#736](https://github.com/nclarity/backend/issues/736)) ([21bafe7](https://github.com/nclarity/backend/commit/21bafe783b02f8fb2883d5ae8192dbd75823ec6e)), closes [/#diff-d105830cf5459f5f1bc2b9e53d1bf34ff411dc3cab570602fb68483dea4b2ce8L18-R18](https://github.com///issues/diff-d105830cf5459f5f1bc2b9e53d1bf34ff411dc3cab570602fb68483dea4b2ce8L18-R18) [/#diff-d105830cf5459f5f1bc2b9e53d1bf34ff411dc3cab570602fb68483dea4b2ce8L33-R33](https://github.com///issues/diff-d105830cf5459f5f1bc2b9e53d1bf34ff411dc3cab570602fb68483dea4b2ce8L33-R33) [/#diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42](https://github.com///issues/diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42) [/#diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42](https://github.com///issues/diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42) [/#diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42](https://github.com///issues/diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L10-R42)
* change equipment profile field in listing ([#749](https://github.com/nclarity/backend/issues/749)) ([37486b9](https://github.com/nclarity/backend/commit/37486b997d4b94acbe4ca726d3733b377f1561c4)), closes [/#diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L66-R78](https://github.com///issues/diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L66-R78) [/#diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L66-R78](https://github.com///issues/diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L66-R78) [/#diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L82-R92](https://github.com///issues/diff-24ae44c868ea88f0dd26a50adb3fbc0a892e7f1f01022a8dfc5e61dcc9193991L82-R92)


### Features

* [DEV-1223] Update database exporter to use equipment profile from Mysql ([#742](https://github.com/nclarity/backend/issues/742)) ([b9f9ae1](https://github.com/nclarity/backend/commit/b9f9ae1e4f8f8c7f0e479d308a29560fba5fceab)), closes [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L2-R2](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L2-R2) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL7-R7](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL7-R7) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL41-R45](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL41-R45) [/#diff-97fc5d0a93b5d19d6779dad01ca282a2f4697857996466057241dece62b85dd4R46-R56](https://github.com///issues/diff-97fc5d0a93b5d19d6779dad01ca282a2f4697857996466057241dece62b85dd4R46-R56) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL141-R154](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL141-R154) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL317-R308](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL317-R308)
* [DEV-1225] Update trigger workflow (for notifications) ([#743](https://github.com/nclarity/backend/issues/743)) ([915da8f](https://github.com/nclarity/backend/commit/915da8fbd50a243554ca37f69e5d29ba7c3ca66c)), closes [/#diff-38334ed99675b0b92d944709b214218a47625630e3f45ca2f630e24be82b5adcL9-R11](https://github.com///issues/diff-38334ed99675b0b92d944709b214218a47625630e3f45ca2f630e24be82b5adcL9-R11) [/#diff-dcf7372b8745241d9656d371ef85aa589b94baa49b9c211d1bd61dad44c44d46L44-R44](https://github.com///issues/diff-dcf7372b8745241d9656d371ef85aa589b94baa49b9c211d1bd61dad44c44d46L44-R44) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L24-R25](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L24-R25) [/#diff-38334ed99675b0b92d944709b214218a47625630e3f45ca2f630e24be82b5adcL31-R35](https://github.com///issues/diff-38334ed99675b0b92d944709b214218a47625630e3f45ca2f630e24be82b5adcL31-R35) [/#diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbL113-R118](https://github.com///issues/diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbL113-R118) [/#diff-57d3ff52b35499f8cdfcacb3136d27a3fd46036b338b89af9ca94957b2eb1bf6L15-R17](https://github.com///issues/diff-57d3ff52b35499f8cdfcacb3136d27a3fd46036b338b89af9ca94957b2eb1bf6L15-R17) [/#diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R14](https://github.com///issues/diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R14) [/#diff-813ebe81abb9d7b1375bdb1142da49b39c2ce1d678ce6a25ef9a32340412d694L7-R7](https://github.com///issues/diff-813ebe81abb9d7b1375bdb1142da49b39c2ce1d678ce6a25ef9a32340412d694L7-R7) [/#diff-6445167f5b2928ad7e158230aaccb9a77d971fa2eb803dfd41baf6d2582b8ee0L7-R7](https://github.com///issues/diff-6445167f5b2928ad7e158230aaccb9a77d971fa2eb803dfd41baf6d2582b8ee0L7-R7) [/#diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL9-R9](https://github.com///issues/diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL9-R9) [/#diff-5319c07eb565940c43fb0f77302b48e63629d12f8b78ec78ea4cdccf1d95e9a4L4-R4](https://github.com///issues/diff-5319c07eb565940c43fb0f77302b48e63629d12f8b78ec78ea4cdccf1d95e9a4L4-R4) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL202-R195](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL202-R195) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL379-R378](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL379-R378) [/#diff-57d3ff52b35499f8cdfcacb3136d27a3fd46036b338b89af9ca94957b2eb1bf6L15-R17](https://github.com///issues/diff-57d3ff52b35499f8cdfcacb3136d27a3fd46036b338b89af9ca94957b2eb1bf6L15-R17) [/#diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R14](https://github.com///issues/diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R14)
* [DEV-1227] endpoint to fetch equipment profile ([52d2a42](https://github.com/nclarity/backend/commit/52d2a425b06386712c610bc668c258078674d80a))
* [DEV-1253] add v2 use case for updating equipment profiles ([#741](https://github.com/nclarity/backend/issues/741)) ([7e6396c](https://github.com/nclarity/backend/commit/7e6396c6b87a5bd5ed2266ddc61f9464a4901fb0))
* [DEV-1254] update endpoint to setup a profile ([#738](https://github.com/nclarity/backend/issues/738)) ([1108e1d](https://github.com/nclarity/backend/commit/1108e1d774cd1f14ffb8d27688084ec04c1b17d4))
* [DEV-1264] Update endpoint to fetch the system profiles used in the equipment edit page  ([#739](https://github.com/nclarity/backend/issues/739)) ([b29681b](https://github.com/nclarity/backend/commit/b29681bda2c2e344ca47100ff8125c322a50ebde)), closes [/#diff-0cd9429adcba89a60db3b78a61de1baefea1cf1d54a17425c24beb62c1f4751cR2-R38](https://github.com///issues/diff-0cd9429adcba89a60db3b78a61de1baefea1cf1d54a17425c24beb62c1f4751cR2-R38) [/#diff-4bfe7de1033198d7582d6501b50d3fcb109ac124a9ff1e831a287323375d8399R1-R17](https://github.com///issues/diff-4bfe7de1033198d7582d6501b50d3fcb109ac124a9ff1e831a287323375d8399R1-R17)
* [DEV-1265] add v2 use cases and controllers for fetching refrigerants and refrigerant by ID ([#740](https://github.com/nclarity/backend/issues/740)) ([07c8531](https://github.com/nclarity/backend/commit/07c8531b99341379fc79bbb4a8ca40a3a26c3a44)), closes [/#diff-d664586474f09c5b952be67ff719e9238be7e5dc9cd6220fa53f941a73a24b4fR2-R31](https://github.com///issues/diff-d664586474f09c5b952be67ff719e9238be7e5dc9cd6220fa53f941a73a24b4fR2-R31) [/#diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R5-R9](https://github.com///issues/diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R5-R9) [/#diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R5-R9](https://github.com///issues/diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R5-R9) [/#diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R21-R45](https://github.com///issues/diff-9f2b24822bac3cb8f8b5d712bec3a2cfa978cc3480e62fe9ff2e6c0ddd6f0c26R21-R45) [/#diff-8d57a04289ef0d424619b4aa56230d983e030a104d30cc5d8438883d557c697bR1-R16](https://github.com///issues/diff-8d57a04289ef0d424619b4aa56230d983e030a104d30cc5d8438883d557c697bR1-R16) [/#diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R13](https://github.com///issues/diff-ee2b328f15f0d247279c132c77dbcfd5c10696e23502ca8d1b3f52f73d8ddb99R1-R13)
* add contracts exports ([#752](https://github.com/nclarity/backend/issues/752)) ([f078584](https://github.com/nclarity/backend/commit/f0785844d34a4f5f8ec904d09dfbbde8b2fbecc4)), closes [/#diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aR1-R293](https://github.com///issues/diff-a578e06991f4d4b71676a9ff412b700b72ba0723e625b3f84ac8337f881a4e5aR1-R293) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L19-L276](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L19-L276) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L6-L8](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398L6-L8) [/#diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R441-R442](https://github.com///issues/diff-175c7d3613506457df11d2c8d8917351ffbfe39e843d08205db5466ff01c8398R441-R442) [/#diff-38fce4a3075bad7be4e2f94f0faea7f2e34c5a38cf590112b685df5607229e50R23-R24](https://github.com///issues/diff-38fce4a3075bad7be4e2f94f0faea7f2e34c5a38cf590112b685df5607229e50R23-R24) [/#diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068R69](https://github.com///issues/diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068R69) [/#diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156L42-R42](https://github.com///issues/diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156L42-R42)
* add v2 controller and use case for deleting equipment ([#737](https://github.com/nclarity/backend/issues/737)) ([8f3710e](https://github.com/nclarity/backend/commit/8f3710e6f22710a0f9ef4a4d4d6840ef3c9a28e5)), closes [/#diff-5b1338848718634fa9620220149ab5acc4e5975d6dcbb675c926a14b01c622b3R1-R34](https://github.com///issues/diff-5b1338848718634fa9620220149ab5acc4e5975d6dcbb675c926a14b01c622b3R1-R34) [/#diff-d558515f107bab63c579e759a22b13ef8ea7d3a82d3c431f16ee06a14168858eR1-R63](https://github.com///issues/diff-d558515f107bab63c579e759a22b13ef8ea7d3a82d3c431f16ee06a14168858eR1-R63) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL4-R6](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL4-R6) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL4-R6](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aL4-R6) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aR24-R25](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aR24-R25) [/#diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aR78](https://github.com///issues/diff-70d5f082af901f44cf01b94d3042e3245060a252532afa06d00867c257cb408aR78) [/#diff-faca3c2ffc1044d8be86ead0a855b121fe9820a2d43484099eebb15b5d00b7a6R13-R18](https://github.com///issues/diff-faca3c2ffc1044d8be86ead0a855b121fe9820a2d43484099eebb15b5d00b7a6R13-R18)
* **api:**  [DEV-1262] add v2 use case for listing equipment by corporate ID with pagination support ([#745](https://github.com/nclarity/backend/issues/745)) ([5f3cdcd](https://github.com/nclarity/backend/commit/5f3cdcdd1a91b488eaa216bebee5922061de578e))
* changed response for update equipment profile ([5f2c2e4](https://github.com/nclarity/backend/commit/5f2c2e4d0665b2a02e35bbf3d1eae430cfb3d868))
* renamed profile to equipment profile for list ([ea718ca](https://github.com/nclarity/backend/commit/ea718ca160a5276e826cc57a108c5305260f2dc1))

# [2.41.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.40.0...@nclarity/contracts-v2.41.0) (2025-04-09)


### Bug Fixes

* [DEV-1184] redirect url changed ([#731](https://github.com/nclarity/backend/issues/731)) ([3d5b11c](https://github.com/nclarity/backend/commit/3d5b11cdb33885b69c7a5db993edceea88382770))
* [DEV-1267] enhance equipment CBM status retrieval with building ID and improve relapse handling ([#734](https://github.com/nclarity/backend/issues/734)) ([b28ec00](https://github.com/nclarity/backend/commit/b28ec0047ba1f7a518c7c1f75f3c05e73c8d7da7)), closes [/#diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR40](https://github.com///issues/diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR40) [/#diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L20-R40](https://github.com///issues/diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L20-R40) [/#diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L20-R40](https://github.com///issues/diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L20-R40) [/#diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L47-R55](https://github.com///issues/diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94L47-R55) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL7-R10](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL7-R10) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL7-R10](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL7-R10) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL20-R49](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL20-R49) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL47-R62](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbL47-R62)
* change equipment assignment to insight rule ([345c818](https://github.com/nclarity/backend/commit/345c818dfd745923d242d2ad19ef5fc239545b08))


### Features

* [DEV-1226] add equipment profiles in mysql ([#733](https://github.com/nclarity/backend/issues/733)) ([43f82a1](https://github.com/nclarity/backend/commit/43f82a16aadd277020f5e8957488ee2278c5c63a)), closes [/#diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L7-R7](https://github.com///issues/diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L7-R7) [/#diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L7-R7](https://github.com///issues/diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L7-R7) [/#diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L30-R30](https://github.com///issues/diff-6ed41395223eed5cce4a751b774ec6352806a51c1fcf9228cc952271a9a8dcb4L30-R30) [/#diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL1-R1](https://github.com///issues/diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL1-R1) [/#diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL1-R1](https://github.com///issues/diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL1-R1) [/#diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL12-R12](https://github.com///issues/diff-f09cbc1bb49cf50a50c4697984cd86b8394403a25408da7bd6506cc6b3ee567aL12-R12) [/#diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34R4-L5](https://github.com///issues/diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34R4-L5) [/#diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34R4-L5](https://github.com///issues/diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34R4-L5) [/#diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34L40-R40](https://github.com///issues/diff-5619e5bb53e289d42168a8a979aef1f53c37df213b216c19fa26572c6069bf34L40-R40) [/#diff-fb48f8c4af2376fd6de29e4e1431807997b42f59ba1941f49833bd5719ba13b9L6-R10](https://github.com///issues/diff-fb48f8c4af2376fd6de29e4e1431807997b42f59ba1941f49833bd5719ba13b9L6-R10) [/#diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R1-R17](https://github.com///issues/diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R1-R17) [/#diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R1-R17](https://github.com///issues/diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R1-R17) [/#diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R26-R58](https://github.com///issues/diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156R26-R58) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L6-R7](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L6-R7) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L6-R7](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L6-R7) [/#diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L37-R39](https://github.com///issues/diff-4287e255edfce0ff0c2ec670eac719c101e20991040260a92e320a301c7a50c2L37-R39) [/#diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR1-R193](https://github.com///issues/diff-6030c29b740687c11aeef77d22de58056d77060053c091040d72e9d55c37551dR1-R193) [/#diff-33e5dff61dd9fcec62e115997ed9a0cdd72d228f62f9b53627dcd3d93108870fR1-R123](https://github.com///issues/diff-33e5dff61dd9fcec62e115997ed9a0cdd72d228f62f9b53627dcd3d93108870fR1-R123)
* **api:** [DEV-1234] implement device deletion with IoT Central integration ([#732](https://github.com/nclarity/backend/issues/732)) ([a9be2d7](https://github.com/nclarity/backend/commit/a9be2d7d496db641d49c77ea49177a491cc63714))
* improve cbm alerts and trendy tracking ([426bc58](https://github.com/nclarity/backend/commit/426bc587b6d190de4d45abcb54971f64de35839a))

# [2.40.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.39.0...@nclarity/contracts-v2.40.0) (2025-04-05)


### Bug Fixes

* [DEV-1243] CBM | Multiple issues ([#728](https://github.com/nclarity/backend/issues/728)) ([b46a105](https://github.com/nclarity/backend/commit/b46a1055c0679b564280552f3d1b178c5cf8fa6e)), closes [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R5](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R5) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R82-R88](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R82-R88) [/#diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL12-R12](https://github.com///issues/diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL12-R12) [/#diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL43-R68](https://github.com///issues/diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL43-R68)
* **api:** [DEV-1245]add building and equipment association logic in createRuleUseCase ([#729](https://github.com/nclarity/backend/issues/729)) ([5311757](https://github.com/nclarity/backend/commit/5311757e9cfbc03c9ed4be0d48b47a91df08d223)), closes [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR1](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR1) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR24-R45](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR24-R45) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR24-R45](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR24-R45) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR155-R192](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbR155-R192)


### Features

* change building to insight relationship ([#730](https://github.com/nclarity/backend/issues/730)) ([b3590e2](https://github.com/nclarity/backend/commit/b3590e2dc8935c6aa271aeba5a0387c48370c45d)), closes [/#diff-908b41da0227ba6d5dbad7ab2c0a77e6dd132a2849590a3ca1a596b8941de6d4R1-R18](https://github.com///issues/diff-908b41da0227ba6d5dbad7ab2c0a77e6dd132a2849590a3ca1a596b8941de6d4R1-R18) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR98](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR98) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL107](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL107) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL450](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL450) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL501-L514](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dL501-L514) [/#diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL25-R32](https://github.com///issues/diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL25-R32) [/#diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL25-R32](https://github.com///issues/diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL25-R32) [/#diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL60-R62](https://github.com///issues/diff-85d7fbc1a8b3f9de645ec9ca8e77ed5f47bffff26ac2823873e30f483d55f24dL60-R62) [/#diff-87ec41a9b99905cc3e1b8057f37e18073c14f6875611c57e0918311be08d9955L6-R15](https://github.com///issues/diff-87ec41a9b99905cc3e1b8057f37e18073c14f6875611c57e0918311be08d9955L6-R15) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL23-R32](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL23-R32) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL23-R32](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL23-R32) [/#diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL155-L166](https://github.com///issues/diff-7e634404a36d4eaeaf153b3ee4caa94eb19a347411f91d3fe03ab19aff6aa1bbL155-L166) [/#diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068L31](https://github.com///issues/diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068L31) [/#diff-bafd3de02c26cf3ed9fee31f1b48666cd273b188552b71727537f26e008f9ed0L98](https://github.com///issues/diff-bafd3de02c26cf3ed9fee31f1b48666cd273b188552b71727537f26e008f9ed0L98) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L123-L132](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L123-L132) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L145-R142](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L145-R142)

# [2.39.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.38.0...@nclarity/contracts-v2.39.0) (2025-04-02)


### Features

* **api:** [DEV-749] add public password reset functionality with email validation ([#724](https://github.com/nclarity/backend/issues/724)) ([7bf068a](https://github.com/nclarity/backend/commit/7bf068ae2f87aeec93e216e08071583f5548a86e)), closes [/#diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R12](https://github.com///issues/diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R12) [/#diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R12](https://github.com///issues/diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R12) [/#diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R47-R50](https://github.com///issues/diff-21c8841c0e713d811b15f17a5b4773cfe27bc099d2d54c9a285c91dca93d2613R47-R50) [/#diff-657ddf7bdbb2ad1a1c2286fb2eeff59a5da1aa97f45e0a9789cde28d42c1a238R1-R29](https://github.com///issues/diff-657ddf7bdbb2ad1a1c2286fb2eeff59a5da1aa97f45e0a9789cde28d42c1a238R1-R29) [/#diff-0ef4ec0bd061835f828192cb531efc42eb61a699ce35ce50f49778552911094bR1-R54](https://github.com///issues/diff-0ef4ec0bd061835f828192cb531efc42eb61a699ce35ce50f49778552911094bR1-R54)
* **api:** [DEV-997] add getCBMAlert functionality to retrieve single CBM alert by insightRuleId ([#726](https://github.com/nclarity/backend/issues/726)) ([6d97db3](https://github.com/nclarity/backend/commit/6d97db393d546adc1bb2300b079b6c752a317932)), closes [/#diff-50d0e02b9033ac0d5f8674df3b9f86febb0d1dd1f8a75abc3173974c38967f7eR1-R32](https://github.com///issues/diff-50d0e02b9033ac0d5f8674df3b9f86febb0d1dd1f8a75abc3173974c38967f7eR1-R32) [/#diff-9dd55d79044f617b618a39186c335b1b3a7723113b54c3c7d40f6834ca154953R1-R17](https://github.com///issues/diff-9dd55d79044f617b618a39186c335b1b3a7723113b54c3c7d40f6834ca154953R1-R17) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R16](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R16) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R16](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R16) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R38-R42](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R38-R42) [/#diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL21-R21](https://github.com///issues/diff-0348630d8ed79aba328aeed294d0ec543504798a7307c34b9987bb2fa09b488aL21-R21) [/#diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L12-R12](https://github.com///issues/diff-45ae65dc03e966e170fde98a28a00a84515ee8621f513b16f0824c2b72267fa8L12-R12)
* trendy insights improvements ([#725](https://github.com/nclarity/backend/issues/725)) ([69e9f78](https://github.com/nclarity/backend/commit/69e9f78415b16118eb75457dfb94e681d9f0adf5)), closes [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L25-R125](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L25-R125) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L25-R125](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L25-R125) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R173-R206](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R173-R206) [/#diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08L1-L102](https://github.com///issues/diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08L1-L102) [/#diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033L1-L120](https://github.com///issues/diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033L1-L120) [/#diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL1-R18](https://github.com///issues/diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL1-R18) [/#diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL1-R18](https://github.com///issues/diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL1-R18) [/#diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL20-R30](https://github.com///issues/diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL20-R30) [/#diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL98-R225](https://github.com///issues/diff-b4326764b84aa94e9a53aa784a86aa5c69ed2888d78f740c2546d223ad9ff5afL98-R225) [/#diff-c68b70f4f3e7747311d6670ba0251da89146fbca2e9feeb503a2a2f0253c72f7L96-L119](https://github.com///issues/diff-c68b70f4f3e7747311d6670ba0251da89146fbca2e9feeb503a2a2f0253c72f7L96-L119) [/#diff-a9c43f51e502707318caee9a2498169e99fef89192b2475de319946ec544b332L88-L111](https://github.com///issues/diff-a9c43f51e502707318caee9a2498169e99fef89192b2475de319946ec544b332L88-L111)

# [2.38.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.37.0...@nclarity/contracts-v2.38.0) (2025-03-26)


### Features

* **api:** [DEV-1217] add CBMHistory and CBMHistoryRelapse types to transactional exports ([#723](https://github.com/nclarity/backend/issues/723)) ([200702a](https://github.com/nclarity/backend/commit/200702a417d06334b9f89710cb3c206db6262f67)), closes [/#diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068R32-R33](https://github.com///issues/diff-a08045956ec567467c6b527e85f3e65e3a57878275dbc34f756183029af08068R32-R33) [/#diff-bafd3de02c26cf3ed9fee31f1b48666cd273b188552b71727537f26e008f9ed0R101-R102](https://github.com///issues/diff-bafd3de02c26cf3ed9fee31f1b48666cd273b188552b71727537f26e008f9ed0R101-R102)

# [2.37.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.36.0...@nclarity/contracts-v2.37.0) (2025-03-26)


### Bug Fixes

* [DEV-1189] improve use case for building condition ([#714](https://github.com/nclarity/backend/issues/714)) ([984714f](https://github.com/nclarity/backend/commit/984714f3ba872a1dbba5cfec33f0d3b198667d80))
* [DEV-1210] unable to save insights ([#710](https://github.com/nclarity/backend/issues/710)) ([6716078](https://github.com/nclarity/backend/commit/671607871edb8851417c7f788622dfbf6ccb7d66))
* [DEV-1212] resolve alert default to alert time ([#722](https://github.com/nclarity/backend/issues/722)) ([6a93f0f](https://github.com/nclarity/backend/commit/6a93f0fe90d147181bdb705b8ba371a700653a0c)), closes [/#diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dR13](https://github.com///issues/diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dR13) [/#diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL95-R121](https://github.com///issues/diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL95-R121)
* [DEV-574] fine tune target calculations ([#712](https://github.com/nclarity/backend/issues/712)) ([54708b4](https://github.com/nclarity/backend/commit/54708b43ca1d871197568d5bbeef9316bf44659d)), closes [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R83-R85](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R83-R85) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R83-R85](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R83-R85) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R142-R144](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R142-R144) [/#diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbL52-R53](https://github.com///issues/diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbL52-R53) [/#diff-76be8f079d1555c29f72cd2d29f1666d6741744aa1d0cf446f38e8c3974b934eL19-R19](https://github.com///issues/diff-76be8f079d1555c29f72cd2d29f1666d6741744aa1d0cf446f38e8c3974b934eL19-R19) [/#diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L167-R184](https://github.com///issues/diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L167-R184) [/#diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL172-R190](https://github.com///issues/diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL172-R190)
* **api:** [DEV-1209] Insight false flag ([#718](https://github.com/nclarity/backend/issues/718)) ([4af5ef7](https://github.com/nclarity/backend/commit/4af5ef7d8189fb823e16be90b62e8a27e96dfe58)), closes [/#diff-f50026b76da564bd778946ca1bf30990f6f92c86e976eb78f28e6c2fa2f77bd4R204-R210](https://github.com///issues/diff-f50026b76da564bd778946ca1bf30990f6f92c86e976eb78f28e6c2fa2f77bd4R204-R210)
* **api:** [DEV-1217] ensure transaction execution is awaited in deleteMultipleItems ([#721](https://github.com/nclarity/backend/issues/721)) ([33e724e](https://github.com/nclarity/backend/commit/33e724ef84417835b99b5d812cabfa21c3cc69b0)), closes [/#diff-c5a0c2cd1a09a5c24829ec49b1dbc825af9da639d54d477209fefe21c3c82570L18-R18](https://github.com///issues/diff-c5a0c2cd1a09a5c24829ec49b1dbc825af9da639d54d477209fefe21c3c82570L18-R18)


### Features

* [DEV-975] Create API endpoint to retrieve building CBM condition status ([#711](https://github.com/nclarity/backend/issues/711)) ([1598bcf](https://github.com/nclarity/backend/commit/1598bcf0589eb72814b0c4749fd02d0e19f811b1)), closes [/#diff-febdd370bff72a24253177ef8d8fd68e653d9dcb5c54809a59f157a9587124c5R1-R41](https://github.com///issues/diff-febdd370bff72a24253177ef8d8fd68e653d9dcb5c54809a59f157a9587124c5R1-R41) [/#diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR1-R49](https://github.com///issues/diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR1-R49) [/#diff-63b6f7df596975418ada7274f920686a80189e66ae2e6fa9a0e76e57b70a2bb9R1-R15](https://github.com///issues/diff-63b6f7df596975418ada7274f920686a80189e66ae2e6fa9a0e76e57b70a2bb9R1-R15) [/#diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94R1-R82](https://github.com///issues/diff-b60277d56cf585a6e5178ce6c23b825a867069810625d1870367f1d44932ac94R1-R82) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbR1-R82](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbR1-R82) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R60](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R60) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R132](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R132) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R58-R64](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R58-R64) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95R55](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95R55)
* **api:**  [DEV-976] Create API endpoint to retrieve equipment CBM condition status ([#709](https://github.com/nclarity/backend/issues/709)) ([015aa94](https://github.com/nclarity/backend/commit/015aa94eda6f9e0d08f92275326fbfe8eed49a0d)), closes [/#diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR1-R43](https://github.com///issues/diff-745208eb0514f45cf01d28e6a76fef5401bfc20e7d06cd63a0ea7e4d4e08107dR1-R43) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R10](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R10) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R10](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R10) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R52-R58](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R52-R58) [/#diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbR1-R82](https://github.com///issues/diff-c4455a8ff1dcb609e369e692bda482be257f752b73d1db043fc69680038e32dbR1-R82) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R5) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R60](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R60) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R132](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R132) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95L42) [/#diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95R55](https://github.com///issues/diff-a591855f02113471c6fd019ed7f8879576955202db40596257a42f15f4605f95R55) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R58-R64](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R58-R64)
* **api:** [DEV-987] add endpoint to resolve CBM alerts per building ([#713](https://github.com/nclarity/backend/issues/713)) ([1d8c7b7](https://github.com/nclarity/backend/commit/1d8c7b754b0d1f9620196126be4d79c9160f7ad2)), closes [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R62-R68](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R62-R68) [/#diff-266ab6de5b19e7b052433faf707b28140321f2862c51bbd651928b9046712d28R1-R34](https://github.com///issues/diff-266ab6de5b19e7b052433faf707b28140321f2862c51bbd651928b9046712d28R1-R34) [/#diff-a937fb8fa2936647a195daa23078a668af3f7e65b6759b78e843b7a83b3abe52R1-R24](https://github.com///issues/diff-a937fb8fa2936647a195daa23078a668af3f7e65b6759b78e843b7a83b3abe52R1-R24)
* **api:** [DEV-988] Create endpoint to review all insights at building level ([#715](https://github.com/nclarity/backend/issues/715)) ([14045f9](https://github.com/nclarity/backend/commit/14045f9a24c3bf8c868a7e6bc713f00e8e84d796)), closes [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R12) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R62-R68](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R62-R68) [/#diff-2890c4a46b4a8fdc9e827494a1dded09c64612867c329963b2bef5f03aa25df5R1-R41](https://github.com///issues/diff-2890c4a46b4a8fdc9e827494a1dded09c64612867c329963b2bef5f03aa25df5R1-R41) [/#diff-8e517d45ac9c1c535fd3acd1b8e15e3b45fe956f482909c3bee800ca0fc48162R1-R18](https://github.com///issues/diff-8e517d45ac9c1c535fd3acd1b8e15e3b45fe956f482909c3bee800ca0fc48162R1-R18)
* **api:** [DEV-989] Create endpoint to resolve all insights at equipment level ([#716](https://github.com/nclarity/backend/issues/716)) ([9ad1806](https://github.com/nclarity/backend/commit/9ad1806eac5adfc3d4f1410f8fbcd6d56ac3725f)), closes [/#diff-9239f80b3e9c57a9043f55caf3135549c186e48b4fe3678e2c2804b8d0a1324eR1-R40](https://github.com///issues/diff-9239f80b3e9c57a9043f55caf3135549c186e48b4fe3678e2c2804b8d0a1324eR1-R40) [/#diff-3d4cbebab339e01071f95c3ac21bb943e04c717cd2bd5801808fc2b60d64f22bR1-R43](https://github.com///issues/diff-3d4cbebab339e01071f95c3ac21bb943e04c717cd2bd5801808fc2b60d64f22bR1-R43) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R13](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R13) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R13](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R13) [/#diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R69](https://github.com///issues/diff-7ca6d69111cad748f5dc94e544acb850a397c07b8aaa63ebfd89a6818e64eea7R69) [/#diff-db4d9849bdc22674c92b137429ab66d5676821607a09137e04d6ddd9e4b75cf8R1-R35](https://github.com///issues/diff-db4d9849bdc22674c92b137429ab66d5676821607a09137e04d6ddd9e4b75cf8R1-R35)
* **api:** [DEV-990] Create endpoint to review all insights at equipment level ([#717](https://github.com/nclarity/backend/issues/717)) ([0aac737](https://github.com/nclarity/backend/commit/0aac737f8bca49ea8b48228a9a3b4a6199f5751c))

# [2.36.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.35.0...@nclarity/contracts-v2.36.0) (2025-03-18)


### Bug Fixes

* [DEV-1180] improve trendy insights ([#691](https://github.com/nclarity/backend/issues/691)) ([8ceaee1](https://github.com/nclarity/backend/commit/8ceaee197308e299dc5c86b08784394e13d1eab7)), closes [/#diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L42-R42](https://github.com///issues/diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L42-R42) [/#diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9L71-R71](https://github.com///issues/diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9L71-R71) [/#diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08R1-R102](https://github.com///issues/diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08R1-R102) [/#diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033R1-R120](https://github.com///issues/diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033R1-R120) [/#diff-6f1ceed6b5955c1d7ea7d388fcc43d6a44f90bfb30a4376d70a6cdf0b38c1d8eR1-R112](https://github.com///issues/diff-6f1ceed6b5955c1d7ea7d388fcc43d6a44f90bfb30a4376d70a6cdf0b38c1d8eR1-R112) [/#diff-b8373c012a9e68affaee672c95542d3ca2a5ba6410bf31a71f91f0121a61a90aL7-R8](https://github.com///issues/diff-b8373c012a9e68affaee672c95542d3ca2a5ba6410bf31a71f91f0121a61a90aL7-R8) [/#diff-f892f27bd0a669f1023a0f3aa0df7a13a8e6f07f0389fb02454169000f9adbfbL22-R22](https://github.com///issues/diff-f892f27bd0a669f1023a0f3aa0df7a13a8e6f07f0389fb02454169000f9adbfbL22-R22) [/#diff-9861b8af44b174a37dd3f351e2036d34b3d06781a5a1a26756f30664b2822427L15-R15](https://github.com///issues/diff-9861b8af44b174a37dd3f351e2036d34b3d06781a5a1a26756f30664b2822427L15-R15) [/#diff-9861b8af44b174a37dd3f351e2036d34b3d06781a5a1a26756f30664b2822427L24-R24](https://github.com///issues/diff-9861b8af44b174a37dd3f351e2036d34b3d06781a5a1a26756f30664b2822427L24-R24) [/#diff-f8b0414fa8bddf5fce8accb9305c1132ed01b4c844a0fe292267a301e61cfba4L34-R34](https://github.com///issues/diff-f8b0414fa8bddf5fce8accb9305c1132ed01b4c844a0fe292267a301e61cfba4L34-R34) [/#diff-1c6d65d55a1188da8a975021f3ad3c722888471ee62e2460983588f266e14b47L15-R15](https://github.com///issues/diff-1c6d65d55a1188da8a975021f3ad3c722888471ee62e2460983588f266e14b47L15-R15) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L74-R57](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L74-R57)
* [DEV-1182] Can't update insights ([#687](https://github.com/nclarity/backend/issues/687)) ([bae2889](https://github.com/nclarity/backend/commit/bae2889a85e055ed05629a482895eac4f7436c50))
* [DEV-574] Target Pressure Ranges for Gauges ([#692](https://github.com/nclarity/backend/issues/692)) ([c0523bb](https://github.com/nclarity/backend/commit/c0523bbf3b08f2e02f4ffa9247a3158694778fb0))
* [DEV-574] Target Pressure ranges for gauges ([#702](https://github.com/nclarity/backend/issues/702)) ([6c45887](https://github.com/nclarity/backend/commit/6c45887816de6a38436085ffda58635510d29b51)), closes [/#diff-6d58ee5487badbf6c5d55964a143702d2495c2a338c66ab1ac325279d66e79edR1-R52](https://github.com///issues/diff-6d58ee5487badbf6c5d55964a143702d2495c2a338c66ab1ac325279d66e79edR1-R52) [/#diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-L5](https://github.com///issues/diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-L5) [/#diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-L5](https://github.com///issues/diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-L5) [/#diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L19-R17](https://github.com///issues/diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L19-R17) [/#diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297R27](https://github.com///issues/diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297R27) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R5](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R5) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R5](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R5) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R43-R49](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R43-R49) [/#diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbR1-R165](https://github.com///issues/diff-6d150d6382934e0f0a81b175d78000ea33bcf5d0750bb6e3aeeddafb7d5db6fbR1-R165) [/#diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L164-R164](https://github.com///issues/diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L164-R164) [/#diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L164-R164](https://github.com///issues/diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L164-R164) [/#diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L180-L206](https://github.com///issues/diff-cc2616a58cc842243bfcbbcee160efbf4bea33398b9d02597db45bb5d4025e66L180-L206) [/#diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL171-R171](https://github.com///issues/diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL171-R171) [/#diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL171-R171](https://github.com///issues/diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL171-R171) [/#diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL185-R191](https://github.com///issues/diff-a321e48605f54013b8161492c3d96d7a5110b255e8bc99afa4126dd4214c8e5eL185-R191)
* [DEV-955] random data appears in insight history listing ([#688](https://github.com/nclarity/backend/issues/688)) ([d95aeb8](https://github.com/nclarity/backend/commit/d95aeb81f0802c57b074506a0d0a9a05df030ce5))
* add equipment for device config use case ([fb62150](https://github.com/nclarity/backend/commit/fb621507ad831c937bc4c46b67e92c2dbaf3d3a7))
* **api:** [DEV-1159] Update Insight button is not enabled after removing the cycle ([#703](https://github.com/nclarity/backend/issues/703)) ([069364a](https://github.com/nclarity/backend/commit/069364a0be2967ddd3cf9126cb5c4ddd621706c1))
* **api:** [DEV-1204] Getting error screen randomly on equipment detail page ([#705](https://github.com/nclarity/backend/issues/705)) ([54ee8b9](https://github.com/nclarity/backend/commit/54ee8b9fad4c6ed11f787755c4bcab2118a70498))
* change system profiles getter ([f0d5f5e](https://github.com/nclarity/backend/commit/f0d5f5e6e0ab2d6a176fcfbbf844e92beb03b4a9))
* DEV-1191 add resolution from mysql instead of mongo ([f208801](https://github.com/nclarity/backend/commit/f2088015341e85288b0d9b750486be0e359e58c6))
* improve trendy insights logic ([#689](https://github.com/nclarity/backend/issues/689)) ([453e3e6](https://github.com/nclarity/backend/commit/453e3e65acc8d950b42eac7c609d2c78c7c22a20)), closes [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL350-R361](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL350-R361) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL386-R387](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL386-R387) [/#diff-e27b999b75d03aef1117d7d9f9d97184fff41f7eec46cfa21c4e306c4f5214d2R1-R16](https://github.com///issues/diff-e27b999b75d03aef1117d7d9f9d97184fff41f7eec46cfa21c4e306c4f5214d2R1-R16) [/#diff-d28e4502de96d58c10050b4c56e9611f15d409724057ad3915adcf2980ab1a62R1-R22](https://github.com///issues/diff-d28e4502de96d58c10050b4c56e9611f15d409724057ad3915adcf2980ab1a62R1-R22) [/#diff-1d4e2fa153cd6006fe0c810955c5bc0b7767fb5b95204ca758e9e6be4c752a43R10-R16](https://github.com///issues/diff-1d4e2fa153cd6006fe0c810955c5bc0b7767fb5b95204ca758e9e6be4c752a43R10-R16) [/#diff-3350baafa3fee47e6dc90cb6fcc54c01d3b4b66df8b52dc64884e0872f4be2e1L13-R20](https://github.com///issues/diff-3350baafa3fee47e6dc90cb6fcc54c01d3b4b66df8b52dc64884e0872f4be2e1L13-R20)


### Features

* [DEV-1000] added new endpoint to retrieve relapses per CBM history entry ([#698](https://github.com/nclarity/backend/issues/698)) ([b201ab2](https://github.com/nclarity/backend/commit/b201ab210bce22c00b03f850a1952ee1c9a22b63))
* [DEV-1001] Create endpoint to retrieve CBM Alerts history entries per insight and equipment and building ([#693](https://github.com/nclarity/backend/issues/693)) ([bbd311c](https://github.com/nclarity/backend/commit/bbd311cbeb97d491fdb0e84f786190f057c6c601))
* [DEV-1002] Update buildings endpoint to allow filtering by enrolled buildings ([#697](https://github.com/nclarity/backend/issues/697)) ([adeec0e](https://github.com/nclarity/backend/commit/adeec0e3fdb41bad3a8e9b7749f3dfb0b740386e))
* [DEV-973] add CBM insights processing ([#696](https://github.com/nclarity/backend/issues/696)) ([caf3b48](https://github.com/nclarity/backend/commit/caf3b4843f5fef68f07995ff6119d79055a7af10)), closes [/#diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08R1-R102](https://github.com///issues/diff-c655f04943605db70c5083fff1c2b7babca301a4d866f0aa06e57d3a1f864c08R1-R102) [/#diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033R1-R120](https://github.com///issues/diff-859d05926609478bdac53521a30507085e690fd6db5b6afe226bba11b50af033R1-R120) [/#diff-6f1ceed6b5955c1d7ea7d388fcc43d6a44f90bfb30a4376d70a6cdf0b38c1d8eR1-R112](https://github.com///issues/diff-6f1ceed6b5955c1d7ea7d388fcc43d6a44f90bfb30a4376d70a6cdf0b38c1d8eR1-R112) [/#diff-aca25d7e4e2d06e24cd97ea558679a1a73cb8ca80650fe880f77060e31bc4945R1-R27](https://github.com///issues/diff-aca25d7e4e2d06e24cd97ea558679a1a73cb8ca80650fe880f77060e31bc4945R1-R27) [/#diff-b8373c012a9e68affaee672c95542d3ca2a5ba6410bf31a71f91f0121a61a90aL7-R8](https://github.com///issues/diff-b8373c012a9e68affaee672c95542d3ca2a5ba6410bf31a71f91f0121a61a90aL7-R8) [/#diff-59051365776908679c61605c9bf5f1adf05e94f862c6929c426267e264070f94L44-R46](https://github.com///issues/diff-59051365776908679c61605c9bf5f1adf05e94f862c6929c426267e264070f94L44-R46) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L48-L65) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L74-R57](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L74-R57) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L2-L15](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L2-L15) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L70-R203](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L70-R203) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086R223-R224](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086R223-R224) [/#diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L42-R42](https://github.com///issues/diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L42-R42) [/#diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9L71-R71](https://github.com///issues/diff-d9b8532ab5dd585cf4eceaf011d7e8677e139d2a89e182a2ad440d7e1b427bb9L71-R71)
* [DEV-974] add cbm statuses adapters ([#706](https://github.com/nclarity/backend/issues/706)) ([cb0275f](https://github.com/nclarity/backend/commit/cb0275fd890f499d1fbe93e778cf0f66c3487cf9)), closes [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L2-R2](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L2-R2) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086R68-R71](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086R68-R71) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L131-R138](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L131-R138) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L173-R177](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L173-R177) [/#diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L197-R213](https://github.com///issues/diff-06ca1f9b1f77041dbc7e33a6fa1f20cabcdbd238ac05767ad9ffc31f25a48086L197-R213) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R14](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R14) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R58](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R58) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R129](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4R129)
* [DEV-993] Create endpoint to delete a multiple CBM history entries ([#700](https://github.com/nclarity/backend/issues/700)) ([b3872c3](https://github.com/nclarity/backend/commit/b3872c377b0d4013a68c39c96d8a500d25d89090))
* add correct equipment profile validation ([3b36b2e](https://github.com/nclarity/backend/commit/3b36b2e5a58b921f02c0259e01e641ebd1de53d6))
* **api:** [DEV- 1162] Allow user token for `v3/charts/measures` and `v3/device-simulation/calculate` APIs ([#704](https://github.com/nclarity/backend/issues/704)) ([605b236](https://github.com/nclarity/backend/commit/605b23671142f93087d76ea6dc8f36f96012a4ac))
* **api:** [DEV-992] Added endpoint to delete a single CBM history entry ([#699](https://github.com/nclarity/backend/issues/699)) ([3740638](https://github.com/nclarity/backend/commit/374063829c5c7409b5d0d7cd5bdd14e05edeb738))

# [2.35.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.34.0...@nclarity/contracts-v2.35.0) (2025-02-25)


### Bug Fixes

* [DEV-1046] Added equipmentId to sendMessage payload ([#666](https://github.com/nclarity/backend/issues/666)) ([91d3217](https://github.com/nclarity/backend/commit/91d321748ae7aadc64af06be70702d0e039fb7c2))
* [DEV-1068] ruleset toggle fix ([#669](https://github.com/nclarity/backend/issues/669)) ([2d13d4b](https://github.com/nclarity/backend/commit/2d13d4bc94df42d2dfae3c2f1605129781c510d1))
* [DEV-1069] unable to delete the equipment ([#667](https://github.com/nclarity/backend/issues/667)) ([1c6d11f](https://github.com/nclarity/backend/commit/1c6d11f3ba6e42083b9e998aaddb32bcf7f331bf))
* [DEV-1146] frequency is added in usecase ([#674](https://github.com/nclarity/backend/issues/674)) ([4e8d01d](https://github.com/nclarity/backend/commit/4e8d01d8b271258a953a2c5df41ea14dc270d06c))
* [DEV-1147] Some Pulse gives error when been added to equipment ([#675](https://github.com/nclarity/backend/issues/675)) ([e74d06d](https://github.com/nclarity/backend/commit/e74d06dedbf82450c013f9eff28f485ebb7ea4ce))
* [DEV-1148] add script to migrate alerts ([#678](https://github.com/nclarity/backend/issues/678)) ([138beb5](https://github.com/nclarity/backend/commit/138beb5a7e7b8947dbf8a1d794ff3f0cd5ebe221)), closes [/#diff-42a11f918227fbebc41d85321a775b60c419ebf46f40e31860b74668837b4c8dL18-R20](https://github.com///issues/diff-42a11f918227fbebc41d85321a775b60c419ebf46f40e31860b74668837b4c8dL18-R20) [/#diff-ada2140ff24821db4d241799276e7eddb879bdf245bb263227f4e98625cc8945L16-R23](https://github.com///issues/diff-ada2140ff24821db4d241799276e7eddb879bdf245bb263227f4e98625cc8945L16-R23) [/#diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3L19-R24](https://github.com///issues/diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3L19-R24) [/#diff-cc48ef831cdaf013b319f972b2aae21721177d23982482dfe2deef105b95bdc8L99-R99](https://github.com///issues/diff-cc48ef831cdaf013b319f972b2aae21721177d23982482dfe2deef105b95bdc8L99-R99) [/#diff-528bbae44d8c622cc3e1132d2fe2e5acf5003347a3c9f1edb7884e5bebc21032L43](https://github.com///issues/diff-528bbae44d8c622cc3e1132d2fe2e5acf5003347a3c9f1edb7884e5bebc21032L43)
* [DEV-1160] change validation in rule assignment controller ([#680](https://github.com/nclarity/backend/issues/680)) ([e4a2c78](https://github.com/nclarity/backend/commit/e4a2c7883459c1ee694ff42f9433d23f17f36605)), closes [/#diff-ec209ca28bc6f53e0e493cc9c139126c82c53670ea72c5028222f021a7e3ba47L2](https://github.com///issues/diff-ec209ca28bc6f53e0e493cc9c139126c82c53670ea72c5028222f021a7e3ba47L2) [/#diff-ec209ca28bc6f53e0e493cc9c139126c82c53670ea72c5028222f021a7e3ba47L16-R16](https://github.com///issues/diff-ec209ca28bc6f53e0e493cc9c139126c82c53670ea72c5028222f021a7e3ba47L16-R16)
* [DEV-1165] remove uuid from equipment validation ([#679](https://github.com/nclarity/backend/issues/679)) ([baf1bb3](https://github.com/nclarity/backend/commit/baf1bb3656e96f7fe266fc3057296d054734779c)), closes [/#diff-fa5b79db3656085b539f4c71ebe591b8dc9ace4ab8b06c662c51c954b9ab46e2L15-R15](https://github.com///issues/diff-fa5b79db3656085b539f4c71ebe591b8dc9ace4ab8b06c662c51c954b9ab46e2L15-R15) [/#diff-faca3c2ffc1044d8be86ead0a855b121fe9820a2d43484099eebb15b5d00b7a6L13-R13](https://github.com///issues/diff-faca3c2ffc1044d8be86ead0a855b121fe9820a2d43484099eebb15b5d00b7a6L13-R13) [/#diff-724a145e2f70f860cc3ac864b6ca79b6fb74efa6ca1e8e1b065b3927c7785eefL13-R13](https://github.com///issues/diff-724a145e2f70f860cc3ac864b6ca79b6fb74efa6ca1e8e1b065b3927c7785eefL13-R13) [/#diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156L14-R14](https://github.com///issues/diff-30c2248d543c8e66a205248840ea8b42f5af5376c2a9d4dfa0fa37ab7538b156L14-R14) [/#diff-81743d63e1a248bc87d1d7ac4a64cd57a6452425b161eab81788eba3471cd76bL13-R13](https://github.com///issues/diff-81743d63e1a248bc87d1d7ac4a64cd57a6452425b161eab81788eba3471cd76bL13-R13) [/#diff-f7ab1cfdf9794615935e789adacba237519cc1380a648e4afabafb922bc9764aL15-R15](https://github.com///issues/diff-f7ab1cfdf9794615935e789adacba237519cc1380a648e4afabafb922bc9764aL15-R15) [/#diff-0f6efcba6776e6ea302362ea95921d0419b3763a699a278234f282c268a4d8b7L13-R13](https://github.com///issues/diff-0f6efcba6776e6ea302362ea95921d0419b3763a699a278234f282c268a4d8b7L13-R13) [/#diff-06b4508528109ba599b63c602a294f638bf9a2e5909e53149773b51f21b00cf8L14-R14](https://github.com///issues/diff-06b4508528109ba599b63c602a294f638bf9a2e5909e53149773b51f21b00cf8L14-R14) [/#diff-35af214db75f1d358181b856767b693e9bf59a749ea157319d32e0892558c339L14-R14](https://github.com///issues/diff-35af214db75f1d358181b856767b693e9bf59a749ea157319d32e0892558c339L14-R14) [/#diff-dc378fa30197801f8335176f44256806362114b75d13d5efba00f71f48db64a8L24-R24](https://github.com///issues/diff-dc378fa30197801f8335176f44256806362114b75d13d5efba00f71f48db64a8L24-R24) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL42](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL42)
* [DEV-1172] Rooftop Unit Maintenance Monitoring email shows pulse status online instead of offline ([#683](https://github.com/nclarity/backend/issues/683)) ([18b2951](https://github.com/nclarity/backend/commit/18b295121cb3bd04a09b27faf23a06525b083ad0))
* [DEV-146] switch refrigerants values ([91f22ec](https://github.com/nclarity/backend/commit/91f22ec22f8c6ac4dba3de8b97fce4f0823a6986))
* [DEV-925] change exporter and reduce logging ([#677](https://github.com/nclarity/backend/issues/677)) ([8295c22](https://github.com/nclarity/backend/commit/8295c226357baae7048b1b5deedfca34681342c2)), closes [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L95-R95](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L95-R95) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L105-R105](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L105-R105) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL86-R100](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL86-R100) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL124-R124](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL124-R124) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL158-R158](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL158-R158) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL195-R195](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL195-R195) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL221-R221](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL221-R221) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL234-R234](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL234-R234) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL263-R263](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL263-R263) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL281-R281](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL281-R281) [/#diff-813ebe81abb9d7b1375bdb1142da49b39c2ce1d678ce6a25ef9a32340412d694L94-R94](https://github.com///issues/diff-813ebe81abb9d7b1375bdb1142da49b39c2ce1d678ce6a25ef9a32340412d694L94-R94) [/#diff-6445167f5b2928ad7e158230aaccb9a77d971fa2eb803dfd41baf6d2582b8ee0L95-R95](https://github.com///issues/diff-6445167f5b2928ad7e158230aaccb9a77d971fa2eb803dfd41baf6d2582b8ee0L95-R95) [/#diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL40-R40](https://github.com///issues/diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL40-R40) [/#diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL59-R59](https://github.com///issues/diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL59-R59) [/#diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL99-R99](https://github.com///issues/diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeL99-R99) [/#diff-1bf45e7d5eebfecf63c653134c0159f7cc4a9dd520b9770426973a1eaaca974fL48-R48](https://github.com///issues/diff-1bf45e7d5eebfecf63c653134c0159f7cc4a9dd520b9770426973a1eaaca974fL48-R48) [/#diff-36fb5c4b9f3f0ca208b80331b2812c9428093258c7e6004761f43b8f661fe934L35-R35](https://github.com///issues/diff-36fb5c4b9f3f0ca208b80331b2812c9428093258c7e6004761f43b8f661fe934L35-R35) [/#diff-1d33e6802a4892a15c5df968e275100927b080af6eb4a4b5537c0da48aa94015L117-R117](https://github.com///issues/diff-1d33e6802a4892a15c5df968e275100927b080af6eb4a4b5537c0da48aa94015L117-R117) [/#diff-1d33e6802a4892a15c5df968e275100927b080af6eb4a4b5537c0da48aa94015L141-R141](https://github.com///issues/diff-1d33e6802a4892a15c5df968e275100927b080af6eb4a4b5537c0da48aa94015L141-R141) [/#diff-90b10f569db6127f62501e4ade396508ba2b542afecfca0d1e099d8b936b9b51R1-R103](https://github.com///issues/diff-90b10f569db6127f62501e4ade396508ba2b542afecfca0d1e099d8b936b9b51R1-R103) [/#diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adL1-R8](https://github.com///issues/diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adL1-R8) [/#diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adL34-L119](https://github.com///issues/diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adL34-L119) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L8-R8](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7L8-R8) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L9-R9](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44L9-R9) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL6-R6](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL6-R6)
* [DEV-966] Temperature suppression issues ([#672](https://github.com/nclarity/backend/issues/672)) ([90ff9a9](https://github.com/nclarity/backend/commit/90ff9a964c53e810d9e6f36bfb90b0a418886ac7))
* **api:** [DEV-1053] No active insights and tech checklist is displayed on the "Rooftop Unit Maintenance Monitoring" emails ([#670](https://github.com/nclarity/backend/issues/670)) ([f47604a](https://github.com/nclarity/backend/commit/f47604a774ca0b6e17c9881940271d5cc78d9708))
* change notification output only when is first alert ([787d677](https://github.com/nclarity/backend/commit/787d677353b769d2b0de9f31debfb6931d1b3da1))
* change references for ruleProcessorInput ([3f7a193](https://github.com/nclarity/backend/commit/3f7a193015721de3f82ddcb3ff27c0667f6c2bfd))
* change use case to edit insight rule ([33dff78](https://github.com/nclarity/backend/commit/33dff78bee7421a98d026cc60c05638cc81a30ac))
* chnge rules from mongo to mysql ([1676433](https://github.com/nclarity/backend/commit/16764334d769c6c165990bf7e750acdc0c521eee))
* remove duplicated entry for function ([cdc9fbb](https://github.com/nclarity/backend/commit/cdc9fbbd43b97d34653827a5125b99d812160aed))


### Features

* [DEV-1005] add implementer for trendy insights ([#676](https://github.com/nclarity/backend/issues/676)) ([15c58de](https://github.com/nclarity/backend/commit/15c58dea54b2d1cc0ff0f49f782465545b722453)), closes [/#diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adR1-R120](https://github.com///issues/diff-96485e952d2156aa8359471fce6c6f51d2d4e103c4d6e21062b9753fc05857adR1-R120) [/#diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R1-R77](https://github.com///issues/diff-4d46483d50b8ca7c708747ea358300c22e8c38a1ace3d617038ecf629869efe7R1-R77) [/#diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44R1-R79](https://github.com///issues/diff-a8f5c6a587f72161b5fda30f3a0522afbfd4fc1939fd8657f4c259c803c8ce44R1-R79) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL1-R6](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL1-R6) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL40-L48](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL40-L48) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL109-L193](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL109-L193) [/#diff-3c0ee88187a26d705a8c17f9252ae2da6927479c0037c6ceb87540f0be5b9136L2-R150](https://github.com///issues/diff-3c0ee88187a26d705a8c17f9252ae2da6927479c0037c6ceb87540f0be5b9136L2-R150) [/#diff-58b87315196547e85f89f41560b4eeb0efff21f6bbda55ebf8fd763affb21b19L2-R2](https://github.com///issues/diff-58b87315196547e85f89f41560b4eeb0efff21f6bbda55ebf8fd763affb21b19L2-R2) [/#diff-58b87315196547e85f89f41560b4eeb0efff21f6bbda55ebf8fd763affb21b19R40-R44](https://github.com///issues/diff-58b87315196547e85f89f41560b4eeb0efff21f6bbda55ebf8fd763affb21b19R40-R44) [/#diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L5-R6](https://github.com///issues/diff-cdfbd5c01e96e07707d6c511833d994c7c943d823ad6712627dae19dc1951e91L5-R6) [/#diff-d11bd4f70e8a37713434009d9623ddea664ecb3264524d368629253c27741bd4R52](https://github.com///issues/diff-d11bd4f70e8a37713434009d9623ddea664ecb3264524d368629253c27741bd4R52)
* [DEV-1007] Insight Alert type ([#671](https://github.com/nclarity/backend/issues/671)) ([f4d4af2](https://github.com/nclarity/backend/commit/f4d4af2a32849ffca5897e69f1dbc75aeabb33d6))
* [DEV-959] add trendy insights output and feature flags ([#668](https://github.com/nclarity/backend/issues/668)) ([f5066b1](https://github.com/nclarity/backend/commit/f5066b1e0d355cf2ffb1ce8fe79e8bc8a933bff0)), closes [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR70-R73](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR70-R73) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR122-R125](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR122-R125) [/#diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR15](https://github.com///issues/diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR15) [/#diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cL43-R44](https://github.com///issues/diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cL43-R44) [/#diff-74cc5371fff615425e54da1408d5e663261390085586a4ff328826b1292a3285R1-R36](https://github.com///issues/diff-74cc5371fff615425e54da1408d5e663261390085586a4ff328826b1292a3285R1-R36) [/#diff-33a842498427e8f8524e70b522520f19b2ebac30dd990f4afae2b6581d1f8daaR1-R84](https://github.com///issues/diff-33a842498427e8f8524e70b522520f19b2ebac30dd990f4afae2b6581d1f8daaR1-R84) [/#diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR7](https://github.com///issues/diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR7) [/#diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR56-R67](https://github.com///issues/diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR56-R67) [/#diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR96-R101](https://github.com///issues/diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR96-R101) [/#diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR117](https://github.com///issues/diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR117) [/#diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR145](https://github.com///issues/diff-2210cce064980342dbff16a53ca3a267b31b07a03f57b24fb6f9d5c2e7cf271cR145) [/#diff-e7b11bd4833791e7f2ee0c6ead2346c5228ae519a20b799fe54384ee8239adedR22](https://github.com///issues/diff-e7b11bd4833791e7f2ee0c6ead2346c5228ae519a20b799fe54384ee8239adedR22) [/#diff-e7b11bd4833791e7f2ee0c6ead2346c5228ae519a20b799fe54384ee8239adedR31](https://github.com///issues/diff-e7b11bd4833791e7f2ee0c6ead2346c5228ae519a20b799fe54384ee8239adedR31) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR635-R637](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR635-R637) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR662-R664](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR662-R664) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR2154-R2158](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR2154-R2158) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR3720-R3722](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR3720-R3722) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbL4674-R4692](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbL4674-R4692) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbL4749-R4768](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbL4749-R4768) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR5845](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR5845) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR5870](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR5870) [/#diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR6879-R6882](https://github.com///issues/diff-32824c984905bb02bc7ffcef96a77addd1f1602cff71a11fbbfdd7f53ee026bbR6879-R6882) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R90](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R90) [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R135-R141](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R135-R141)
* [DEV-991] cbm insights usecase added ([#681](https://github.com/nclarity/backend/issues/681)) ([5d2b527](https://github.com/nclarity/backend/commit/5d2b527132b9869662badb7706210da3c7778adf))
* add creation for frequency ([45a55cf](https://github.com/nclarity/backend/commit/45a55cf2a4862dd9407a379c75736e5e53ab5632))
* **api:** [DEV-961] Create BuildingToInsightRule relationship table and CRUD operations ([#684](https://github.com/nclarity/backend/issues/684)) ([496f710](https://github.com/nclarity/backend/commit/496f7105cc81e2a4d1a0aed329a707dc84e747ef))
* change device calculation response ([901fdb2](https://github.com/nclarity/backend/commit/901fdb2012d50e4c8cebe6332c745bfa19467df4))
* improve type definitions for contracts ([1caaae0](https://github.com/nclarity/backend/commit/1caaae04862f60bf855f30f9a1d2805ee579c360))

# [2.34.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.33.0...@nclarity/contracts-v2.34.0) (2025-01-22)


### Features

* [DEV-956] add isCbmEnabled field ([#664](https://github.com/nclarity/backend/issues/664)) ([0d611a2](https://github.com/nclarity/backend/commit/0d611a259f28841c059a46d975290a21cde8ca78)), closes [/#diff-58ef932d22013632e86dc48712c3bec9323bfd99fcbb098242abd556dd40ec4dR1-R3](https://github.com///issues/diff-58ef932d22013632e86dc48712c3bec9323bfd99fcbb098242abd556dd40ec4dR1-R3) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR513](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR513)
* [DEV-958] add frequency fields and models ([#665](https://github.com/nclarity/backend/issues/665)) ([371647d](https://github.com/nclarity/backend/commit/371647d4f72898cacc70512085da55a3065f9db1)), closes [/#diff-58c3981c14133337defb6a589b4556c14a21fbd6b02a5aed4e85ad243b2f033cR1-R25](https://github.com///issues/diff-58c3981c14133337defb6a589b4556c14a21fbd6b02a5aed4e85ad243b2f033cR1-R25) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR514-R515](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR514-R515) [/#diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR526-R551](https://github.com///issues/diff-5c96d48dc56e6a58251d77cc07e358689c2c1db788fed7df23fea22b3f72699dR526-R551)

# [2.33.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.32.1...@nclarity/contracts-v2.33.0) (2025-01-22)


### Features

* improve contracts definitions ([b90641e](https://github.com/nclarity/backend/commit/b90641e9743a8eeedb446b7f6abe9b1ec4c85876))

## [2.32.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.32.0...@nclarity/contracts-v2.32.1) (2025-01-22)


### Bug Fixes

* improve contracts package ([1137b9e](https://github.com/nclarity/backend/commit/1137b9eef7ad45bf7c9420ea0e59d7b0827641f9))

# [2.32.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.31.0...@nclarity/contracts-v2.32.0) (2025-01-22)


### Bug Fixes

* [DEV-1021] equal range fix ([#650](https://github.com/nclarity/backend/issues/650)) ([5301faa](https://github.com/nclarity/backend/commit/5301faa002d1fa87d517fff129c3de4fc6f06afe))
* [DEV-1030] display previous equipment insights in Insight History after pulse reassignment ([#655](https://github.com/nclarity/backend/issues/655)) ([bddb199](https://github.com/nclarity/backend/commit/bddb199a2b471ab6d3c73afd8bc48cd9129a9d69))
* [DEV-1030] update retention policy in getAlertsByEquipmentIds to 'infinite' ([#662](https://github.com/nclarity/backend/issues/662)) ([74d45dc](https://github.com/nclarity/backend/commit/74d45dcef9c9086e80413292282c6c227d5c0759))
* [DEV-1033] change reference for equipment profile id ([b120c00](https://github.com/nclarity/backend/commit/b120c007143d713e83bd3c785681f10a70c080d3))
* [DEV-1036] update reference for equipment profile in device ([#663](https://github.com/nclarity/backend/issues/663)) ([07f3ee1](https://github.com/nclarity/backend/commit/07f3ee14402735a9ee3484b26ebf064ed0d91bdf)), closes [/#diff-2908b4b3368385ecf41988aff7b3b3686f822dd04649bda2063299bd5e16cb0fL32-R38](https://github.com///issues/diff-2908b4b3368385ecf41988aff7b3b3686f822dd04649bda2063299bd5e16cb0fL32-R38)
* [DEV-1044] temperature value convertion ([#651](https://github.com/nclarity/backend/issues/651)) ([120e7e1](https://github.com/nclarity/backend/commit/120e7e1f59f4ee6ae8b7184d1a42cc0561f0ed54))
* [DEV-901] temperature rule updated ([#628](https://github.com/nclarity/backend/issues/628)) ([8713992](https://github.com/nclarity/backend/commit/8713992542cd9f165d150e6c1c6abc472275804b))
* [DEV-907] increase timeout for user creation and deletion use cases; update schema for optional user reference in notes ([#634](https://github.com/nclarity/backend/issues/634)) ([67c9fb6](https://github.com/nclarity/backend/commit/67c9fb6d6a809772c74210774154eb3c58cc5672))
* [DEV-917] add filter for devices list ([#626](https://github.com/nclarity/backend/issues/626)) ([6f66d53](https://github.com/nclarity/backend/commit/6f66d53645244318a9c26e5f0b18a1421db7b512))
* [DEV-922] change schema validation for device id ([#627](https://github.com/nclarity/backend/issues/627)) ([7a7c233](https://github.com/nclarity/backend/commit/7a7c2339423fbc2625d4afcd2f2f293cb244ec3e))
* [DEV-924] reuse device schema validation ([#629](https://github.com/nclarity/backend/issues/629)) ([816ad7b](https://github.com/nclarity/backend/commit/816ad7bd5cc9fc9b5ccc9e2ad56d18a3a3266dc3))
* [DEV-927] added oadb as to be calculated ([#639](https://github.com/nclarity/backend/issues/639)) ([29623b3](https://github.com/nclarity/backend/commit/29623b30a84d47cc088d9c15d2909fe3a76472c7))
* [DEV-927] fixed split logic for telemetry keys ([#641](https://github.com/nclarity/backend/issues/641)) ([e1d6607](https://github.com/nclarity/backend/commit/e1d6607fa2b3eab2e8c2e66e44255dfe323a8d1d))
* [DEV-929] add device attachment to device creation ([#631](https://github.com/nclarity/backend/issues/631)) ([88b0ce3](https://github.com/nclarity/backend/commit/88b0ce3fd3308c83c3ee0a6349b4ef1338fc1383))
* [DEV-935] aaon devices not visible ([8bdb500](https://github.com/nclarity/backend/commit/8bdb5006209e67aee97b78af27e0f6e8e1b63064))
* [DEV-937] add gateway filtering ([#632](https://github.com/nclarity/backend/issues/632)) ([fe589e5](https://github.com/nclarity/backend/commit/fe589e5684a3b5e8727ef5a801a6116cba6d3605))
* [DEV-966] dry bulb temperature conversion fix ([#644](https://github.com/nclarity/backend/issues/644)) ([eb0f4ee](https://github.com/nclarity/backend/commit/eb0f4eee77ca47a75eaf01a9be1bb549a72f283f))
* [DEV-967] relative humity range issue ([#645](https://github.com/nclarity/backend/issues/645)) ([148d6ea](https://github.com/nclarity/backend/commit/148d6ea054ad82b3d70fe04de383e7ba8799e354))
* **api:** [DEV-1055]update equipment profile reference to use documentId instead of pulseId ([#657](https://github.com/nclarity/backend/issues/657)) ([7eee386](https://github.com/nclarity/backend/commit/7eee38663afc8ed9eeae68ab8c320929d99dc86f))
* **api:** [DEV-1059]  Error appears when linking a new equipment to a recently unassigned pulse ([#659](https://github.com/nclarity/backend/issues/659)) ([2af9a78](https://github.com/nclarity/backend/commit/2af9a786cd6b7ff6c5d654b0b3cfa33a86db08b5))
* **api:** [DEV-1061] Equipment gets disappeared on building page after assigning new pulse ([#660](https://github.com/nclarity/backend/issues/660)) ([405d0a2](https://github.com/nclarity/backend/commit/405d0a291eff2d2aea31d33f633b74b9c0c865fa))
* change reference for equipment profile id ([c584294](https://github.com/nclarity/backend/commit/c584294b892b94b89549a040fcd27468b9a6e9ab))
* **nexus:** [DEV-1023] fixed error to process rules ([#654](https://github.com/nclarity/backend/issues/654)) ([1bfaace](https://github.com/nclarity/backend/commit/1bfaacef0ce2874d1d1bf4c321740ca58f74edc8))
* **nexus:** [DEV-909] Cleaned erroneous telemetry data before processing and calculating the values ([#649](https://github.com/nclarity/backend/issues/649)) ([782fbed](https://github.com/nclarity/backend/commit/782fbeda26f0e3b6c483cfcb098a675192290215))


### Features

* [DEV-1029] add timeout for alerts queries ([#652](https://github.com/nclarity/backend/issues/652)) ([14406ce](https://github.com/nclarity/backend/commit/14406ce1deae259b0c31ed1a7f75bdaeae6b45cd)), closes [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R29](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R29) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R29](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R29) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R46](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778R46) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R48](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R48)
* [DEV-706] mapping for AAON devices ([#625](https://github.com/nclarity/backend/issues/625)) ([803598b](https://github.com/nclarity/backend/commit/803598b6a847254a85ae3972c08bf0a87c31c9ce))
* [DEV-899] add gw validation and improve device creation ([#623](https://github.com/nclarity/backend/issues/623)) ([206eb0a](https://github.com/nclarity/backend/commit/206eb0aa59fc38e46402893059a3e2ddbf26dbab))
* [DEV-927] range conditions added ([#636](https://github.com/nclarity/backend/issues/636)) ([0900a28](https://github.com/nclarity/backend/commit/0900a289bffa4279168c1d48d873687a58da04a0))
* **api:** [DEV-637] updated process to add a equipment ([#540](https://github.com/nclarity/backend/issues/540)) ([10a25da](https://github.com/nclarity/backend/commit/10a25dafc54beb3e16ef9ba84d46f5bf4b280e0b))
* **api:** [DEV-637] updated process to get a equipment details and equipment profiles ([#538](https://github.com/nclarity/backend/issues/538)) ([d853353](https://github.com/nclarity/backend/commit/d853353526d660bd0bbe4c4e4bf8cd4aff92cb7a))
* **api:** [DEV-883] assign pulse ([#647](https://github.com/nclarity/backend/issues/647)) ([bba36b1](https://github.com/nclarity/backend/commit/bba36b118b822b49b8e8d6d9bda832d7414a3fb0))
* **api:** [DEV-908] enhance telemetry use case to retrieve last points from cache ([#643](https://github.com/nclarity/backend/issues/643)) ([4e40165](https://github.com/nclarity/backend/commit/4e401659f101b69ecb3578b87727e8ea5695a640))
* improve contracts package definition ([8854eb8](https://github.com/nclarity/backend/commit/8854eb8cff2f0f7bc636676eac1caa5a0455b5e8))
* **packages:** [DEV-637] add equipmentId field to alerts in InfluxDB ([#638](https://github.com/nclarity/backend/issues/638)) ([5dbbe9d](https://github.com/nclarity/backend/commit/5dbbe9d00845644ea24065589a8903c6cd8cf034))
* rules changed to use mysql ([#648](https://github.com/nclarity/backend/issues/648)) ([3ed1dc0](https://github.com/nclarity/backend/commit/3ed1dc007da28e34011127cfbfbf57d069dceb05)), closes [/#diff-93c53125c902e0239eecc30fe32cc4baf7dc420af592b74bc3117af4d762238bR1](https://github.com///issues/diff-93c53125c902e0239eecc30fe32cc4baf7dc420af592b74bc3117af4d762238bR1) [/#diff-93c53125c902e0239eecc30fe32cc4baf7dc420af592b74bc3117af4d762238bR144-R156](https://github.com///issues/diff-93c53125c902e0239eecc30fe32cc4baf7dc420af592b74bc3117af4d762238bR144-R156) [/#diff-6a27f431aa0ccfe2d649dbd3c1b9c234f8cc3c6a16c23f1752141363c7a94cc6R2-R26](https://github.com///issues/diff-6a27f431aa0ccfe2d649dbd3c1b9c234f8cc3c6a16c23f1752141363c7a94cc6R2-R26) [/#diff-a1b95e55b4b4d8b0cce6750eb9201524d328479312202b80947de4bfb3eed2a5L2-L38](https://github.com///issues/diff-a1b95e55b4b4d8b0cce6750eb9201524d328479312202b80947de4bfb3eed2a5L2-L38) [/#diff-81b15c2eca41b7cbcaa5650b669fa775a1094227e536fdb79f88d715b90c4a76L2-R26](https://github.com///issues/diff-81b15c2eca41b7cbcaa5650b669fa775a1094227e536fdb79f88d715b90c4a76L2-R26) [/#diff-7d94220b948cd41a7e66f81e48e424ecda383b9fbf348993855e11b4388efa7fL3-R28](https://github.com///issues/diff-7d94220b948cd41a7e66f81e48e424ecda383b9fbf348993855e11b4388efa7fL3-R28) [/#diff-741519bca4f10ab3843ac05947ffbfd98764810171fa847f79bb9d7a485a6767L2-R5](https://github.com///issues/diff-741519bca4f10ab3843ac05947ffbfd98764810171fa847f79bb9d7a485a6767L2-R5) [/#diff-74335852be885fa624126e13a82c126421af7634d5a234908cf571d9a1947273L3-R4](https://github.com///issues/diff-74335852be885fa624126e13a82c126421af7634d5a234908cf571d9a1947273L3-R4) [/#diff-96e46d89deedd75bdc9a7d25c76980b683a37dc6552cda0c1893ad32a611d206L1-L9](https://github.com///issues/diff-96e46d89deedd75bdc9a7d25c76980b683a37dc6552cda0c1893ad32a611d206L1-L9)

# [2.31.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.30.0...@nclarity/contracts-v2.31.0) (2024-12-02)


### Bug Fixes

* [DEV-742] fixed structure to setup a equipment profile ([#596](https://github.com/nclarity/backend/issues/596)) ([369f382](https://github.com/nclarity/backend/commit/369f382596cbba8d1b121930d330d68f9b3659c1))
* [DEV-805] handle null values for kpis ([#599](https://github.com/nclarity/backend/issues/599)) ([e8d4414](https://github.com/nclarity/backend/commit/e8d4414a0ec6cadbce0b04f678074c06e839b029))
* [DEV-813] corporate filter added ([#603](https://github.com/nclarity/backend/issues/603)) ([15dc184](https://github.com/nclarity/backend/commit/15dc1847498eab7e6a6092423cd75bd153b721c7))
* [DEV-813] dev merged ([a42b29a](https://github.com/nclarity/backend/commit/a42b29aa4404217679f8dfd5a9b44b8b0342f78a))
* [DEV-813] deviceId filter added ([4d95742](https://github.com/nclarity/backend/commit/4d95742b23c39dd80a67674068bf10299765fe34))
* [DEV-813] deviceId filter added ([5e41c42](https://github.com/nclarity/backend/commit/5e41c42f519e983d77a52befee62db65a5d74de0))
* [DEV-813] deviceId filter added ([#612](https://github.com/nclarity/backend/issues/612)) ([9729ee5](https://github.com/nclarity/backend/commit/9729ee5b0422fb63f95d6c8c9deb1c89a3dd06cc))
* [DEV-813] requested changes ([23734d2](https://github.com/nclarity/backend/commit/23734d2aa8ab37e94dab808a7fb3c816b0b3ab38))
* [DEV-814] device details issue fix ([#601](https://github.com/nclarity/backend/issues/601)) ([8d73359](https://github.com/nclarity/backend/commit/8d73359115246774ff02ac4162672328248d418e))
* [DEV-814] issue fix ([5fd9360](https://github.com/nclarity/backend/commit/5fd9360f737f81ec918d1ced37818e9e16e4237d))
* [DEV-815] improve query for devices data ([#595](https://github.com/nclarity/backend/issues/595)) ([74ea85a](https://github.com/nclarity/backend/commit/74ea85aaa0f88e71be8eaa6f17301412b8bbb65e))
* [DEV-847] update logic to update equipment condition ([#600](https://github.com/nclarity/backend/issues/600)) ([846364f](https://github.com/nclarity/backend/commit/846364fa8657fb2e8fd04fc3cef6749b32771340))
* [DEV-851] equipment condition donut chart ([#589](https://github.com/nclarity/backend/issues/589)) ([#590](https://github.com/nclarity/backend/issues/590)) ([40f5463](https://github.com/nclarity/backend/commit/40f54638e32e8196535e987512624cd32cf2d270))
* [DEV-870] improve device deletion ([#602](https://github.com/nclarity/backend/issues/602)) ([1511d3b](https://github.com/nclarity/backend/commit/1511d3bff2fc19a03b9e2234df8b2b93e2a091ea))
* [DEV-870]prevent device deletion when equipment is attached and cascade delete equipment notes ([#615](https://github.com/nclarity/backend/issues/615)) ([019d23f](https://github.com/nclarity/backend/commit/019d23fdd108751b02a9e761a8af0b947d61b39e))
* [DEV-879] change timeInHeating variable ([#609](https://github.com/nclarity/backend/issues/609)) ([604e94c](https://github.com/nclarity/backend/commit/604e94c5f2c25d33ab28377d0bb0d9a155db522d))
* [DEV-881] equiments without profiles removed in listing ([#613](https://github.com/nclarity/backend/issues/613)) ([de015e6](https://github.com/nclarity/backend/commit/de015e624eac7b6f7d60cd1c50c61d9ced8258da))
* [DEV-885] enable partial calculated values ([#611](https://github.com/nclarity/backend/issues/611)) ([8e505fb](https://github.com/nclarity/backend/commit/8e505fb4da643422fd087ec1682c7a359723d823))
* [DEV-885] enable partial calculated values ([#611](https://github.com/nclarity/backend/issues/611)) [skip ci] ([#618](https://github.com/nclarity/backend/issues/618)) ([cb2bb55](https://github.com/nclarity/backend/commit/cb2bb55003e4b7d999465acdd8e82c192d922ee7)), closes [/#diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR17](https://github.com///issues/diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR17) [/#diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR17](https://github.com///issues/diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR17) [/#diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR31-R35](https://github.com///issues/diff-aaf86e87abde3fbba4ebd9518bcc93f405f682d9782289c457f6bcaced3b151cR31-R35) [/#diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeL12-R19](https://github.com///issues/diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeL12-R19) [/#diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeL12-R19](https://github.com///issues/diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeL12-R19) [/#diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeR33-R37](https://github.com///issues/diff-3bea92b1aa39285da7dfe98119f620976f95eab44b90513cc4202cd5b5e650eeR33-R37) [/#diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6L12-R17](https://github.com///issues/diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6L12-R17) [/#diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6L12-R17](https://github.com///issues/diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6L12-R17) [/#diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6R31-R35](https://github.com///issues/diff-af2efe0399951ecb4596a64ed2051d1bb7b5c26ac2d18e3db4c8c14c7ceb53f6R31-R35) [/#diff-ddd43ef810d44ae9878b20709fd5c67c7f9889efeb098b5980ad6a9d552f75c6L104-R115](https://github.com///issues/diff-ddd43ef810d44ae9878b20709fd5c67c7f9889efeb098b5980ad6a9d552f75c6L104-R115) [/#diff-01b1756ca8d16abfd65c2f5e922376be8b4969334867c958d2495d66f8440136R1-R13](https://github.com///issues/diff-01b1756ca8d16abfd65c2f5e922376be8b4969334867c958d2495d66f8440136R1-R13) [/#diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L395-R395](https://github.com///issues/diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L395-R395) [/#diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L395-R395](https://github.com///issues/diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L395-R395) [/#diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310R407-R417](https://github.com///issues/diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310R407-R417) [/#diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeR98-R101](https://github.com///issues/diff-921a2492329e180ca41d81ff4bcc6c02fd607960af0b6aa63f0bdd160aea79aeR98-R101) [/#diff-1bf45e7d5eebfecf63c653134c0159f7cc4a9dd520b9770426973a1eaaca974fR31-R35](https://github.com///issues/diff-1bf45e7d5eebfecf63c653134c0159f7cc4a9dd520b9770426973a1eaaca974fR31-R35)
* [DEV-898] equipment status fix ([#619](https://github.com/nclarity/backend/issues/619)) ([7665297](https://github.com/nclarity/backend/commit/7665297e3ee494de37653e44089ea955ee3d4f6a))
* **api:** [DEV-856] Error page displayed when a new user with "Technician" role accesses pulse page ([65d9bf3](https://github.com/nclarity/backend/commit/65d9bf39b2124b5c98bfba648ea8fb33d93b26e8))
* **api:** [DEV-856] Error page displayed when a new user with "Technician" role accesses pulse page ([#598](https://github.com/nclarity/backend/issues/598))[skip ci] ([#598](https://github.com/nclarity/backend/issues/598)) ([95d57e0](https://github.com/nclarity/backend/commit/95d57e0786964bd3892469ee3f93fdc7462e1a12))
* **api:** [DEV-870] set transaction timeout for deleteDeviceUseCase to enhance reliability during deletion process ([7da8d6c](https://github.com/nclarity/backend/commit/7da8d6ca0af977a56b9a63902c5fd1f9e15149da))
* **api:** [DEV-870] set transaction timeout for deleteDeviceUseCase to enhance reliability during deletion process ([#605](https://github.com/nclarity/backend/issues/605)) ([292ac7f](https://github.com/nclarity/backend/commit/292ac7f384a4776613420cd0075ebb1c81f7cd91))
* **api:** [DEV-881] add error handling for pulse creation to prevent transaction failures ([#621](https://github.com/nclarity/backend/issues/621)) ([4031b07](https://github.com/nclarity/backend/commit/4031b074c0267b2cf0cbc72fc2ff8bc63e7b25f4))
* improve point definition for influx ([#620](https://github.com/nclarity/backend/issues/620)) ([13000e5](https://github.com/nclarity/backend/commit/13000e5d8901d46e4f30e158c5ae18606c96e2ef))


### Features

* [DEV-899] add device creation extension and better typings ([#622](https://github.com/nclarity/backend/issues/622)) ([03203d9](https://github.com/nclarity/backend/commit/03203d9eaa59767f94383b688c6097218ca58f76))
* **api:** [DEV-874] integrate cache adapter to list alerts with reviewed status for device-friendly IDs ([f7b9f4c](https://github.com/nclarity/backend/commit/f7b9f4ce6b3e97c34c698663ede4f9cf1fed96ad))
* **api:** [DEV-874] integrate cache adapter to list alerts with reviewed status for device-friendly IDs ([#604](https://github.com/nclarity/backend/issues/604)) [skip ci] ([#604](https://github.com/nclarity/backend/issues/604)) ([e01e56b](https://github.com/nclarity/backend/commit/e01e56bd57dccf11f269b70193f13fa4ddadd5e9))

# [2.30.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.29.0...@nclarity/contracts-v2.30.0) (2024-11-07)


### Bug Fixes

* [DEV-741] change improve summary per account ([64f1a5c](https://github.com/nclarity/backend/commit/64f1a5c8878cfd4d64911353fbb68a8622f817ee))
* [DEV-784] change review process and alert history ([#572](https://github.com/nclarity/backend/issues/572)) ([28ab1b3](https://github.com/nclarity/backend/commit/28ab1b3e6944ce50437fd10244cbced4240fab49))
* [DEV-784] change review process and alert history ([#572](https://github.com/nclarity/backend/issues/572)) ([#573](https://github.com/nclarity/backend/issues/573)) ([cd77a45](https://github.com/nclarity/backend/commit/cd77a456ceb2f141a75f5e99597f38dc270d6129))
* [DEV-785] remove unnecessary mapping in buildings ([351efb7](https://github.com/nclarity/backend/commit/351efb7a103cf9e2c258582d3561de8909a68f83))
* [DEV-785] remove unnecessary severity calculation ([#574](https://github.com/nclarity/backend/issues/574)) ([8d73be7](https://github.com/nclarity/backend/commit/8d73be7321562bcc506de726525b65cf848bae9c))
* [DEV-796] change database for influx ([#576](https://github.com/nclarity/backend/issues/576)) ([334410e](https://github.com/nclarity/backend/commit/334410e43484304c7c6105882078009778df6d0f)), closes [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R81-R82](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R81-R82) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R137-R138](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849R137-R138) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR68-R69](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR68-R69) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR116-R117](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befR116-R117) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280R104-R105](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280R104-R105) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L144-R152](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L144-R152) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L165-R169](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280L165-R169) [/#diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280R196-R210](https://github.com///issues/diff-5d6fe23b7fdb99c3adeb507de60f3bd61a6d40e0fb174ede5f85954b6516e280R196-R210) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR1](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR1) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR10-L12](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR10-L12) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR189-R195](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR189-R195) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL213-R228](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL213-R228) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR10-L12](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR10-L12) [/#diff-2d6b529016b1073e90ae24a0d664d72c5253265dd30d870aac6c64cb7d7406f9L1-L24](https://github.com///issues/diff-2d6b529016b1073e90ae24a0d664d72c5253265dd30d870aac6c64cb7d7406f9L1-L24) [/#diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L1-R11](https://github.com///issues/diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L1-R11) [/#diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L22-L33](https://github.com///issues/diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L22-L33) [/#diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L57-R67](https://github.com///issues/diff-a5c5b9311b3b00505e78bb3ee9e76d3b91895af4cb3bb8b92ce8f715f0aa9143L57-R67) [/#diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3L1-R12](https://github.com///issues/diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3L1-R12) [/#diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3R25-R36](https://github.com///issues/diff-15f5689f1fee5ce400d7b8cd0d0a61d27c9cac8ffd06e1a35cc0a965cd0c8be3R25-R36) [/#diff-8249177fb76ce983c6208f293851c94aefc5faf8e37aab90aa75c0cca010ef84L1-R13](https://github.com///issues/diff-8249177fb76ce983c6208f293851c94aefc5faf8e37aab90aa75c0cca010ef84L1-R13) [/#diff-8249177fb76ce983c6208f293851c94aefc5faf8e37aab90aa75c0cca010ef84R28-R39](https://github.com///issues/diff-8249177fb76ce983c6208f293851c94aefc5faf8e37aab90aa75c0cca010ef84R28-R39)
* [DEV-814] add device friendly id lookup ([#579](https://github.com/nclarity/backend/issues/579)) ([f72e01d](https://github.com/nclarity/backend/commit/f72e01dbbf4fc30d62910f2f65e74e1e92d705b8))
* [DEV-815] change logic to get last data point ([#578](https://github.com/nclarity/backend/issues/578)) ([d1c57c8](https://github.com/nclarity/backend/commit/d1c57c8f29cbdc62a2b8e42680a23a39610fa89b)), closes [/#diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R19](https://github.com///issues/diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R19) [/#diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R19](https://github.com///issues/diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R19) [/#diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL22-R84](https://github.com///issues/diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL22-R84) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L1-R1](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L1-R1) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L1-R1](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L1-R1) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R26-R38](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R26-R38)
* [DEV-829] improve equipment condition calculation ([#584](https://github.com/nclarity/backend/issues/584)) ([1b6103e](https://github.com/nclarity/backend/commit/1b6103e908ed5ea307da499f9b2b5bcf54e001bd))
* [DEV-835] resolve all insights ([#580](https://github.com/nclarity/backend/issues/580)) ([b06e9f8](https://github.com/nclarity/backend/commit/b06e9f8f86e104b85c9271c04ac00ef82b5fe8dd))
* [DEV-837] add orchestrator to retry influx tasks ([#586](https://github.com/nclarity/backend/issues/586)) ([498db31](https://github.com/nclarity/backend/commit/498db316954e60a21e38775a76d9bd331b79122e)), closes [/#diff-79c7b18127ce1ce8bb0d4c70a4912c3d4f7e6103a8eb337593c9eb1cc6842613R1-R22](https://github.com///issues/diff-79c7b18127ce1ce8bb0d4c70a4912c3d4f7e6103a8eb337593c9eb1cc6842613R1-R22) [/#diff-b58978ab7ce5586bbe516a67a7aaa0f8592f652ca18aa5ccb6568d464db1c2d8R1-R31](https://github.com///issues/diff-b58978ab7ce5586bbe516a67a7aaa0f8592f652ca18aa5ccb6568d464db1c2d8R1-R31) [/#diff-1034a1ae14fc4ce5d6dbb67fda1cfeabdcca584d58a17e6ae4343b531ac4e7f3R1-R23](https://github.com///issues/diff-1034a1ae14fc4ce5d6dbb67fda1cfeabdcca584d58a17e6ae4343b531ac4e7f3R1-R23) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R14-R17](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R14-R17) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R76-R78](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R76-R78) [/#diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR9-R11](https://github.com///issues/diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR9-R11) [/#diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR108-R121](https://github.com///issues/diff-1f464efcda3fc598a3103c8b802af536e071896f53e4ed1c56e063ca8739a31cR108-R121)
* [DEV-837] retries now happen for the last hour ([b6d74e7](https://github.com/nclarity/backend/commit/b6d74e776cd984b1cef42d52bd641bf829be8000))
* [DEV-848] use tsdb for telemetry by id lookup ([#593](https://github.com/nclarity/backend/issues/593)) ([f298f33](https://github.com/nclarity/backend/commit/f298f3339b755b115b1c747f9b16d3ab81d946f7))
* [DEV-851] equipment condition donut chart ([#589](https://github.com/nclarity/backend/issues/589)) ([fcdecf7](https://github.com/nclarity/backend/commit/fcdecf716ee8baf1b57227ed6802c1042f1dec4f))
* **api:** [DEV-841] update getEquipmentByPulseId to improve data retrieval accuracy by setting option to true ([#587](https://github.com/nclarity/backend/issues/587)) ([fe53f4f](https://github.com/nclarity/backend/commit/fe53f4f1070ae6898bea5816f2c1766cb79a75e5))
* **nexus:** [DEV-802]sanitize alertId by removing surrounding quotes in processEhRulesImplHandler function ([#581](https://github.com/nclarity/backend/issues/581)) ([00649a3](https://github.com/nclarity/backend/commit/00649a3b38efd8cf6d6ec5d7cd84e6d712e32778))


### Features

* [DEV-740] severity summary for account ([#569](https://github.com/nclarity/backend/issues/569)) ([e8a1da3](https://github.com/nclarity/backend/commit/e8a1da36b1df9dd91efe03d17b3314ce715238bc)), closes [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beR1-R11](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beR1-R11) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beR1-R11](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beR1-R11) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL21-R33](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL21-R33) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL40-L183](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL40-L183) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL193-R169](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL193-R169) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL205-R185](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL205-R185) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL224-R197](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL224-R197) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L7-R7](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L7-R7) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L7-R7](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L7-R7) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L17-R29](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L17-R29) [/#diff-97f4552597dbda1ee259b36571e181c23252a211f2dd52d39275dc77ca06da68R1-R68](https://github.com///issues/diff-97f4552597dbda1ee259b36571e181c23252a211f2dd52d39275dc77ca06da68R1-R68) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R10](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R10) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R10](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R10) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L19-R26](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2L19-R26) [/#diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R65-R67](https://github.com///issues/diff-24c1f49fc3bff0ac09c17d66504557e96208026de1d83348906d530d9cfaf6c2R65-R67) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L8](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L8) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L8](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L8) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L50-R56](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L50-R56) [/#diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L114-L116](https://github.com///issues/diff-174a5cd8a5d6832aba52ed2c868e5d853e801ae8d8d80dcee0f27c665f7bb8d4L114-L116) [/#diff-6a9924d2400911b852610dd1ffa390eeb7c1f529c37a03190a17de175d842a7fL9-R15](https://github.com///issues/diff-6a9924d2400911b852610dd1ffa390eeb7c1f529c37a03190a17de175d842a7fL9-R15)
* [DEV-783] dispatcher page alerts with tsdb ([#570](https://github.com/nclarity/backend/issues/570)) ([cdb4606](https://github.com/nclarity/backend/commit/cdb4606a935e59ac86d5a666ab9b713558a6417f)), closes [/#diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L12-R12](https://github.com///issues/diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L12-R12) [/#diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L12-R12](https://github.com///issues/diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L12-R12) [/#diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022R21-R23](https://github.com///issues/diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022R21-R23) [/#diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L32-L38](https://github.com///issues/diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L32-L38) [/#diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L52-R46](https://github.com///issues/diff-35ff153ac78758c5c30af61c4cefc753068998bd4622f9558465aacd67673022L52-R46) [/#diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L10-R12](https://github.com///issues/diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L10-R12) [/#diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L10-R12](https://github.com///issues/diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L10-R12) [/#diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031R21-R23](https://github.com///issues/diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031R21-R23) [/#diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L32-R33](https://github.com///issues/diff-02c7a671bada4c531455669e927a934823341d3070628510ba8b54bae53b5031L32-R33) [/#diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L12-R12](https://github.com///issues/diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L12-R12) [/#diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L12-R12](https://github.com///issues/diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L12-R12) [/#diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L41-R43](https://github.com///issues/diff-73d03d247f594183a250d718dda097d7911e3a062f3bd6a07f0cfeb11a51ca55L41-R43) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL8-R11](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL8-R11) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL8-R11](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL8-R11) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL39-R40](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fL39-R40) [/#diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L25-R25](https://github.com///issues/diff-e88a13ef965f2bb44e73772679dc0f5a1eb29e323524b4dd9bbda4e112893778L25-R25) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL52-R52](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL52-R52) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL52-R52](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL52-R52) [/#diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL155-R161](https://github.com///issues/diff-f482381f942bb1d490bcbb8f08fc79cef5adfe34d52a5be3817134eb120690beL155-R161) [/#diff-c0ba6d4df13c3287990ec1e6da08ac318b00aab658d8d0570c0ba94072eac330L85](https://github.com///issues/diff-c0ba6d4df13c3287990ec1e6da08ac318b00aab658d8d0570c0ba94072eac330L85)
* DEV-740, DEV-739, & DEV-741 solved ([bd14d7a](https://github.com/nclarity/backend/commit/bd14d7ab3185cd82a8d28eec7a2ce882277385cc))

# [2.29.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.28.0...@nclarity/contracts-v2.29.0) (2024-10-20)

### Features

- [DEV-738] unresolved alerts list use case ([#563](https://github.com/nclarity/backend/issues/563)) ([e334514](https://github.com/nclarity/backend/commit/e334514dba3b07d79df5ff5236f83cf413d11061))
- add endpoints to get alert history ([4bd75f8](https://github.com/nclarity/backend/commit/4bd75f8d0a5c0ac1e964f766db87be9a6871e9ee))
- add pagination options ([deab60c](https://github.com/nclarity/backend/commit/deab60cdc1de4e91c33a9cb364b510d324f13019))
- **api:** [DEV-735] update the endpoint to get alert history from tsdb ([#565](https://github.com/nclarity/backend/issues/565)) ([4d3df92](https://github.com/nclarity/backend/commit/4d3df92a844eb5259ce5cff3903c458d36128a8a)), closes [/#diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L13-R22](https://github.com///issues/diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L13-R22) [/#diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L13-R22](https://github.com///issues/diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L13-R22) [/#diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L30-R35](https://github.com///issues/diff-4b3adac813ed313b2d8298e4d83511f008abf9e04795547b0a26a7b607fb3295L30-R35) [/#diff-bf91678c3f17826e41f3a7b43bb5c2c0ce9c3db472e995a553b446e412fdd345R1-R30](https://github.com///issues/diff-bf91678c3f17826e41f3a7b43bb5c2c0ce9c3db472e995a553b446e412fdd345R1-R30) [/#diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L1-R21](https://github.com///issues/diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L1-R21) [/#diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L1-R21](https://github.com///issues/diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L1-R21) [/#diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L33-R46](https://github.com///issues/diff-bd91e22ebb20408bd2d9369149447995e98c2924bb30a971af31eaab1f7966a7L33-R46) [/#diff-17dafd5e4ff6f2b951a01e69392452f4068c8274db83c66b314e07a6fd9268ffL1-R72](https://github.com///issues/diff-17dafd5e4ff6f2b951a01e69392452f4068c8274db83c66b314e07a6fd9268ffL1-R72) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR6](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR6) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR6](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR6) [/#diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR78-R84](https://github.com///issues/diff-558bc057a3ef1fecb91a13df290c79b4186d12d7ae36659a85844c8d73e2976fR78-R84) [/#diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L1-L16](https://github.com///issues/diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L1-L16) [/#diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L1-L16](https://github.com///issues/diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L1-L16) [/#diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L30-R89](https://github.com///issues/diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L30-R89) [/#diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L136-R137](https://github.com///issues/diff-0c6981b8f316af6292a2f8d053c17fcc288e12c71987c4e15927fb8142bcee75L136-R137)
- refactor list alerts history to tsdb data ([318be3b](https://github.com/nclarity/backend/commit/318be3bd3350dc6f966c1bfc0d3a8b2d46805ae2))

# [2.28.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.27.0...@nclarity/contracts-v2.28.0) (2024-10-18)

### Bug Fixes

- improve flux point builder ([8f8c41a](https://github.com/nclarity/backend/commit/8f8c41a9d25b046853f0bccdd4de1aa16eae7c74))

### Features

- [DEV-732] add alerts to influx ([829291d](https://github.com/nclarity/backend/commit/829291dde90cdc3070296d7ae1aa75b73a53dbf2))
- [DEV-733] add influx persistor for alerts ([#559](https://github.com/nclarity/backend/issues/559)) ([e6f7cd4](https://github.com/nclarity/backend/commit/e6f7cd49940ae1ccd991779aab219dbe08ec8ce7))
- [DEV-734] handleWorkflowTrigger tsdb migration ([#560](https://github.com/nclarity/backend/issues/560)) ([3faaf4a](https://github.com/nclarity/backend/commit/3faaf4ae6ed0c7ef7d1a75796707d586fffcaee1))
- [DEV-736] add resolve insight with tsdb ([#562](https://github.com/nclarity/backend/issues/562)) ([c79f692](https://github.com/nclarity/backend/commit/c79f692c778d01d1b49a4cc3f9acca909b765e54)), closes [/#diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509R19-R34](https://github.com///issues/diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509R19-R34) [/#diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509R19-R34](https://github.com///issues/diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509R19-R34) [/#diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509L33-R49](https://github.com///issues/diff-9769d88111e7e7a332b2ac172801ea0c5f3e77662d396b1f8101de9c088f1509L33-R49) [/#diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL1-R10](https://github.com///issues/diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL1-R10) [/#diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL13-R133](https://github.com///issues/diff-8c32701f7f0724a9a414d1ec065125e10b67b29d8e9534ce881f27dac5e4908dL13-R133) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL1-L10](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL1-L10) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL37-R38](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL37-R38) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL101-R121](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeL101-R121) [/#diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeR144-R151](https://github.com///issues/diff-2dc2d742460785099dd064164887a0ef79d83477d9c53bc4787eba9767c500eeR144-R151) [/#diff-1d19cfb128dc242577bd5799a1871a640ae6d88160799a9b813cb7ed255417e9L6-R28](https://github.com///issues/diff-1d19cfb128dc242577bd5799a1871a640ae6d88160799a9b813cb7ed255417e9L6-R28) [/#diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fL6-R13](https://github.com///issues/diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fL6-R13) [/#diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fL6-R13](https://github.com///issues/diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fL6-R13) [/#diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fR23-R34](https://github.com///issues/diff-eb7a3a9bc1634b20d82d65bd9b5437fe412409370f6c74d3db7e023207bdc04fR23-R34) [/#diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23L43-R43](https://github.com///issues/diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23L43-R43) [/#diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23L43-R43](https://github.com///issues/diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23L43-R43) [/#diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R66-R72](https://github.com///issues/diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R66-R72) [/#diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R85-R86](https://github.com///issues/diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R85-R86) [/#diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R113-R114](https://github.com///issues/diff-7a40900622bdf555f48176305632b6c5953da1751f1b13ddc650f491fa307d23R113-R114)
- [DEV-777] update equipment condition refactor ([#561](https://github.com/nclarity/backend/issues/561)) ([c3b6bf9](https://github.com/nclarity/backend/commit/c3b6bf9bfba67260b236856563ff58945f0574a7))
- add profile default creation controller ([7598cfc](https://github.com/nclarity/backend/commit/7598cfcb8edab575980092efc10d6e6fa8e8dd70))

# [2.27.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.26.0...@nclarity/contracts-v2.27.0) (2024-10-03)

### Bug Fixes

- [DEV-725] add telemetry temperature transformers ([#550](https://github.com/nclarity/backend/issues/550)) ([18e4a25](https://github.com/nclarity/backend/commit/18e4a25a86fed73852a3cc93a3624601fc0438f0))
- [DEV-725] insight false flag ([#549](https://github.com/nclarity/backend/issues/549)) ([6ba161b](https://github.com/nclarity/backend/commit/6ba161bf333ff97859c4c84a52f6ab92936e3abb))
- add split exceptions for telemetry measures ([85014a8](https://github.com/nclarity/backend/commit/85014a8d5e7c7ec72025c55962c328a67f8b62e0))
- add telemetry transformers ([5dfbd65](https://github.com/nclarity/backend/commit/5dfbd653af4cec0155e02c833424f6235c406ab4))
- add telemetryId per insights triggered ([755ab9c](https://github.com/nclarity/backend/commit/755ab9caa0bc4aa9e68a38fc691cfb9d1a5a553e))
- change order for telemetry controller ([be04aef](https://github.com/nclarity/backend/commit/be04aef4520ab59bd3b2e243327bf294fb4be87e))
- create raw telemetry endpoint per id ([4f6ac92](https://github.com/nclarity/backend/commit/4f6ac923316318a8be11c79cb86573362f2045da))
- improve derived states calculations ([9e7a2fc](https://github.com/nclarity/backend/commit/9e7a2fce0d4883230d64c2a71a69ff8f9d91a66a))
- improve sorting for getHistoryByAlertId use case ([2467363](https://github.com/nclarity/backend/commit/2467363813428991b2eec13b64732960ad627d95))
- **nexus:** enable multipartition approach ([67a4e52](https://github.com/nclarity/backend/commit/67a4e52439917a9eff509f362d87394820339b66))
- remove clean alert request ([d580f22](https://github.com/nclarity/backend/commit/d580f22ee3cb861ed215880f0b5be3e7e56221fa))

### Features

- [DEV-709] endpoint to reboot device added ([a3bd216](https://github.com/nclarity/backend/commit/a3bd216fb30102cedb9511fa519b0feaa0322492))
- [DEV-709] endpoint to reboot device added ([#545](https://github.com/nclarity/backend/issues/545)) [skip ci] ([7e2fce3](https://github.com/nclarity/backend/commit/7e2fce3a1cc4ffbaead25cf4e1cfa22f7dddd559))
- [DEV-714] add tsdb exporter ([#557](https://github.com/nclarity/backend/issues/557)) ([059260a](https://github.com/nclarity/backend/commit/059260a50b1aafe3370be012b0fd51f6ee12ee98)), closes [/#diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R130](https://github.com///issues/diff-a5de3e5871ffcc383a2294845bd3df25d3eeff6c29ad46e3a396577c413bf357R130) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL61-R67](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL61-R67) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL61-R67](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL61-R67) [/#diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL110-R113](https://github.com///issues/diff-e06d755beb0c9b61786db7f33f414237f2c55a5351480c6d2d3e438d60131befL110-R113) [/#diff-e45e45baeda1c1e73482975a664062aa56f20c03dd9d64a827aba57775bed0d3L18-R21](https://github.com///issues/diff-e45e45baeda1c1e73482975a664062aa56f20c03dd9d64a827aba57775bed0d3L18-R21) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR13-L21](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR13-L21) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR13-L21](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eR13-L21) [/#diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL282-R290](https://github.com///issues/diff-9324023aa86888b309ed00b7e999bc1e15db721ba402c85efde5b2aed4034f3eL282-R290) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR3](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR3) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR3](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR3) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR15-R18](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR15-R18) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR32](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR32) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR47-R50](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR47-R50) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efL53-R78](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efL53-R78) [/#diff-2d6b529016b1073e90ae24a0d664d72c5253265dd30d870aac6c64cb7d7406f9L1-R13](https://github.com///issues/diff-2d6b529016b1073e90ae24a0d664d72c5253265dd30d870aac6c64cb7d7406f9L1-R13) [/#diff-62a1edcd6d9d9a583a2f8aae3f3ed10cb8798001ce46a210f89bc18d7c6fca28R27](https://github.com///issues/diff-62a1edcd6d9d9a583a2f8aae3f3ed10cb8798001ce46a210f89bc18d7c6fca28R27) [/#diff-1349f3412ff2498089104c031bcd07666359b8766bb1c4aef16f5ad1b49a2a4bR20](https://github.com///issues/diff-1349f3412ff2498089104c031bcd07666359b8766bb1c4aef16f5ad1b49a2a4bR20) [/#diff-377a5451322bba95c32cf94632f01c3b6cf3f8bf54ce2f995b27654f8386d808L2-R2](https://github.com///issues/diff-377a5451322bba95c32cf94632f01c3b6cf3f8bf54ce2f995b27654f8386d808L2-R2) [/#diff-fb253f8cfec836effe538f7e48c37157d50f045422e8a6a6543ca7e1d88dc8e7L1-R1](https://github.com///issues/diff-fb253f8cfec836effe538f7e48c37157d50f045422e8a6a6543ca7e1d88dc8e7L1-R1) [/#diff-cfd50f58702a555ed8d2835dedf2b74e2bf6946adaf4a70338b7bd9e1910545bL1](https://github.com///issues/diff-cfd50f58702a555ed8d2835dedf2b74e2bf6946adaf4a70338b7bd9e1910545bL1) [/#diff-e38b9fdcb4db1b77dcc195d0215cbabbfc86c405cfb977b856c6f4bc9a507bfbR1](https://github.com///issues/diff-e38b9fdcb4db1b77dcc195d0215cbabbfc86c405cfb977b856c6f4bc9a507bfbR1) [/#diff-6cddfe040c2a7c5b4b89a394cbc501e698a78f2f4ef139c7f764d4113f5bd51aR4](https://github.com///issues/diff-6cddfe040c2a7c5b4b89a394cbc501e698a78f2f4ef139c7f764d4113f5bd51aR4) [/#diff-e6a1e42e3af98d5376c376ac7ce6eed12b47c9ae3963b0ffd3346dbc877d054bL4-R8](https://github.com///issues/diff-e6a1e42e3af98d5376c376ac7ce6eed12b47c9ae3963b0ffd3346dbc877d054bL4-R8) [/#diff-cf368e4831558e31b557cfd1090845f5ec19ef949a79a1e5bae7c382fc769cecR14-R17](https://github.com///issues/diff-cf368e4831558e31b557cfd1090845f5ec19ef949a79a1e5bae7c382fc769cecR14-R17) [/#diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL3-R3](https://github.com///issues/diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL3-R3) [/#diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL3-R3](https://github.com///issues/diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL3-R3) [/#diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL157-L181](https://github.com///issues/diff-314fafad9e21409ec87d1bffe009217f7c1c14ef60c6fb1cf22bcc44a71c14caL157-L181) [/#diff-d5b94c6fb476484d83d9ef2e3b47ab56675bf58b3e60f90d01e6053b8a676cc3R4](https://github.com///issues/diff-d5b94c6fb476484d83d9ef2e3b47ab56675bf58b3e60f90d01e6053b8a676cc3R4)
- [DEV-715] add endpoint to get data ([#558](https://github.com/nclarity/backend/issues/558)) ([45c76c4](https://github.com/nclarity/backend/commit/45c76c4bc71714c4655251c45ea1726e1730eb83)), closes [/#diff-04ab0ba666ef061d5f8c2753f93f4dbd3ea8b8b4e0a412591e30664a060ad7aaL53-L57](https://github.com///issues/diff-04ab0ba666ef061d5f8c2753f93f4dbd3ea8b8b4e0a412591e30664a060ad7aaL53-L57) [/#diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-R41](https://github.com///issues/diff-07e4efc1aec0c3cad7eba8b4b1fa98299a025b8f29778974ff7d335b8fc45297L1-R41) [/#diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R53](https://github.com///issues/diff-61b308128cbae3ee98df09de3cc4e62c6505e3cc35310bd7fc2a3b3958b22a8fL1-R53) [/#diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30R2-L4](https://github.com///issues/diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30R2-L4) [/#diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30R2-L4](https://github.com///issues/diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30R2-L4) [/#diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30L13-R18](https://github.com///issues/diff-2a882234ee87a20e5bdc36109545f979c9b3313c694a206c8a7a6729d13abd30L13-R18) [/#diff-39416987b344804f8dfe51bf213c6a6dcdfe6023a4a5be00198b42ed4651b3baL1-L45](https://github.com///issues/diff-39416987b344804f8dfe51bf213c6a6dcdfe6023a4a5be00198b42ed4651b3baL1-L45) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L76-R80](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L76-R80) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L76-R80](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L76-R80) [/#diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L129-R134](https://github.com///issues/diff-751947b923d78109cedf6eb2d5a61e16c1202abd95e44f863b2d3299322c8849L129-R134) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L9](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L9) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L9](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L9) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L22-L39](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52L22-L39) [/#diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R43-R53](https://github.com///issues/diff-578589c109770680a1e3424c7e675299a841454cc061f05c412b6817711bab52R43-R53) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR4](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR4) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR4](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR4) [/#diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR29-R32](https://github.com///issues/diff-1e8dcc5dd54f4b3de3e3e7466ca77e602ad478392643784019a706a6e5d187efR29-R32) [/#diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L445-R455](https://github.com///issues/diff-102e813e53f8cfc37f83036f9b38d0090d74af9240cc2271c8958e04e9ae9310L445-R455)
- [DEV-719] Shorten Default Passwords ([8358fb2](https://github.com/nclarity/backend/commit/8358fb29bb5127f10e7b33263e11f962a1ae60c5))
- [DEV-719] Shorten Default Passwords ([#547](https://github.com/nclarity/backend/issues/547)) [skip ci] ([a94ae7a](https://github.com/nclarity/backend/commit/a94ae7aaae71ab0b6bcf5d6ae7d0e87a5d192963))

# [2.26.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.25.0...@nclarity/contracts-v2.26.0) (2024-08-22)

### Bug Fixes

- [DEV-517] digest emails ([#541](https://github.com/nclarity/backend/issues/541)) ([5935350](https://github.com/nclarity/backend/commit/59353500b2c0c5958610d90e50a2b0ddd5056ec1))
- [DEV-688] freeze insight ([#543](https://github.com/nclarity/backend/issues/543)) ([76ddad7](https://github.com/nclarity/backend/commit/76ddad7ede76711c0305a37ed3c2946d27d04abb))
- [DEV-688] insight detail transformers ([#539](https://github.com/nclarity/backend/issues/539)) ([debaef8](https://github.com/nclarity/backend/commit/debaef83bca69000e3bd626824e975b39a0bb318))
- add transformers for relapses ([9a0e812](https://github.com/nclarity/backend/commit/9a0e812d43ae42554a4906e0b4e91553fa2c9f79))
- **api:** update statuses ([356fa97](https://github.com/nclarity/backend/commit/356fa973b4d673eb05c755d05461e6eacd841077))
- DEV-688 insight detail transformers ([#535](https://github.com/nclarity/backend/issues/535)) ([e1c2bd2](https://github.com/nclarity/backend/commit/e1c2bd29cae2cf04e6cf6d5e9e245ee5a3ceacf0))
- improve label calculation in use case ([ca32cb7](https://github.com/nclarity/backend/commit/ca32cb7d979c0dd2d864bc2a94a4f582248d503f))
- improve pulse creation ([#533](https://github.com/nclarity/backend/issues/533)) ([dae5578](https://github.com/nclarity/backend/commit/dae5578914d5cb39c770ea763e30114da2c87419))
- insights transformers ([192114c](https://github.com/nclarity/backend/commit/192114c4c2a6a253c48cf38ae57c6bd6a52626c1))
- **nexus:** change equipment status update ([208de34](https://github.com/nclarity/backend/commit/208de34087edab1d9dcb1aca2fecc1bfb905be33))
- update result for operation in upsert profile ([b38e9e2](https://github.com/nclarity/backend/commit/b38e9e20b0be5843b59f0d77b883bf5bc4f6323c))

### Features

- [DEV-517] digest emails ([#536](https://github.com/nclarity/backend/issues/536)) ([0cd6c06](https://github.com/nclarity/backend/commit/0cd6c068870365f3082250a8d2cbd099c09f6e6b))
- **all:** gateway devices support ([#529](https://github.com/nclarity/backend/issues/529)) [skip ci] ([9d13335](https://github.com/nclarity/backend/commit/9d1333592e92dc53f4197ff1cb07586f0222bc92))
- **api:** update device creation controller ([#531](https://github.com/nclarity/backend/issues/531)) ([cf3ba16](https://github.com/nclarity/backend/commit/cf3ba16cf9ea605ba46d2c01dd07d2da284cb50d))
- **api:** usecases and controllers for devices ([#530](https://github.com/nclarity/backend/issues/530)) [skip ci] ([34cb2cc](https://github.com/nclarity/backend/commit/34cb2ccd21958e840926704b9fabead8e9ffaac6))
- device id validation ([04e0a31](https://github.com/nclarity/backend/commit/04e0a310a573b8530f32c9bed7d1f9ab9c4cddfb))
- upsert equipment profile ([d518406](https://github.com/nclarity/backend/commit/d518406267236ccabcb84134788b42f8569529d0))
- upsert equipment profile ([#534](https://github.com/nclarity/backend/issues/534)) ([38c0bea](https://github.com/nclarity/backend/commit/38c0bea75e393bf3aa1754fccb826f7587683532))

# [2.25.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.24.0...@nclarity/contracts-v2.25.0) (2024-07-09)

### Bug Fixes

- apps env files ([#526](https://github.com/nclarity/backend/issues/526)) ([66efd3f](https://github.com/nclarity/backend/commit/66efd3f2fa0cbb76df133e875f4e6b71c4722add))

### Features

- **all:** change devices status reporting ([#527](https://github.com/nclarity/backend/issues/527)) ([d23834b](https://github.com/nclarity/backend/commit/d23834b36fd81100e3bf3bd945b649959da87c6f))
- **api:** ensure device status accuracy and update migration ([#528](https://github.com/nclarity/backend/issues/528)) ([f7dce18](https://github.com/nclarity/backend/commit/f7dce187fd5c36b3672b3a56aac26f5758ad1382))

# [2.24.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.23.0...@nclarity/contracts-v2.24.0) (2024-07-03)

### Bug Fixes

- [DEV-660] telemetry history export ([#514](https://github.com/nclarity/backend/issues/514)) ([975a68c](https://github.com/nclarity/backend/commit/975a68c32ea3557503c58523261d7476c68a5c78))
- add declaration to database package [skip ci] ([e0538ea](https://github.com/nclarity/backend/commit/e0538eac6f51b231d084e19d2782e5756c61861a))
- **api:** [DEV-668] add all equipment controller ([b7fd0a1](https://github.com/nclarity/backend/commit/b7fd0a1b25732c7b7a400e2f29fa4561e5854389))

### Features

- [DEV-636] created a migration to make address fields as optional ([#510](https://github.com/nclarity/backend/issues/510))[skip ci] ([1f52d7f](https://github.com/nclarity/backend/commit/1f52d7f4fdc983b12c0e237c78f07930a6e7b868))
- [DEV-638] created process to add a device to equipment ([944c077](https://github.com/nclarity/backend/commit/944c07772e0581f4fd88fef6c05756d04fe16c19))
- [DEV-638] created process to add a device to equipment ([#517](https://github.com/nclarity/backend/issues/517)) [skip ci] ([2bf94ee](https://github.com/nclarity/backend/commit/2bf94eedd82e88d11854958c0fbeb2c55771f4c7))
- [DEV-638] updated process to create a equipment and equipment profile without pulse id ([#512](https://github.com/nclarity/backend/issues/512))[skip ci] ([818b079](https://github.com/nclarity/backend/commit/818b07950baeeafda9031071c37200095c03910e))
- [DEV-654] device config set to 4 circuits ([620a3dd](https://github.com/nclarity/backend/commit/620a3dd4a68c45db5f26e4e041ba6a2bb4b0f5b5))
- add activity updater ([ee9fdea](https://github.com/nclarity/backend/commit/ee9fdea9a0b474d7a82ab6a4b0898186b4f40c73))
- add csvFileCreator activity ([89e8629](https://github.com/nclarity/backend/commit/89e862985655bf5961b2377d349b1be127b18afd))
- add entity coordination ([e5d76b4](https://github.com/nclarity/backend/commit/e5d76b460a1ffd958d344bf7c102bc8367a6fc63))
- add file exists validation ([fafdc12](https://github.com/nclarity/backend/commit/fafdc122a6612a77d4514ec28662de1ce3a7e957))
- add logging an optional checking ([5a1e09e](https://github.com/nclarity/backend/commit/5a1e09ec54c31d369bc26abdbffe6f26ed8333af))
- add more logs ([b8d2da2](https://github.com/nclarity/backend/commit/b8d2da273741310edd8234aa47e05cff1aa85091))
- add operations batches for telemetry history ([1dc4a75](https://github.com/nclarity/backend/commit/1dc4a75d85e24f43d82e1f4049b7aac13e390f18))
- add parent orchestration id ([66b2f4d](https://github.com/nclarity/backend/commit/66b2f4d29f0e1e5d2d66b80ff2ab32b0b5f22dbd))
- add request updater at orchestration start ([5925fd1](https://github.com/nclarity/backend/commit/5925fd1e81eab0625352ee64c558c5c12599aaa8))
- add table storage for exports ([615a3cd](https://github.com/nclarity/backend/commit/615a3cd1a70e1f45e2b198ce82a57a31e74827fd))
- add telemetry exporter app ([#521](https://github.com/nclarity/backend/issues/521)) ([2b6b0e7](https://github.com/nclarity/backend/commit/2b6b0e7e98073f4f530835f3343524e9dc5d01c0))
- **api:** [DEV-638] fixed error when deleting a pulse (now also deletes the record from the database) ([#513](https://github.com/nclarity/backend/issues/513))[skip ci] ([c339e5e](https://github.com/nclarity/backend/commit/c339e5e3a83a48daed182c41d65e24aadca0e394))
- **api:** [DEV-662] default insights creation to off ([#509](https://github.com/nclarity/backend/issues/509)) ([f0eb6e3](https://github.com/nclarity/backend/commit/f0eb6e3106fc103a2afa249aa1ff2cc8870e3e9c))
- **api:** [DEV-681] Can't resolve an insight ([#522](https://github.com/nclarity/backend/issues/522)) ([8fd98f6](https://github.com/nclarity/backend/commit/8fd98f6212eaa41c2a8410e07d97d30e32850e18))
- **api:** add endpoint to resolve alerts by insight id ([#515](https://github.com/nclarity/backend/issues/515)) ([57401b2](https://github.com/nclarity/backend/commit/57401b2683fd7f2932967e3026314e3ed36a29a3))
- change table name ([b506f90](https://github.com/nclarity/backend/commit/b506f90194fbc832d142b48cd84ac8d2682ee264))
- change table name and subOrchestrator call ([e04330c](https://github.com/nclarity/backend/commit/e04330c430323fb9dc024e29b0a4432ec2fcd052))
- change telemetry row builder ([d94757b](https://github.com/nclarity/backend/commit/d94757b251a6dd52966264a8c03981f2a9dc3c1e))
- create telemetry export script ([#519](https://github.com/nclarity/backend/issues/519)) ([9161460](https://github.com/nclarity/backend/commit/91614605dc0883100e1e0e1c046ff2c4a8411041))
- created a migration to make address fields as optional ([2e3c900](https://github.com/nclarity/backend/commit/2e3c9005dbffa86d3dcc9757a28f2170bcfb409f))
- fix table names ([311d7cd](https://github.com/nclarity/backend/commit/311d7cd85cbb4296fda328a16358b00cc92b3041))
- fix validation for updater activity ([f314ccc](https://github.com/nclarity/backend/commit/f314cccaf8a073204c018f5e2add1ac6a9ec97fa))
- fixed error when deleting a pulse (now also deletes the record from the database) ([97d5acc](https://github.com/nclarity/backend/commit/97d5acc329d00532e4c6873e481d01d8692779eb))
- improve add data api ([4185667](https://github.com/nclarity/backend/commit/4185667b777d14077b6f72848b4ebda2ca58f45f))
- improve blob paths extractor ([810c7ec](https://github.com/nclarity/backend/commit/810c7ec07f5efbb1e94133cd1300ee3a76b6ca38))
- improve csv concatenation ([516d418](https://github.com/nclarity/backend/commit/516d41800ece750f1128a0cac20e2e1c27f9fdb3))
- improve csv downloader file ([df7364d](https://github.com/nclarity/backend/commit/df7364dc01b388f867658b02005fb7bd101ad452))
- improve file upload ([d48146d](https://github.com/nclarity/backend/commit/d48146dc5ab56210cc0fb48256bfa5d5d6c85fac))
- improve header definitions ([e4a3728](https://github.com/nclarity/backend/commit/e4a3728f28f9113c93a783bf82622f562dad6811))
- improve telemetry history df app ([a184b18](https://github.com/nclarity/backend/commit/a184b184ec0f8a8ae2569a68a38c58f1d2d5a91f))
- **nexus:** add updateEquipmentCondition function ([#516](https://github.com/nclarity/backend/issues/516)) [skip ci] ([eeb2600](https://github.com/nclarity/backend/commit/eeb26009060b6d18e37c0c56a1e8f66079df57f8))
- **nexus:** improve csv writer activity ([1353a77](https://github.com/nclarity/backend/commit/1353a778943af0fd9b8f49c475b31aac61c7e133))
- **nexus:** update exporter context to manage multiple cache items ([4a8cb72](https://github.com/nclarity/backend/commit/4a8cb728a9bbf5b30895813c7bf3e04d6e2b03bb))
- ruleset creation defaulted to off ([63ef821](https://github.com/nclarity/backend/commit/63ef821e421bdcdbc78e799d8e65dfe6fc61b129))
- update suborchestrator yielding ([aa65b9c](https://github.com/nclarity/backend/commit/aa65b9c551c4389ff3dfab817b446da524125268))
- updated process to create a equipment and equipment profile without pulse id ([958ba0e](https://github.com/nclarity/backend/commit/958ba0e6e9b89fb7590a3fd09cb54aa33db2c6b5))

# [2.23.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.22.0...@nclarity/contracts-v2.23.0) (2024-06-03)

### Bug Fixes

- [DEV-660] telemetry history exports ([#505](https://github.com/nclarity/backend/issues/505)) [skip ci] ([ae4f0a0](https://github.com/nclarity/backend/commit/ae4f0a081e2c55157974c1a0037321fb33dbb5b5))
- **nexus:** improve getters for rules processor ([21532be](https://github.com/nclarity/backend/commit/21532be8b4621df7df642fc913e566d297fbcd66))

### Features

- [DEV-632] added schema shareAlerts an refactor reviewAndShareAlerts ([9758f51](https://github.com/nclarity/backend/commit/9758f51481b6cc5f63d7f2921e6363aea8874d87))
- [DEV-632] removed shareAlert file from schema folder ([aae5d6b](https://github.com/nclarity/backend/commit/aae5d6b243f892a000d11afb6bfc87e097c73068))
- **all:** add equipment notes to contracts ([#507](https://github.com/nclarity/backend/issues/507)) ([27047b3](https://github.com/nclarity/backend/commit/27047b300e9450524b2521888653460846e0fafc))
- **api:** [DEV-100]update controller for review alerts and send email ([3007594](https://github.com/nclarity/backend/commit/3007594f1e38d8316fb4e8472625ffbf191adf3b))
- **api:** [DEV-632] update controller for review alerts and send email ([#506](https://github.com/nclarity/backend/issues/506)) ([9979d55](https://github.com/nclarity/backend/commit/9979d551fa5fbfa4e04760d02c42c0e1a4ba6200))
- **nexus:** [DEV-100] fixed logic to update status o devices ([b45b660](https://github.com/nclarity/backend/commit/b45b660742dcc3579b13435bbf7180e6d6ae48a0))
- **nexus:** [DEV-100] pulse view ([#504](https://github.com/nclarity/backend/issues/504)) ([0af251c](https://github.com/nclarity/backend/commit/0af251cbc0aed27ebf5377fdaaf53574d4e6cfad))

# [2.22.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.21.0...@nclarity/contracts-v2.22.0) (2024-05-29)

### Features

- [DEV-599] telemetry data downsampling ([#494](https://github.com/nclarity/backend/issues/494)) [skip ci] ([e033d2e](https://github.com/nclarity/backend/commit/e033d2e9225c1f93ab7161d53830e0a104117e5e))
- **all:** add measures endpoint and data ([#495](https://github.com/nclarity/backend/issues/495)) [skip ci] ([e856574](https://github.com/nclarity/backend/commit/e8565745a411485a4c356e4761f541662d6280f5))
- **api:** create controllers and usecase ([#501](https://github.com/nclarity/backend/issues/501)) ([46fc78b](https://github.com/nclarity/backend/commit/46fc78b099d22a7d6412632dd142416bb0232309))
- **database:** [DEV-642] create notes fields ([#500](https://github.com/nclarity/backend/issues/500)) [skip ci] ([d1c9cdf](https://github.com/nclarity/backend/commit/d1c9cdf06e1fb7bc09c14e7e945aac8f3b844085))

# [2.21.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.20.0...@nclarity/contracts-v2.21.0) (2024-05-27)

### Features

- [DEV-100] pulse view ([#497](https://github.com/nclarity/backend/issues/497)) ([068eae7](https://github.com/nclarity/backend/commit/068eae7ec87fe41129e5e68e316b31041d88d3cf))
- added comment in migration file to update device status ([e2b42e0](https://github.com/nclarity/backend/commit/e2b42e0135647ee1ac4ce6bd3538781c5195ad77))
- added migration to update device status ([777d40e](https://github.com/nclarity/backend/commit/777d40e64ad8a4be315004b886926dddb475b31f))
- fixed export in index file of transactional and fixed logic for filters for get devices ([a789ba5](https://github.com/nclarity/backend/commit/a789ba508515602163fbd4e1afd6364277105f6e))

# [2.20.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.19.0...@nclarity/contracts-v2.20.0) (2024-05-27)

### Features

- add voltAbsConverter ([b93c8a5](https://github.com/nclarity/backend/commit/b93c8a5487be3d8e5d9ba2ad2e00482f75143ad0))
- **nexus:** add absolute value transformer for volts ([f0ca3b3](https://github.com/nclarity/backend/commit/f0ca3b39fc64bbb8e7e20808aba14460be3ecbd2))
- **nexus:** add absolute value transformer for volts ([#499](https://github.com/nclarity/backend/issues/499)) ([435235c](https://github.com/nclarity/backend/commit/435235cd15523a0e664408a1bb9e9c33c600336a))
- **nexus:** add mongo export to exporter ([1f4af23](https://github.com/nclarity/backend/commit/1f4af2344686c4f612e2861b03e01648ab9ee016))
- **nexus:** add mongo export to exporter ([#498](https://github.com/nclarity/backend/issues/498)) ([2a94817](https://github.com/nclarity/backend/commit/2a9481717e966af303bf16e28cb82d27c057f795))

# [2.19.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.18.0...@nclarity/contracts-v2.19.0) (2024-05-22)

### Features

- **packages:** [DEV-100] added new exports in index file of transactional ([#496](https://github.com/nclarity/backend/issues/496)) ([1423ebc](https://github.com/nclarity/backend/commit/1423ebcf4ad051e074a30bed55754233f2818c33))

# [2.18.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.17.0...@nclarity/contracts-v2.18.0) (2024-05-22)

### Bug Fixes

- [DEV-612] no heat insight ([#488](https://github.com/nclarity/backend/issues/488)) ([b6e3c12](https://github.com/nclarity/backend/commit/b6e3c12fe56275043d563a64fb6b2112bfa7b757))
- [DEV-646] update bulkGetRuleSetToEquipment to exclude inactive rules ([7d2974e](https://github.com/nclarity/backend/commit/7d2974eb56eb8a0217073a07fb83f051ec03570e))
- [DEV-655] coerce main user id ([4f40d95](https://github.com/nclarity/backend/commit/4f40d95c7daa4195887c2932f70ea289c75a8d2b))
- **all:** [DEV-649] update inservice field ([#479](https://github.com/nclarity/backend/issues/479)) ([b960b05](https://github.com/nclarity/backend/commit/b960b05c4569e3fe611637abce62a484a8270e45))
- **api:** [DEV-494] Notification Email Image ([#470](https://github.com/nclarity/backend/issues/470)) ([cfa1535](https://github.com/nclarity/backend/commit/cfa15357c4a391f78e98b33d6449bc4151e5eab2))
- **api:** [DEV-551] Changes don't display after Save, Equipment Page ([#469](https://github.com/nclarity/backend/issues/469)) ([75ca7c9](https://github.com/nclarity/backend/commit/75ca7c9d3c5dd480ec6e6349bfa485ec1287530e))
- **api:** update unassigned pulse unit ([#472](https://github.com/nclarity/backend/issues/472)) ([218c0d3](https://github.com/nclarity/backend/commit/218c0d3d4edb29bc3b9bb8aa777efdfbe1ee1cf0))
- deploy api config ([4f1374d](https://github.com/nclarity/backend/commit/4f1374d2d5f6d1a807dd0b4dcd4b4bfe91065b35))
- device config endpoint ([#474](https://github.com/nclarity/backend/issues/474)) ([d96c385](https://github.com/nclarity/backend/commit/d96c3857fd31080aeea97c9f1774ee87cb041eec))
- error of types ([e83e230](https://github.com/nclarity/backend/commit/e83e230a1c0ba334df5f91f61b56866cddb628bf))
- fixed prefix specified in getBuildingImageUseCase ([b018d32](https://github.com/nclarity/backend/commit/b018d325ed52f41fd975b499435eff0fa5679578))
- improve device config fetch ([4819d6f](https://github.com/nclarity/backend/commit/4819d6f5c5f6f9412a41e24664bf79d4605d698e))
- refactor updatedEquipmentUseCase ([ce3ad85](https://github.com/nclarity/backend/commit/ce3ad85e6e70f8f95c7a1ba123760bab0e07b9ed))

### Features

- add new endpoint for get all device ([26885ae](https://github.com/nclarity/backend/commit/26885ae4254da7d54174fe4cc3ecb5ee4162864e))
- add new endpoint to delete a device ([a43a887](https://github.com/nclarity/backend/commit/a43a8873eb4ab9f4a5eb7c652c6a24921471c962))
- added device model in schema.prisma ([763a93c](https://github.com/nclarity/backend/commit/763a93c70bc1fcf08029a6944791419faf4c6751))
- Added new migration migrate dat to device table ([0cd216f](https://github.com/nclarity/backend/commit/0cd216fd20cfb023bb906a9631be76b8172d9ca0))
- **analytics:** [DEV-627] add madb field ([#473](https://github.com/nclarity/backend/issues/473)) ([95698c6](https://github.com/nclarity/backend/commit/95698c61f89c4fdcd082abe750ac9f7b422740df))
- **api:** [DEV-100] add new endpoint for get all device ([#478](https://github.com/nclarity/backend/issues/478)) [skip ci] ([717c686](https://github.com/nclarity/backend/commit/717c686073389d79d8537f8f02d131253b0b9eaa))
- **api:** [DEV-100] add new endpoint to delete a device ([#481](https://github.com/nclarity/backend/issues/481)) [skip ci] ([5f696f8](https://github.com/nclarity/backend/commit/5f696f8f984396276efec7d0881d6355515f66e6))
- **api:** [DEV-100] added new endpoint to add a new devices ([#477](https://github.com/nclarity/backend/issues/477)) [skip ci] ([2121c52](https://github.com/nclarity/backend/commit/2121c52e38da7c2f736cf648fb82619b9f213624))
- **api:** [DEV-578] device permissions middleware ([#490](https://github.com/nclarity/backend/issues/490)) ([fe20bfc](https://github.com/nclarity/backend/commit/fe20bfc6797b2770ffed7ec59a8581b67389faff))
- fix device config ([2a7758b](https://github.com/nclarity/backend/commit/2a7758bd266b49c6865bc222711a73f0d0e8006b))
- **nexus:** [DEV-100] Added new feature to uodate the device in databaseExporter ([9224f92](https://github.com/nclarity/backend/commit/9224f92b631d0fe54cb9f1c388ccbaa36ba15e33))
- **nexus:** [DEV-100] Added new feature to update the device in databaseExporter ([#492](https://github.com/nclarity/backend/issues/492)) [skip ci] ([32cdd0f](https://github.com/nclarity/backend/commit/32cdd0fd41914c4ffe3630c346bb4ab060d86039))
- **nexus:** [DEV-626] 700 series exporter ([#471](https://github.com/nclarity/backend/issues/471)) ([7b4b4b8](https://github.com/nclarity/backend/commit/7b4b4b8ebc9302fd0c367129a8a6a1c6ef8a97ed))
- **nexus:** remove rules enqueuer for insights engine ([#487](https://github.com/nclarity/backend/issues/487)) [skip ci] ([5c5bfac](https://github.com/nclarity/backend/commit/5c5bfac355fce3e1f4809f011d9358c0af504256))
- **packages:** [DEV-100] added device model in schema.prisma ([#476](https://github.com/nclarity/backend/issues/476)) [skip ci] ([349cbfa](https://github.com/nclarity/backend/commit/349cbfa1db437e392b79fde6ab198eea5c11e3db))
- remove rules enqueuer ([23601aa](https://github.com/nclarity/backend/commit/23601aaf46aaca7c434cebeda0503df45ec9a86b))
- **signalwave:** [DEV-635] email subject change ([#491](https://github.com/nclarity/backend/issues/491)) ([3a0f088](https://github.com/nclarity/backend/commit/3a0f088860775a6fe8a22b420293dd391c2a52e8))

# [2.17.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.16.0...@nclarity/contracts-v2.17.0) (2024-04-18)

### Features

- [DEV-101] Change Account Owner ([#466](https://github.com/nclarity/backend/issues/466)) ([c0b2cac](https://github.com/nclarity/backend/commit/c0b2cacc496e275737038fef33fed56741f25517))
- [DEV-443] Equipment condition missing profile ([#467](https://github.com/nclarity/backend/issues/467)) ([99d92e9](https://github.com/nclarity/backend/commit/99d92e9a9dedb9734a62a5f064a627cc5fa9dd66))
- [DEV-443] equipment condition, missing profile ([#468](https://github.com/nclarity/backend/issues/468)) ([de9b12b](https://github.com/nclarity/backend/commit/de9b12bbaaf2800c95910d61e69e3b065fcbd114))
- [DEV-625] device type schema and migrations ([#465](https://github.com/nclarity/backend/issues/465)) [skip ci] ([c8198fa](https://github.com/nclarity/backend/commit/c8198fa6118797d0b7cadaae885ce7a8d4a3dbe6))
- Add mainUserId field to account edit use case and validation schema ([b68dd4e](https://github.com/nclarity/backend/commit/b68dd4e07c5db1bdb9e59e53cccfe00e466bb722))
- add new conditional in mongo migration equipment condition ([228b9d1](https://github.com/nclarity/backend/commit/228b9d16fd75f9a405ab7b7582327c56c90aba97))
- added initial equipment condition in setupEquipmentProfileUseCase ([0cd0ec2](https://github.com/nclarity/backend/commit/0cd0ec2d3dff18922082d412b6823108286b82f3))
- new endpoint for get superadmin users has been added ([69f9840](https://github.com/nclarity/backend/commit/69f9840b18c9e5524175e88b3cc43b5139cdf5d1))
- refactor getUsersByAccountAndRoleController to use zod for role validation in getUsersByAccountAndRole.ts and refactor getUsersByAccountAndRoleUseCase for get a users ([0d34b69](https://github.com/nclarity/backend/commit/0d34b6926dde41afb2728d7de82ca1de266c3238))
- refactor getUsersForAccountController to include role validation and use accountId from req.query ([09cbee8](https://github.com/nclarity/backend/commit/09cbee88ffaab1d05c33efc78b37d1c555f8ae60))
- refactor useCase and controller of the endpoint to get user by role ([728f9a9](https://github.com/nclarity/backend/commit/728f9a9f7c2102f72bd7839d00966990d027c9cf))
- rename a functions for get all users of account for a role and accountId ([80e1683](https://github.com/nclarity/backend/commit/80e1683a16fa7df81be3f7d7c415e7cef5929ac0))
- Update getUsersOfAccountUseCase to include UserStatus.Active filter ([5e75db7](https://github.com/nclarity/backend/commit/5e75db72aac683dddfcf21324f538e2770d8d92b))

# [2.17.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.16.0...@nclarity/contracts-v2.17.0) (2024-04-18)

### Features

- [DEV-101] Change Account Owner ([#466](https://github.com/nclarity/backend/issues/466)) ([c0b2cac](https://github.com/nclarity/backend/commit/c0b2cacc496e275737038fef33fed56741f25517))
- [DEV-443] Equipment condition missing profile ([#467](https://github.com/nclarity/backend/issues/467)) ([99d92e9](https://github.com/nclarity/backend/commit/99d92e9a9dedb9734a62a5f064a627cc5fa9dd66))
- [DEV-443] equipment condition, missing profile ([#468](https://github.com/nclarity/backend/issues/468)) ([de9b12b](https://github.com/nclarity/backend/commit/de9b12bbaaf2800c95910d61e69e3b065fcbd114))
- [DEV-625] device type schema and migrations ([#465](https://github.com/nclarity/backend/issues/465)) [skip ci] ([c8198fa](https://github.com/nclarity/backend/commit/c8198fa6118797d0b7cadaae885ce7a8d4a3dbe6))
- Add mainUserId field to account edit use case and validation schema ([b68dd4e](https://github.com/nclarity/backend/commit/b68dd4e07c5db1bdb9e59e53cccfe00e466bb722))
- add new conditional in mongo migration equipment condition ([228b9d1](https://github.com/nclarity/backend/commit/228b9d16fd75f9a405ab7b7582327c56c90aba97))
- added initial equipment condition in setupEquipmentProfileUseCase ([0cd0ec2](https://github.com/nclarity/backend/commit/0cd0ec2d3dff18922082d412b6823108286b82f3))
- new endpoint for get superadmin users has been added ([69f9840](https://github.com/nclarity/backend/commit/69f9840b18c9e5524175e88b3cc43b5139cdf5d1))
- refactor getUsersByAccountAndRoleController to use zod for role validation in getUsersByAccountAndRole.ts and refactor getUsersByAccountAndRoleUseCase for get a users ([0d34b69](https://github.com/nclarity/backend/commit/0d34b6926dde41afb2728d7de82ca1de266c3238))
- refactor getUsersForAccountController to include role validation and use accountId from req.query ([09cbee8](https://github.com/nclarity/backend/commit/09cbee88ffaab1d05c33efc78b37d1c555f8ae60))
- refactor useCase and controller of the endpoint to get user by role ([728f9a9](https://github.com/nclarity/backend/commit/728f9a9f7c2102f72bd7839d00966990d027c9cf))
- rename a functions for get all users of account for a role and accountId ([80e1683](https://github.com/nclarity/backend/commit/80e1683a16fa7df81be3f7d7c415e7cef5929ac0))
- Update getUsersOfAccountUseCase to include UserStatus.Active filter ([5e75db7](https://github.com/nclarity/backend/commit/5e75db72aac683dddfcf21324f538e2770d8d92b))

# [2.17.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.16.0...@nclarity/contracts-v2.17.0) (2024-04-18)

### Features

- [DEV-101] Change Account Owner ([#466](https://github.com/nclarity/backend/issues/466)) ([c0b2cac](https://github.com/nclarity/backend/commit/c0b2cacc496e275737038fef33fed56741f25517))
- [DEV-443] Equipment condition missing profile ([#467](https://github.com/nclarity/backend/issues/467)) ([99d92e9](https://github.com/nclarity/backend/commit/99d92e9a9dedb9734a62a5f064a627cc5fa9dd66))
- [DEV-443] equipment condition, missing profile ([#468](https://github.com/nclarity/backend/issues/468)) ([de9b12b](https://github.com/nclarity/backend/commit/de9b12bbaaf2800c95910d61e69e3b065fcbd114))
- [DEV-625] device type schema and migrations ([#465](https://github.com/nclarity/backend/issues/465)) [skip ci] ([c8198fa](https://github.com/nclarity/backend/commit/c8198fa6118797d0b7cadaae885ce7a8d4a3dbe6))
- Add mainUserId field to account edit use case and validation schema ([b68dd4e](https://github.com/nclarity/backend/commit/b68dd4e07c5db1bdb9e59e53cccfe00e466bb722))
- add new conditional in mongo migration equipment condition ([228b9d1](https://github.com/nclarity/backend/commit/228b9d16fd75f9a405ab7b7582327c56c90aba97))
- added initial equipment condition in setupEquipmentProfileUseCase ([0cd0ec2](https://github.com/nclarity/backend/commit/0cd0ec2d3dff18922082d412b6823108286b82f3))
- new endpoint for get superadmin users has been added ([69f9840](https://github.com/nclarity/backend/commit/69f9840b18c9e5524175e88b3cc43b5139cdf5d1))
- refactor getUsersByAccountAndRoleController to use zod for role validation in getUsersByAccountAndRole.ts and refactor getUsersByAccountAndRoleUseCase for get a users ([0d34b69](https://github.com/nclarity/backend/commit/0d34b6926dde41afb2728d7de82ca1de266c3238))
- refactor getUsersForAccountController to include role validation and use accountId from req.query ([09cbee8](https://github.com/nclarity/backend/commit/09cbee88ffaab1d05c33efc78b37d1c555f8ae60))
- refactor useCase and controller of the endpoint to get user by role ([728f9a9](https://github.com/nclarity/backend/commit/728f9a9f7c2102f72bd7839d00966990d027c9cf))
- rename a functions for get all users of account for a role and accountId ([80e1683](https://github.com/nclarity/backend/commit/80e1683a16fa7df81be3f7d7c415e7cef5929ac0))
- Update getUsersOfAccountUseCase to include UserStatus.Active filter ([5e75db7](https://github.com/nclarity/backend/commit/5e75db72aac683dddfcf21324f538e2770d8d92b))

# [2.16.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.15.0...@nclarity/contracts-v2.16.0) (2024-04-11)

### Features

- [DEV-443] equipment condition, missing profile ([#461](https://github.com/nclarity/backend/issues/461)) ([aad7409](https://github.com/nclarity/backend/commit/aad7409e7b66441f53c673c01c35f7cfbc13d0ba))
- [DEV-443] equipment condition, missing profile ([#462](https://github.com/nclarity/backend/issues/462)) ([234ac93](https://github.com/nclarity/backend/commit/234ac930ebbea5d1a4996b04992b5705f0cf8bc6))
- config for influxdb ([b611111](https://github.com/nclarity/backend/commit/b6111117e5901d7c87ed20c2f3bd7efd4ea3a5d9))
- update markAlertAsReviewedByIdUseCase for updated de equipment condition when all alerts triggered are reviewed ([3b21110](https://github.com/nclarity/backend/commit/3b2111075b35e381df6de5fc0e0d53fd284ab62c))
- update resolveAlertUseCase for update the equipment condition ([edcf65e](https://github.com/nclarity/backend/commit/edcf65e015d1301651dc240d8cfd50e8d673bf39))
- updated scale used for update the equipment condition in index transactional ([1ce3482](https://github.com/nclarity/backend/commit/1ce3482480321468e86f71308b126e52a681b2a9))

# [2.15.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.14.0...@nclarity/contracts-v2.15.0) (2024-04-02)

### Bug Fixes

- improve delta values calculation ([3e77bd8](https://github.com/nclarity/backend/commit/3e77bd8fb9eb4444288b7985f2710251fe978d90))

### Features

- [DEV-443] migration equipment-status-record ([#460](https://github.com/nclarity/backend/issues/460)) ([ea40c80](https://github.com/nclarity/backend/commit/ea40c80400b548f325bea1392934241f88e12eef))
- [DEV-484] Equipment Age ([#457](https://github.com/nclarity/backend/issues/457)) ([d9b4677](https://github.com/nclarity/backend/commit/d9b46776f39ac6bbf6e38e4be5e2a3ad8597d71c))
- [DEV-575] default rules copying ([#456](https://github.com/nclarity/backend/issues/456)) ([6232a47](https://github.com/nclarity/backend/commit/6232a47010e0aec02db7fb4942e1a1d47c8c5b47))
- Add CONDITION_SEVERITY to updateEquipmentCondition and processEhRules ([e7ece5f](https://github.com/nclarity/backend/commit/e7ece5f15f852160ef451a502ee8da1eba454bfa))
- add relapses to alert history ([91e5b2e](https://github.com/nclarity/backend/commit/91e5b2eb362817220e9845aeceb4f7aae7c819c4))
- add route to create notes by pulse ID in equipment-profile ([da7c743](https://github.com/nclarity/backend/commit/da7c74388fcc1840c41afeb8c3bde53089424819))
- added more time to transaction in the create equipment useCase and update condition logic ([96e583a](https://github.com/nclarity/backend/commit/96e583acf192f0bb22e75586a23fae2207e3795d))
- added new migration equipmentCondition for mongo db ([b151fc1](https://github.com/nclarity/backend/commit/b151fc1023bda8d20e6f883918cb7fa3b34c90d4))
- added ts_startStreaming value in updateEquipmentUseCase ([eb177b9](https://github.com/nclarity/backend/commit/eb177b911a5aed47f41b02cf37c33380f15c7d8a))
- **api:** [DEV-487] Equipment notes ([#454](https://github.com/nclarity/backend/issues/454)) ([9a427c9](https://github.com/nclarity/backend/commit/9a427c9dba4997a662dd34c27fbf5ebea5769d34))
- default rules copying done ([a8db6e1](https://github.com/nclarity/backend/commit/a8db6e1dd9095293e4df4525ffdbe2ff5bbbe5aa))
- delete unnecessary timeout in createEquipmentUseCase ([c598c75](https://github.com/nclarity/backend/commit/c598c75bafdf67a62958934a02104739f9105e2d))
- Equipment Condition, Missing Profile: ([d6de40f](https://github.com/nclarity/backend/commit/d6de40f28e3d6fbe59a1afa1e834491d2ceaf19d))
- fixed errors in equipment-status-record-updated migration, and added reviewed in schema.prisma database transactional ([6c4ef3e](https://github.com/nclarity/backend/commit/6c4ef3e4d3591d9bba872cb7e99f0bc64f5e833a))
- fixed errors in the queries for equipment-status-record-updated ([7c59a0a](https://github.com/nclarity/backend/commit/7c59a0ab11ad7f4e4c04208c0d973f2eec7d51a7))
- formatted schema.prisma in transactional folder ([7bce814](https://github.com/nclarity/backend/commit/7bce814cac459e73842125cd2eae646d99f24ae9))
- new endpoint to create or edit notes of equipment has been added ([81cd6c3](https://github.com/nclarity/backend/commit/81cd6c34a5b7d85543f7b8c5eb40b9b5037f58fe))
- refactor equipment condition migration ([02d9162](https://github.com/nclarity/backend/commit/02d9162b24b58c0e521db2ff9458d78a613473bf))
- refactor equipment condition severity mapping ([69b7647](https://github.com/nclarity/backend/commit/69b7647177502f37117a38409195b5743b4334e9))
- Refactor severity score calculation in equipmentCondition migration ([cf0abff](https://github.com/nclarity/backend/commit/cf0abff1b69cada7f4e530b4efc556950af2a447))
- refactor variable name in createNotes.ts ([94f3fdd](https://github.com/nclarity/backend/commit/94f3fdd7dd4e5e567a680dd79b2cf69f8724f48a))
- removed unnecessary casting in processEhRules impl.ts ([48bd1e5](https://github.com/nclarity/backend/commit/48bd1e594f234869740ea702f43c22d032dbee9c))
- script to copy rules to another corporate ([20004c4](https://github.com/nclarity/backend/commit/20004c473f9dd94dea7c02c9acea096a6ab04590))
- update equipment age ([a8424ea](https://github.com/nclarity/backend/commit/a8424ea38f78f020d79b47b26fc84259b1343dd7))
- update equipment condition and alert severity in migrations ([aa900ad](https://github.com/nclarity/backend/commit/aa900adf84fa902c29f6fa8ea54174d01d419634))
- update equipment condition values in migration ([d8bc4da](https://github.com/nclarity/backend/commit/d8bc4dac8e8ffb4eeacc134e7965c8fef93e2672))
- update EquipmentStatusRecord model in schema.prisma ([4112d3c](https://github.com/nclarity/backend/commit/4112d3c68f819e262a90a048d38fcc87121f9040))

# [2.14.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.13.0...@nclarity/contracts-v2.14.0) (2024-03-15)

### Bug Fixes

- [DEV-568] welcome email display name ([#445](https://github.com/nclarity/backend/issues/445)) ([ad8fb29](https://github.com/nclarity/backend/commit/ad8fb29eb3240cf8415603c23302fdecdddf685b))
- fix payload for schema ([019f898](https://github.com/nclarity/backend/commit/019f89825ef7988c15ba5e967e9a2d6f349e6555))
- improve account schema validation ([0f488b2](https://github.com/nclarity/backend/commit/0f488b2325668a10882864b186f9e0702baf5876))
- improved types for csvUrl ([88db564](https://github.com/nclarity/backend/commit/88db5640e14238ef89e5c32cbd9cbba3249787b3))
- **labs:** [DEV-550] fixed equipment profile circuits ([#436](https://github.com/nclarity/backend/issues/436)) ([8c9f2c7](https://github.com/nclarity/backend/commit/8c9f2c7d40dcc3c33ee07131d2fdef3849c98794))
- **labs:** [DEV-550] fixed equipment profile circuits ([#436](https://github.com/nclarity/backend/issues/436)) ([#437](https://github.com/nclarity/backend/issues/437)) ([ec0ad61](https://github.com/nclarity/backend/commit/ec0ad61243e3e37dade6371b5dc1ddd2d4b77861))
- **nexus:** [DEV-512] telemetry export failing ([#447](https://github.com/nclarity/backend/issues/447)) ([267f398](https://github.com/nclarity/backend/commit/267f39889f0ddfb54113900466940d42894b09e1))
- **nexus:** remove old code and improve file upload and download ([a7e48fc](https://github.com/nclarity/backend/commit/a7e48fc715e93c83220f11deef08bf066709554c))
- optional download link in telemetry history ([1ee668b](https://github.com/nclarity/backend/commit/1ee668bce3c77d83670d0b693cc0924323488476))
- send display name for email user creation ([15be19c](https://github.com/nclarity/backend/commit/15be19c12e27a999a75a24ceb2ec7e2b8964f8a0))

### Features

- [DEV-109] add operations data requests ([#448](https://github.com/nclarity/backend/issues/448)) ([f5879a0](https://github.com/nclarity/backend/commit/f5879a0f6df839c50d5c944a4155bb2c5f9fd0f0))
- [DEV-146] create requested refrigerants and controllers ([#446](https://github.com/nclarity/backend/issues/446)) ([73f74b0](https://github.com/nclarity/backend/commit/73f74b07c6a9cd108100e3a7943f22ab1ecf214c))
- [DEV-251] add device config ([#455](https://github.com/nclarity/backend/issues/455)) [skip ci] ([603840b](https://github.com/nclarity/backend/commit/603840b818765d6323d31981953cb6372ff99310))
- [DEV-557] deployment scripts ([#452](https://github.com/nclarity/backend/issues/452)) ([5e4f64a](https://github.com/nclarity/backend/commit/5e4f64aa4f0279443fbca869722174609446a3ec))
- [DEV-569] Review from equipment page ([#453](https://github.com/nclarity/backend/issues/453)) ([06a7529](https://github.com/nclarity/backend/commit/06a752920ed53c051766a2649d5f12086c04c053))
- [DEV-576] Add trip details for insights ([#451](https://github.com/nclarity/backend/issues/451)) ([490cd48](https://github.com/nclarity/backend/commit/490cd48f0f296c9a5010ac2f6660f29362ad2ce2))
- a new endpoint has been added for the review alert by Id. ([eee2cba](https://github.com/nclarity/backend/commit/eee2cbaae105de8fdf2b43d4086a7990eae3358f))
- Add alertTemperatureTransformer to list-alerts use case ([aa42627](https://github.com/nclarity/backend/commit/aa4262772eb88dcf98f3e8c6b947890c009c32dd))
- **api:** create controllers and use case for telemetry ([8f7a520](https://github.com/nclarity/backend/commit/8f7a52097ff365f65f4e40264348843bb6a30e63))
- **api:** create device config controller ([ae2d3c6](https://github.com/nclarity/backend/commit/ae2d3c6d879faf52a341171ad15eb03d58095c17))
- **api:** create use cases for device config ([46f0872](https://github.com/nclarity/backend/commit/46f0872506b6179556993f06cfd508b2b1524afe))
- **api:** improve config endpoint rules ([fe7c953](https://github.com/nclarity/backend/commit/fe7c95399bc6aff0dc2f3e33cbba38ac5705b0bd))
- **api:** modify list equipment to get selected data ([b359fdf](https://github.com/nclarity/backend/commit/b359fdf124516b8349a45b35fe79d3cd3aa9c048))
- **api:** update effects on equipment creation ([80c0a13](https://github.com/nclarity/backend/commit/80c0a1355ae875169d83c6c493e5479b43073f6b))
- **contracts:** add device initial config model ([4a00254](https://github.com/nclarity/backend/commit/4a00254a887c1e7e27314a39be681cd7c9b538c6))
- create requested refrigerants and controllers ([037a1db](https://github.com/nclarity/backend/commit/037a1dbaa45f28291e3c8e0bc7c38ada43014ee0))
- **database:** add migration for device config ([d3e2ee6](https://github.com/nclarity/backend/commit/d3e2ee6cd73594390ecaf8a0237a93fa4dcfff12))
- **database:** create model for device config ([f0e1858](https://github.com/nclarity/backend/commit/f0e1858f2fcc5b7d54c71eef6f0cfc561eedc64d))
- **database:** improve data definition and migration ([cd0bf10](https://github.com/nclarity/backend/commit/cd0bf10ee1f02277a6e3f0180f6a2ce7ca4f72ca))
- **database:** improve seeder and add ops user ([05e4436](https://github.com/nclarity/backend/commit/05e44366d87cba3604cec25e7686749c27e7fd7a))
- **db:** swap refrigerant info in defaults ([46cd205](https://github.com/nclarity/backend/commit/46cd205ef75f0e4ee07ac769afadeacb5e49c6fe))
- improve reliability for insights relations ([10baa2b](https://github.com/nclarity/backend/commit/10baa2bad0e20d84597ba448899218017a9529da))
- **nexus:** update update request ([d5fba79](https://github.com/nclarity/backend/commit/d5fba79cf68018b4cd3b1049a4b94c9dae707c8d))
- refactor "alertTemperatureTransformer" and use it in the right endpoint ([c691fe6](https://github.com/nclarity/backend/commit/c691fe6172d2c0a0b6069c4ec697c5f04563d36d))
- update package version ([a1ac143](https://github.com/nclarity/backend/commit/a1ac143c7a4a1d97f00e463f9765fe6511bda1fa))
- update workflow ([ea768aa](https://github.com/nclarity/backend/commit/ea768aa8c2461b9452a5493ed36656094fb2eb9a))

# [2.13.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.12.0...@nclarity/contracts-v2.13.0) (2024-02-21)

### Features

- **scripts:** add option to include db clients ([f9c53b8](https://github.com/nclarity/backend/commit/f9c53b8009ba55a1bbfe1acf53d1c5c6b1e50919))

# [2.12.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.11.0...@nclarity/contracts-v2.12.0) (2024-02-20)

### Features

- [DEV-512] telemetry history exports ([#444](https://github.com/nclarity/backend/issues/444)) ([eb62f38](https://github.com/nclarity/backend/commit/eb62f38b7b3d553fd5cb26fcd2db0cd03a71956d))
- added function to request data from old container ([2d2ed4f](https://github.com/nclarity/backend/commit/2d2ed4f49bcbaedc3490234675fa2c15fd201f62))
- added telemetry history storage service ([7c04601](https://github.com/nclarity/backend/commit/7c04601b96d43a5bba4deafa1bddd4ed7a62f686))
- added unique index on telemetry table ([119b74a](https://github.com/nclarity/backend/commit/119b74a2f58792f4f9cc6db756e79cea37aaaae6))
- added validation for telemetry history ([a50d13d](https://github.com/nclarity/backend/commit/a50d13d55e5d0fba345a8ec8e7d6443f7e7cefc5))
- **api:** add environment validation ([ae24004](https://github.com/nclarity/backend/commit/ae2400429c8fa02024d3228e24e74fccf8135646))
- **api:** create controllers and use case to manage exports ([d9a6a06](https://github.com/nclarity/backend/commit/d9a6a06d3bd66def08def9648fddbaddd350d50d))
- **api:** include the equipment in response ([eb4a092](https://github.com/nclarity/backend/commit/eb4a092107902464cf2344759aa40fa6d2463232))
- **api:** update use case to sort export requests ([a26b1d2](https://github.com/nclarity/backend/commit/a26b1d29f6e0d454fc879f8270ece9096388b8aa))
- create entity and exporter activities ([2d7813d](https://github.com/nclarity/backend/commit/2d7813dc009cab10d3647da19ad8b56f30d96c12))
- create skeleton for telemetry exporter orchestrator ([e29d62c](https://github.com/nclarity/backend/commit/e29d62c4032e61ff0d0c146862094bf21f326949))
- created controller to validate data for telemetry ([781ee7b](https://github.com/nclarity/backend/commit/781ee7bc65346d9492b39ac6f6260d3a8d7632db))
- created new copyblobfile function ([9ddb54f](https://github.com/nclarity/backend/commit/9ddb54f4fd5cdb0b0bec90baf1ad4ad619f21fa7))
- created parser to parquet ([5cd10fc](https://github.com/nclarity/backend/commit/5cd10fcf5cf50ae0de2a481ce435852d77d8c756))
- **database:** add migration for telemetry exports ([dcea949](https://github.com/nclarity/backend/commit/dcea949cab098e025785d185c674d65769e8f145))
- **database:** create upsert method for telemetry ([60bf005](https://github.com/nclarity/backend/commit/60bf005ff7c9178629e4f84a77d4aaff6fd392aa))
- **database:** created export request schema ([a2de9a9](https://github.com/nclarity/backend/commit/a2de9a9add9d1add370735c3a67719904342c303))
- **database:** update telemetry export definition ([4cae9f2](https://github.com/nclarity/backend/commit/4cae9f20ab81857f3f81b19a450e7dcfb6b4745f))
- **database:** updated exports in transactional ([40b9e42](https://github.com/nclarity/backend/commit/40b9e42434d8be672517b673da8952bad7b45fbe))
- **database:** updated migrations for telemetry exports ([32ee01a](https://github.com/nclarity/backend/commit/32ee01ada8d4b12086d890d68c6c0050a3ab2c27))
- decoding avro ([3eaa74e](https://github.com/nclarity/backend/commit/3eaa74ecd572c9ee4bd412901b132478ee7aa787))
- improved copy blob to convert from avro to parquet ([97516a6](https://github.com/nclarity/backend/commit/97516a666728dd3f2c5ba21c030f3d1e113f6be1))
- initial concept for telemetry exporter ([4ba7d91](https://github.com/nclarity/backend/commit/4ba7d91b855626ecc865b5e3843f70cbf35b6fa9))
- **logger:** added custom child logger ([3b8ef9b](https://github.com/nclarity/backend/commit/3b8ef9b226f4dac4ee6893367e0e0a398fefe80e))
- **nexus:** add sql exporter for telemetry history ([0d35244](https://github.com/nclarity/backend/commit/0d352448b80d6c6b23ef4c571a9a6486cbca17cc))
- **nexus:** create csv exporter for orchestration ([ccb8e96](https://github.com/nclarity/backend/commit/ccb8e969eda45cc83f68b49daae80950e3159de8))
- **nexus:** create error updater for telemetry orchestrator ([89ac930](https://github.com/nclarity/backend/commit/89ac93092375eae496c3ed406aa31b04333c2aad))
- **nexus:** create exports request updater ([7d719f3](https://github.com/nclarity/backend/commit/7d719f37dd8850d327e77236cabd9c5b6b4653e7))
- **nexus:** create notifiers and fixes ([5ee7b21](https://github.com/nclarity/backend/commit/5ee7b2182af8adb178c6bba543655609da8f8b73))
- **nexus:** exported telemetry data with calculated data ([06c8032](https://github.com/nclarity/backend/commit/06c8032dfa4cc023c7a3ace3be590f22fe73bf82))
- **nexus:** improve decoding avro ([2c96a6b](https://github.com/nclarity/backend/commit/2c96a6b1aee0d976e8ce50cdcd3d2538f932a2ff))
- **nexus:** improve sql exporter ([20d281d](https://github.com/nclarity/backend/commit/20d281dc092c859fb2b7b5042324b363afc8d188))
- **nexus:** improved csv exporter and notifications ([469a982](https://github.com/nclarity/backend/commit/469a982784d7b0b2422295bd2153a9184a72db5e))
- **nexus:** notifiers for exporters created ([64afc41](https://github.com/nclarity/backend/commit/64afc4199b0cd68da8a0c7722916adbfb0274f7d))
- **nexus:** refactored sql exporter to be a sub orchestrator ([b784417](https://github.com/nclarity/backend/commit/b7844171a50379f2586102ea3f22ace7176a035e))
- **nexus:** removed unused code ([e35319a](https://github.com/nclarity/backend/commit/e35319ae274d44b97b78c9324b28b84ed55d29e6))
- parquet converter ([381057b](https://github.com/nclarity/backend/commit/381057bf49348c5535ecb67795ea00a791773789))
- parquet converter ([f98b8e5](https://github.com/nclarity/backend/commit/f98b8e5ed19a32e1a85988ea1007f962e40b390f))
- parquet converter functions ([039e3d5](https://github.com/nclarity/backend/commit/039e3d592f80258e6b04e953baf2f6b6171e05b2))
- update config file for nexus app ([2dce84f](https://github.com/nclarity/backend/commit/2dce84fb033c73d3737b55469fa1c9aa7e220be5))
- updated function to use storage adapter ([814788c](https://github.com/nclarity/backend/commit/814788c8391e51a46bfda2e769ce77d52ba24dc1))
- wip ([4ab5e32](https://github.com/nclarity/backend/commit/4ab5e32e7f762ecbdc90af29b4672313485fddb1))

# [2.11.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.10.0...@nclarity/contracts-v2.11.0) (2024-02-14)

### Bug Fixes

- update button text in SingleAlertNotification component ([955a21a](https://github.com/nclarity/backend/commit/955a21a16800f3290392f8e8ed810a8710d104e2))

### Features

- [DEV-498] Email, change "Resolve alert" to "Review alert" ([#442](https://github.com/nclarity/backend/issues/442)) ([a264fcf](https://github.com/nclarity/backend/commit/a264fcffed6dd67b33933773aec072b5c3006149))

# [2.10.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.9.0...@nclarity/contracts-v2.10.0) (2024-02-08)

### Features

- [DEV-449] Notification bell APIs ([#440](https://github.com/nclarity/backend/issues/440)) ([2ca84d2](https://github.com/nclarity/backend/commit/2ca84d290e5272e0cfc498fa2450034a40483a17))
- Add UserNotificationsRouter to API routes ([4f5c6a6](https://github.com/nclarity/backend/commit/4f5c6a6c4d542db75ad3b3c2bf10cb1a6739a46f))
- add validation schema for update notification and updated controller ([9f86fed](https://github.com/nclarity/backend/commit/9f86fed85a8744a3d0ae5d55377c3d3802e33a3f))
- refactor notifications routes ([a744479](https://github.com/nclarity/backend/commit/a744479444a0498988b7628dad025bcc318ff271))

# [2.9.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.8.0...@nclarity/contracts-v2.9.0) (2024-02-02)

### Bug Fixes

- **labs:** [DEV-550] fixed equipment profile circuits ([#436](https://github.com/nclarity/backend/issues/436)) ([fd6c8d6](https://github.com/nclarity/backend/commit/fd6c8d6720d130a3c06099c29f11ecbca9a611a8))
- **labs:** fixed equipment profile circuits ([7deea0b](https://github.com/nclarity/backend/commit/7deea0b7075deb64b93bd0f222bee60ebdddee6b))

### Features

- [DEV-475] insights alerts chart use case ([#438](https://github.com/nclarity/backend/issues/438)) ([87398f6](https://github.com/nclarity/backend/commit/87398f6b604b5337dd775be6d1f24e26ff0138b4))
- [DEV-538] kpis calculations improvements ([#439](https://github.com/nclarity/backend/issues/439)) ([3553619](https://github.com/nclarity/backend/commit/35536195aab99b14ddffb2c6bb4d2cb5b69a26e7))
- added necessary controllers and use case ([cd4c9a9](https://github.com/nclarity/backend/commit/cd4c9a9a91121a0b0bebb371467c83af099667ff))
- changed to add the relapse timestamp instead triggering ([3dd4b66](https://github.com/nclarity/backend/commit/3dd4b6647856f4bd8e8c17ec5fe127774f3515ea))
- create controllers and use case ([c692c1d](https://github.com/nclarity/backend/commit/c692c1d70aa29eccb5a9114aad8a88d4b50a89b5))
- created better indexes for telemetry table ([c22d5be](https://github.com/nclarity/backend/commit/c22d5be62ca204ea785e6e7d47f7bf6ca8614bb1))
- improved kpis aggregation ([d4838d7](https://github.com/nclarity/backend/commit/d4838d7b31a4b5e5b3488fa0eea7af486da13327))
- improved stages definitions ([368f6e0](https://github.com/nclarity/backend/commit/368f6e032b2911cebd036702dc8daf7bc6f465c5))
- updated aggregation for plot bands ([38930d1](https://github.com/nclarity/backend/commit/38930d1a027c462ab1cd043dd0a585af1f5fd0c9))

# [2.8.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.7.0...@nclarity/contracts-v2.8.0) (2024-01-29)

### Bug Fixes

- removed relapses from listing usecase ([fb05678](https://github.com/nclarity/backend/commit/fb05678455f58e758edfd7b32cfb81f2167c182b))

### Features

- [DEV-414] location and equipment preferences ([#435](https://github.com/nclarity/backend/issues/435)) ([cac54f6](https://github.com/nclarity/backend/commit/cac54f66b3d58624730875a3042416dce3952c6a))
- added migration to update selected equipment ([248ed77](https://github.com/nclarity/backend/commit/248ed772a9115d19c25560fe92cad57f033509a8))
- created equipment preference spec ([0a64f18](https://github.com/nclarity/backend/commit/0a64f18ef27cd5309d8d0d3a76b2f8dbff94c75a))
- created equipment specification ([0d0469f](https://github.com/nclarity/backend/commit/0d0469f7f3c9e462b12daef8b49c68a44eb86519))
- updated user preferences use case ([594bb5f](https://github.com/nclarity/backend/commit/594bb5fd82865e5680a92bffdfbd2f36ffde0f96))

# [2.7.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.6.0...@nclarity/contracts-v2.7.0) (2024-01-26)

### Bug Fixes

- [DEV-533] updated weekly kpis to be an orchestrator ([#431](https://github.com/nclarity/backend/issues/431)) ([c0e96ff](https://github.com/nclarity/backend/commit/c0e96ff730b38f4f0aa2eac257940c3fc7910424))
- [DEV-535] updated equipment profile save ([52d17d1](https://github.com/nclarity/backend/commit/52d17d18c7ce49e49d13d7c15c93bdc012827ac2))
- [DEV-542] kpi values refactored ([#433](https://github.com/nclarity/backend/issues/433)) ([bf8e1cf](https://github.com/nclarity/backend/commit/bf8e1cf97fa8b69e05d9d15c81535754efa7667e))
- allowed the string values instead the uuid ([f17afd1](https://github.com/nclarity/backend/commit/f17afd15cfe1533b5150da5cc892d8c9a3f98cad))
- created adapters to get kpis in analytics database ([c68757e](https://github.com/nclarity/backend/commit/c68757ef9a1ff689dca0c11a6e044f2c5841d545))
- env variables fixes for nexus app ([#419](https://github.com/nclarity/backend/issues/419)) ([abd7649](https://github.com/nclarity/backend/commit/abd76492a00265c218ea1a26bd8a54db1c745045))
- fixed nan for weekly kpis updater ([dbc2848](https://github.com/nclarity/backend/commit/dbc2848f00dcb6555b92324d3f0f70448adfa34e))
- improved error handling for redis ([499228e](https://github.com/nclarity/backend/commit/499228e3e2de1025aaa5eb361271c9d38087c73f))
- improved weekly adapter logic ([7fdcdb6](https://github.com/nclarity/backend/commit/7fdcdb6a6d03a14440f8c499431e2e1d4585caae))
- updated return after for update equipment ([fdff325](https://github.com/nclarity/backend/commit/fdff3250322293d01f14db69038c43a199ee22be))
- updated weekly kpis to be an orchestrator ([a0f0125](https://github.com/nclarity/backend/commit/a0f0125e7ea8ad6d145b5119a2836531619ba3a2))

### Features

- [DEV-503] schemas for insights in analytics db ([#394](https://github.com/nclarity/backend/issues/394)) ([#398](https://github.com/nclarity/backend/issues/398)) ([7caaee6](https://github.com/nclarity/backend/commit/7caaee6be611837094e4dd8bf448b3e8e82853a9))
- added indexes for telemetry data in database ([312a4a1](https://github.com/nclarity/backend/commit/312a4a18fd99c22e59b185306fa196b400df943b))
- added labs app ([a5b36f5](https://github.com/nclarity/backend/commit/a5b36f55c3d07cfc36bdecd91bed636de769d7b9))
- removed console log from adapters ([7e5d3cb](https://github.com/nclarity/backend/commit/7e5d3cb767419cd0fee2752720ac4c4f230fac86))

# [2.6.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.5.1...@nclarity/contracts-v2.6.0) (2024-01-25)

### Features

- [DEV-415] update user preferences use case and validation schema ([#432](https://github.com/nclarity/backend/issues/432)) ([1fe4801](https://github.com/nclarity/backend/commit/1fe4801d7dfbcabe2c30604017c0fce45ab4ceb9))

## [2.5.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.5.0...@nclarity/contracts-v2.5.1) (2024-01-23)

### Bug Fixes

- heating insights ([3910af9](https://github.com/nclarity/backend/commit/3910af9f4e85189af3065f81553116b8aa9f2251))

# [2.5.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.4.0...@nclarity/contracts-v2.5.0) (2024-01-22)

### Bug Fixes

- [DEV-528] heating insights fix ([#428](https://github.com/nclarity/backend/issues/428)) ([bfa5668](https://github.com/nclarity/backend/commit/bfa5668fd34b435903453f3e2764252fa5b608f1))
- make delta values absolute values ([dc6c09e](https://github.com/nclarity/backend/commit/dc6c09ef2c4707c78d02197ea7cb0265530401fa))

### Features

- [DEV-150] weather cache in telemetry exporter ([#427](https://github.com/nclarity/backend/issues/427)) ([f69293a](https://github.com/nclarity/backend/commit/f69293ab652a07cb86889a7a38d4ba8bb98c00da))

# [2.4.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.3.2...@nclarity/contracts-v2.4.0) (2024-01-19)

### Features

- [DEV-454] digested alerst email ([#426](https://github.com/nclarity/backend/issues/426)) ([69d9ecd](https://github.com/nclarity/backend/commit/69d9ecd34a41bf66ee9e4bb2db1e6129ec50fb33))

## [2.3.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.3.1...@nclarity/contracts-v2.3.2) (2024-01-18)

### Bug Fixes

- updated contracts main file ([73549ce](https://github.com/nclarity/backend/commit/73549cea7ba6f46e62a5ef001523ccccb369762b))

## [2.3.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.3.0...@nclarity/contracts-v2.3.1) (2024-01-18)

### Bug Fixes

- changed export format for contracts ([31f8d6c](https://github.com/nclarity/backend/commit/31f8d6ceec1eae7cf552b60670db2d5a786f3daa))
- updated contracts publishing files ([d1e917d](https://github.com/nclarity/backend/commit/d1e917dda115c7dd36024c42e8539c6cefcf3b52))

# [2.3.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.2.3...@nclarity/contracts-v2.3.0) (2024-01-18)

### Features

- bundled contracts ([df26de6](https://github.com/nclarity/backend/commit/df26de61c380c3f4d54a9fb36eb8c7bb46f4d398))

## [2.2.3](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.2.2...@nclarity/contracts-v2.2.3) (2024-01-18)

### Bug Fixes

- added browser field in package json ([feea2ad](https://github.com/nclarity/backend/commit/feea2ada3e8e0fd9e25d507b680464e20517da03))

## [2.2.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.2.1...@nclarity/contracts-v2.2.2) (2024-01-18)

### Bug Fixes

- index browser for prisma types ([40f6cbd](https://github.com/nclarity/backend/commit/40f6cbda7061a017e40f4d822c00d40c33aa0025))

## [2.2.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.2.0...@nclarity/contracts-v2.2.1) (2024-01-18)

### Bug Fixes

- [DEV-526] kpis apis fixes ([#425](https://github.com/nclarity/backend/issues/425)) ([3c8118e](https://github.com/nclarity/backend/commit/3c8118e4d23e5be599d5ea978389fdf791a6a921))
- [DEV-528] changed the order of calling in rules enqueuer ([#423](https://github.com/nclarity/backend/issues/423)) ([bd51ae7](https://github.com/nclarity/backend/commit/bd51ae70eab1d07399745843ca5b3fcf1daa1151))
- added database package [skip ci] ([51561bf](https://github.com/nclarity/backend/commit/51561bfb7b98dcaf5503ede76a6785dfc1140d42))
- **nexus:** [DEV-528] added temperature transformer for telemetry getter ([#424](https://github.com/nclarity/backend/issues/424)) ([7624736](https://github.com/nclarity/backend/commit/7624736927ece1fc1eb1e12c9eabb8e83a02eb40))
- rules enqueuer refrigerant id ([e524348](https://github.com/nclarity/backend/commit/e5243480bde058adf45b7a0cb7f9425db4cf8af9))

# [2.2.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.1.1...@nclarity/contracts-v2.2.0) (2024-01-17)

### Features

- added analaytics db types ([bec258f](https://github.com/nclarity/backend/commit/bec258f648433bedb7f19610ff3e76a16ce0d8a2))
- added database package to contracts ([a062854](https://github.com/nclarity/backend/commit/a0628542f5269145fae0570467e58bb99b186887))

## [2.1.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.1.0...@nclarity/contracts-v2.1.1) (2024-01-17)

### Bug Fixes

- fixed dependecy issue for contracts ([5a2c2b6](https://github.com/nclarity/backend/commit/5a2c2b608cc613e9aabd353670d47e591666e613))
- fixed dependency requirement in contracs ([003b57b](https://github.com/nclarity/backend/commit/003b57ba8dc500c3a1c71faebcef6bd43e36e67e))

# [2.1.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v2.0.0...@nclarity/contracts-v2.1.0) (2024-01-16)

### Features

- changed build generation for contracts ([818537d](https://github.com/nclarity/backend/commit/818537d1b52d526a620c30515bd96736721928f9))

# [2.0.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.22.0...@nclarity/contracts-v2.0.0) (2024-01-16)

- feat!: types are now prisma generated ([1422aa5](https://github.com/nclarity/backend/commit/1422aa539fe1cb64044e63ecba7eae1a4fc980ba))

### BREAKING CHANGES

- new types added for the analytics database and types are now exported from the prisma generated

# [1.22.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.21.1...@nclarity/contracts-v1.22.0) (2024-01-15)

### Bug Fixes

- [DEV-528] heating insights fixes ([#422](https://github.com/nclarity/backend/issues/422)) ([037f9a1](https://github.com/nclarity/backend/commit/037f9a1b8633aa80ee69687da6f4b9bf7c0a16b1))

### Features

- updated chart data endpoint ([#421](https://github.com/nclarity/backend/issues/421)) ([3c6d488](https://github.com/nclarity/backend/commit/3c6d4883faf9e8ee37f1aea9cfaed50fffd661ec))

## [1.21.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.21.0...@nclarity/contracts-v1.21.1) (2024-01-09)

### Bug Fixes

- [DEV-509] process eh rules fix ([#415](https://github.com/nclarity/backend/issues/415)) ([fa3ccd6](https://github.com/nclarity/backend/commit/fa3ccd63f4735cf933ea7f93e6a60c435d287154))

# [1.21.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.20.0...@nclarity/contracts-v1.21.0) (2024-01-08)

### Bug Fixes

- [DEV-524] updated schema to cascade equipment deletion ([#414](https://github.com/nclarity/backend/issues/414)) ([14432cb](https://github.com/nclarity/backend/commit/14432cb7af769277d550f81467495b2692337a66))

### Features

- updated parsing for event hubs ([bc597aa](https://github.com/nclarity/backend/commit/bc597aa5dd5a4caa0e35cbfa722b6babc19c4b4f))

# [1.20.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.19.0...@nclarity/contracts-v1.20.0) (2024-01-04)

### Features

- [DEV-515] telemetry KPIs & Azure functions updates ([#412](https://github.com/nclarity/backend/issues/412)) ([7e511e0](https://github.com/nclarity/backend/commit/7e511e014af0aa4300537bd8101145b7dcd72b22))

# [1.19.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.18.0...@nclarity/contracts-v1.19.0) (2023-12-28)

### Features

- [DEV-507] insights repetition count ([#411](https://github.com/nclarity/backend/issues/411)) ([7700fb9](https://github.com/nclarity/backend/commit/7700fb9956e596060aa95313b60ad42dfaa70e57))

# [1.18.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.17.0...@nclarity/contracts-v1.18.0) (2023-12-26)

### Bug Fixes

- [DEV-401] improved from and to dates definition ([#405](https://github.com/nclarity/backend/issues/405)) ([4752c91](https://github.com/nclarity/backend/commit/4752c91e6bc8ea62d338073248ab938e0c2320aa))

### Features

- [DEV-473] create Route for refrigerants ([#408](https://github.com/nclarity/backend/issues/408)) ([58df533](https://github.com/nclarity/backend/commit/58df533fbe1bc4349bd74fc5316cf8dc313dd44a))
- reduced parallel queue items in function app ([#406](https://github.com/nclarity/backend/issues/406)) ([387eab9](https://github.com/nclarity/backend/commit/387eab936ee03fe1567f496439c5307232d0ad61))

# [1.17.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.16.0...@nclarity/contracts-v1.17.0) (2023-12-12)

### Features

- [DEV-401] fixed insight specification and improved logging ([#404](https://github.com/nclarity/backend/issues/404)) ([6b6c9f5](https://github.com/nclarity/backend/commit/6b6c9f5f905a425e07bcd6e8a32c062b98b90799))
- [DEV-481] changed convertible measures in charts ([#403](https://github.com/nclarity/backend/issues/403)) ([5a175c5](https://github.com/nclarity/backend/commit/5a175c53d1672c3d30c23827b46d8534541de877))

# [1.16.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.15.0...@nclarity/contracts-v1.16.0) (2023-11-30)

### Features

- [DEV-455] added insight backend ([#401](https://github.com/nclarity/backend/issues/401)) ([e56512a](https://github.com/nclarity/backend/commit/e56512a0d057f465977ba8a8beb03279368b9990))
- [DEV-508] device historic report ([#402](https://github.com/nclarity/backend/issues/402)) ([80c6944](https://github.com/nclarity/backend/commit/80c69446f793d46d5b36e425b36db9295cec5be4))

# [1.15.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.14.0...@nclarity/contracts-v1.15.0) (2023-11-17)

### Features

- [DEV-467] added migration for motor phase ([#400](https://github.com/nclarity/backend/issues/400)) ([a31ac6a](https://github.com/nclarity/backend/commit/a31ac6a7bd1c36fc32e185b5270012be7a4ce49b))

# [1.14.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.13.0...@nclarity/contracts-v1.14.0) (2023-11-16)

### Features

- [DEV-467] updated contracts to reflect condenser changes ([#399](https://github.com/nclarity/backend/issues/399)) ([68228d0](https://github.com/nclarity/backend/commit/68228d0c8efdf0c710a0b2aeb9f98f052906282e))

# [1.13.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.12.1...@nclarity/contracts-v1.13.0) (2023-11-16)

### Bug Fixes

- add delete route for user insights ([f15f08b](https://github.com/nclarity/backend/commit/f15f08bbe16cb0793d24be64412e92b347b0ebd8))

### Features

- [DEV-401] feat: add route to remove insight from user preferences ([#396](https://github.com/nclarity/backend/issues/396)) ([8e46ae2](https://github.com/nclarity/backend/commit/8e46ae20a57ccd38193b11e0d548891c5b337d5e))

## [1.12.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.12.0...@nclarity/contracts-v1.12.1) (2023-11-16)

### Bug Fixes

- [DEV-437] update database configuration and permissions ([#392](https://github.com/nclarity/backend/issues/392)) ([10391d4](https://github.com/nclarity/backend/commit/10391d4d6f521a7500e274281d024aba1e81c3de))
- update database configuration and permissions ([b299ddb](https://github.com/nclarity/backend/commit/b299ddb90f55bd322b2acba8b0a880e6632ec135))
- update database seed instructions in README.md ([2948313](https://github.com/nclarity/backend/commit/29483132bd24eaa38c062e7f1fd54ac11c6b2d80))

# [1.12.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.11.0...@nclarity/contracts-v1.12.0) (2023-11-16)

### Features

- [DEV-482] telemetry charts from analytics db ([#397](https://github.com/nclarity/backend/issues/397)) ([79050b0](https://github.com/nclarity/backend/commit/79050b05f75fcd50048315e7aa589534776bf909))

# [1.11.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.10.0...@nclarity/contracts-v1.11.0) (2023-11-13)

### Features

- [DEV-503] schemas for insights in analytics db ([#394](https://github.com/nclarity/backend/issues/394)) ([2244348](https://github.com/nclarity/backend/commit/2244348689b011055411ff398df9f60c739da6ce))

# [1.10.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.9.0...@nclarity/contracts-v1.10.0) (2023-11-03)

### Features

- [DEV-501] duplicated notifications ([#388](https://github.com/nclarity/backend/issues/388)) ([a40e177](https://github.com/nclarity/backend/commit/a40e177b1458e141157b9c6f1413b3b6d2a43601))
- fixed eh rules function processor ([b8a2adb](https://github.com/nclarity/backend/commit/b8a2adb52c07c563db628d5f86c6ab657ba82b4e))
- fixed equipment profile syncing ([1a14fc3](https://github.com/nclarity/backend/commit/1a14fc34f757363355e3d402f81f4685d92f84d6))
- removed unused function ([aaa37d8](https://github.com/nclarity/backend/commit/aaa37d8787d66dabf1294076221413a35a3b7eac))
- **signalwave:** removed the ab for single alerts email ([e827b66](https://github.com/nclarity/backend/commit/e827b6671c911a5f7c16d0e48cbb7d017bdfbe53))

# [1.9.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.8.0...@nclarity/contracts-v1.9.0) (2023-11-02)

### Features

- [DEV-431] schedule notifications ([#386](https://github.com/nclarity/backend/issues/386)) ([aa7b8f5](https://github.com/nclarity/backend/commit/aa7b8f59b6212359e0f48eb2b343fd37d5cc1787))
- added function to handle emails from queue ([cc015ab](https://github.com/nclarity/backend/commit/cc015ab68358598d31fdbe66068879ff64c8974e))
- added new apis for database adapter ([dbdd348](https://github.com/nclarity/backend/commit/dbdd348c0229312c15ac0d67f27cf1f2ada3f0fa))
- added new apis for email adapter ([6e3c90d](https://github.com/nclarity/backend/commit/6e3c90dd8863defd25de1aca7c6130fbdf0eb947))
- added queue storage adapter ([4d1954c](https://github.com/nclarity/backend/commit/4d1954c2f09e549f16d7e0d098ddef67cfdbe87f))
- cache adapter added and new apis ([c8bb106](https://github.com/nclarity/backend/commit/c8bb1067455699914cfde353d44f1232522f4c59))
- channels selection chain and specifications for preferences ([0797982](https://github.com/nclarity/backend/commit/0797982ba7c0d0a9f9c244dd282219a30abd2190))
- created implementations for email sending ([8851b9d](https://github.com/nclarity/backend/commit/8851b9d6c8a0b57af2b39c7a3cba3d4841e0e86e))
- created queue adapter ([31ac730](https://github.com/nclarity/backend/commit/31ac730ec4de8cff5b4e018306be5bb767ac6440))
- emails sending from adapter ([21e9fed](https://github.com/nclarity/backend/commit/21e9feda3eb245cdd3cfebb783de57b7edd3a5b0))
- improved types for queue adapter ([238f6f5](https://github.com/nclarity/backend/commit/238f6f551d5a8fb2777d7240a362ac710de8d2ba))
- modified time spec to also schedule next notification ([4d6e7a4](https://github.com/nclarity/backend/commit/4d6e7a4215dfa6cd16ebce575247d65728158cf5))
- multiple alerts email created ([b878c8e](https://github.com/nclarity/backend/commit/b878c8e19774becf4ea95a8babf057a35c439afe))
- new database api created ([7e0168f](https://github.com/nclarity/backend/commit/7e0168f41c7687835db519d67774ed1efe41d878))
- refactored handleWorkflowTrigger function ([f7392b6](https://github.com/nclarity/backend/commit/f7392b6a4facc38f51c792c20955d3d6bba24826))
- refactored handleWorkflowTriggerFunction ([828d7b9](https://github.com/nclarity/backend/commit/828d7b9b3739f1c6691cff6028cc53e661cc4de1))
- schedule notifications feature ([02d84b3](https://github.com/nclarity/backend/commit/02d84b33b67d38ab672a2596d92775bca1a57cd7))
- scheduled notifications enqueuer ([9310831](https://github.com/nclarity/backend/commit/93108317cec7b45f76c7d92c4a87b703fc7cfad9))
- unscheduled trigger handled ([3712b9f](https://github.com/nclarity/backend/commit/3712b9fbd5aa62a1d99fec512a6a5532a8ac9f4a))
- updated kind of notification in processRule impl ([e1c55db](https://github.com/nclarity/backend/commit/e1c55dbdf4cb7aaebeed4db191e1167860ad8075))
- wip for notifications ([62e6424](https://github.com/nclarity/backend/commit/62e64244f5abc5e719346669c2962d5c355a44d4))

# [1.9.0-dev.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.8.0...@nclarity/contracts-v1.9.0-dev.1) (2023-11-01)

### Features

- [DEV-431] schedule notifications ([#386](https://github.com/nclarity/backend/issues/386)) ([aa7b8f5](https://github.com/nclarity/backend/commit/aa7b8f59b6212359e0f48eb2b343fd37d5cc1787))
- added function to handle emails from queue ([cc015ab](https://github.com/nclarity/backend/commit/cc015ab68358598d31fdbe66068879ff64c8974e))
- added new apis for database adapter ([dbdd348](https://github.com/nclarity/backend/commit/dbdd348c0229312c15ac0d67f27cf1f2ada3f0fa))
- added new apis for email adapter ([6e3c90d](https://github.com/nclarity/backend/commit/6e3c90dd8863defd25de1aca7c6130fbdf0eb947))
- added queue storage adapter ([4d1954c](https://github.com/nclarity/backend/commit/4d1954c2f09e549f16d7e0d098ddef67cfdbe87f))
- cache adapter added and new apis ([c8bb106](https://github.com/nclarity/backend/commit/c8bb1067455699914cfde353d44f1232522f4c59))
- channels selection chain and specifications for preferences ([0797982](https://github.com/nclarity/backend/commit/0797982ba7c0d0a9f9c244dd282219a30abd2190))
- created implementations for email sending ([8851b9d](https://github.com/nclarity/backend/commit/8851b9d6c8a0b57af2b39c7a3cba3d4841e0e86e))
- created queue adapter ([31ac730](https://github.com/nclarity/backend/commit/31ac730ec4de8cff5b4e018306be5bb767ac6440))
- emails sending from adapter ([21e9fed](https://github.com/nclarity/backend/commit/21e9feda3eb245cdd3cfebb783de57b7edd3a5b0))
- improved types for queue adapter ([238f6f5](https://github.com/nclarity/backend/commit/238f6f551d5a8fb2777d7240a362ac710de8d2ba))
- modified time spec to also schedule next notification ([4d6e7a4](https://github.com/nclarity/backend/commit/4d6e7a4215dfa6cd16ebce575247d65728158cf5))
- multiple alerts email created ([b878c8e](https://github.com/nclarity/backend/commit/b878c8e19774becf4ea95a8babf057a35c439afe))
- new database api created ([7e0168f](https://github.com/nclarity/backend/commit/7e0168f41c7687835db519d67774ed1efe41d878))
- refactored handleWorkflowTrigger function ([f7392b6](https://github.com/nclarity/backend/commit/f7392b6a4facc38f51c792c20955d3d6bba24826))
- refactored handleWorkflowTriggerFunction ([828d7b9](https://github.com/nclarity/backend/commit/828d7b9b3739f1c6691cff6028cc53e661cc4de1))
- schedule notifications feature ([02d84b3](https://github.com/nclarity/backend/commit/02d84b33b67d38ab672a2596d92775bca1a57cd7))
- scheduled notifications enqueuer ([9310831](https://github.com/nclarity/backend/commit/93108317cec7b45f76c7d92c4a87b703fc7cfad9))
- unscheduled trigger handled ([3712b9f](https://github.com/nclarity/backend/commit/3712b9fbd5aa62a1d99fec512a6a5532a8ac9f4a))
- updated kind of notification in processRule impl ([e1c55db](https://github.com/nclarity/backend/commit/e1c55dbdf4cb7aaebeed4db191e1167860ad8075))
- wip for notifications ([62e6424](https://github.com/nclarity/backend/commit/62e64244f5abc5e719346669c2962d5c355a44d4))

# [1.8.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.7.0...@nclarity/contracts-v1.8.0) (2023-10-31)

### Features

- added new function to process rules ([46bf62f](https://github.com/nclarity/backend/commit/46bf62f0a074b72cf3e80177c69af3499739ff23))
- changed in binding for function ([a1a0020](https://github.com/nclarity/backend/commit/a1a0020fd2c33cf84662b94ee3123005827888f4))
- enabled event hub messages for rules processor ([#381](https://github.com/nclarity/backend/issues/381)) ([5b87b7e](https://github.com/nclarity/backend/commit/5b87b7e456b88e13c4f0fe839272824f99510315))

# [1.7.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.6.0...@nclarity/contracts-v1.7.0) (2023-10-19)

### Bug Fixes

- [DEV-493] duplicated pulses ([#378](https://github.com/nclarity/backend/issues/378)) ([0f12e36](https://github.com/nclarity/backend/commit/0f12e36bc71e2dde3a062be3b30603d9052e8371))

### Features

- updated schema to don't allow spaces and add messages ([0ff8d03](https://github.com/nclarity/backend/commit/0ff8d0326fdedb151ee2016031ad22acb311bacd))

# [1.6.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.5.0...@nclarity/contracts-v1.6.0) (2023-10-19)

### Bug Fixes

- [DEV-447] email notifications ([#374](https://github.com/nclarity/backend/issues/374)) ([b53ec6f](https://github.com/nclarity/backend/commit/b53ec6f37b7074b63f8f79aa29f616a955d5a7d9))
- [DEV-447] emails notifications ([#376](https://github.com/nclarity/backend/issues/376)) ([4a44b78](https://github.com/nclarity/backend/commit/4a44b781ffe12c5efc2c5cb71eb1a2739ec427af))
- [DEV-447] schedule preferences ([#371](https://github.com/nclarity/backend/issues/371)) ([9f00c4e](https://github.com/nclarity/backend/commit/9f00c4e2955a005d0f0e3080383a6946e138037b))

### Features

- availability schedule updates ([#365](https://github.com/nclarity/backend/issues/365)) ([41d7dcd](https://github.com/nclarity/backend/commit/41d7dcd8d3eb5401d4ffb983b25cf9231a0ba8b3))
- centered email button ([d551526](https://github.com/nclarity/backend/commit/d551526a7e60c25cf2f043b30b8898cdc871203a))
- email templates update ([ed296aa](https://github.com/nclarity/backend/commit/ed296aabdba6804bfa046b7d00b37f28947a604c))

# [1.4.0-dev.8](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.7...@nclarity/contracts-v1.4.0-dev.8) (2023-10-19)

### Features

- centered email button ([d551526](https://github.com/nclarity/backend/commit/d551526a7e60c25cf2f043b30b8898cdc871203a))

# [1.4.0-dev.7](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.6...@nclarity/contracts-v1.4.0-dev.7) (2023-10-18)

### Features

- email templates update ([ed296aa](https://github.com/nclarity/backend/commit/ed296aabdba6804bfa046b7d00b37f28947a604c))

# [1.4.0-dev.6](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.5...@nclarity/contracts-v1.4.0-dev.6) (2023-10-18)

### Bug Fixes

- [DEV-447] emails notifications ([#376](https://github.com/nclarity/backend/issues/376)) ([4a44b78](https://github.com/nclarity/backend/commit/4a44b781ffe12c5efc2c5cb71eb1a2739ec427af))

# [1.4.0-dev.5](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.4...@nclarity/contracts-v1.4.0-dev.5) (2023-10-17)

### Bug Fixes

- [DEV-447] email notifications ([#374](https://github.com/nclarity/backend/issues/374)) ([b53ec6f](https://github.com/nclarity/backend/commit/b53ec6f37b7074b63f8f79aa29f616a955d5a7d9))
- [DEV-447] schedule preferences ([#371](https://github.com/nclarity/backend/issues/371)) ([9f00c4e](https://github.com/nclarity/backend/commit/9f00c4e2955a005d0f0e3080383a6946e138037b))

# [1.4.0-dev.4](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.3...@nclarity/contracts-v1.4.0-dev.4) (2023-10-05)

### Features

- availability schedule updates ([#365](https://github.com/nclarity/backend/issues/365)) ([41d7dcd](https://github.com/nclarity/backend/commit/41d7dcd8d3eb5401d4ffb983b25cf9231a0ba8b3))
- creation for user preference ([f92fc65](https://github.com/nclarity/backend/commit/f92fc65a42b27643d08d34bce298807a026aa6dc))
- updated selected equipment for user pref ([5eedaad](https://github.com/nclarity/backend/commit/5eedaad422d8aa12168dc54903ba23686d52d515))

# [1.4.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.3.0...@nclarity/contracts-v1.4.0) (2023-10-03)

### Bug Fixes

- [DEV-420] added volts sensor optional field in model ([#359](https://github.com/nclarity/backend/issues/359)) ([02cd9b9](https://github.com/nclarity/backend/commit/02cd9b99bbfe03069bd0c9b2aaeef4818e2548c7))
- [DEV-439] equipment condition donut ([#357](https://github.com/nclarity/backend/issues/357)) ([e8e7d77](https://github.com/nclarity/backend/commit/e8e7d77155404648ed3db78f2b686195f77e403a))
- added unassigned pulse unit deletion ([2bf7403](https://github.com/nclarity/backend/commit/2bf740336e086edca3063f7b24cbd17e42a88627))
- added volts sensor optional field in model ([406c8e3](https://github.com/nclarity/backend/commit/406c8e3675f4a75dd48afbcf28ec8677b2316aeb))
- **api:** summary for corporate excluded repeated pulseids ([ac0db7b](https://github.com/nclarity/backend/commit/ac0db7b72a99c1974c4d42293995db55d96b3cb2))
- fixed user creation ([560a307](https://github.com/nclarity/backend/commit/560a307428bc2803e5027303389536db307d605e))

### Features

- added emails templates apps ([d655431](https://github.com/nclarity/backend/commit/d655431e69cbbc86759e9b22c4c818538d1086aa))
- **all:** [DEV-424] create phone number field ([#352](https://github.com/nclarity/backend/issues/352)) ([4507a84](https://github.com/nclarity/backend/commit/4507a84c116826d3086c78eb2eee918b0e044a3f))
- **all:** [DEV-427] notifications enablement ([#356](https://github.com/nclarity/backend/issues/356)) ([0bedee9](https://github.com/nclarity/backend/commit/0bedee992ded927f24fcd410dcba240da61e62b5))
- **all:** [DEV-447] email templates for single notification ([#362](https://github.com/nclarity/backend/issues/362)) ([10fb5ff](https://github.com/nclarity/backend/commit/10fb5ff38f55f3ea4f377a3d34395c0dd8553550))
- **all:** added adapters and schema for prisma ([16b45e2](https://github.com/nclarity/backend/commit/16b45e239f3eee667078f8a7f5118b5bb782d8c9))
- **all:** create phone number field ([1024036](https://github.com/nclarity/backend/commit/102403660d63d600936e805a191f5ee6f954f42f))
- **all:** created endpoints to manage user preferences ([2589e8b](https://github.com/nclarity/backend/commit/2589e8bb16ffca42872a26cc1d1ef998599ea480))
- **all:** created initial draft for notifications ([a742eaf](https://github.com/nclarity/backend/commit/a742eafa907f77a93fd0dbb0bec0638d88397762))
- **all:** removed template references ([ae9a1c6](https://github.com/nclarity/backend/commit/ae9a1c6a66e1223900614578554baf059768ddaa))
- api endpoint to see images ([497a878](https://github.com/nclarity/backend/commit/497a87854ba267808f4c35739ff902babbc9b09d))
- **api:** added api to get images ([43b1b1c](https://github.com/nclarity/backend/commit/43b1b1c1d858ca0b79f1e2bb97c49c9fb23e2afe))
- **api:** added custom methods to get the user and ops user ([da0b9bd](https://github.com/nclarity/backend/commit/da0b9bd47fd5f2add36cb4ab1c515df67b6ad0fa))
- **contracts:** updated contracts to include preferences ([976cc05](https://github.com/nclarity/backend/commit/976cc05291b8b01da1b8aeebd94999fd22c7c15f))
- creation for user preference ([f92fc65](https://github.com/nclarity/backend/commit/f92fc65a42b27643d08d34bce298807a026aa6dc))
- **database:** added analytics db schema ([629e25a](https://github.com/nclarity/backend/commit/629e25a9084f10e95d8d7125be09dea8f9627806))
- **database:** changed client path ([cd0a21c](https://github.com/nclarity/backend/commit/cd0a21c7ab23ed8c0b22d149cdfc53ccb0250d30))
- **database:** created new commands and improved client ([719615a](https://github.com/nclarity/backend/commit/719615a68b5ae918b18becc8e9445a87f3a97e4c))
- **database:** created new tables for notifications ([d64d29d](https://github.com/nclarity/backend/commit/d64d29d90df4b5036aa7e1d2ffc5e906f4238981))
- **database:** exporter telemetry for analytics ([911426c](https://github.com/nclarity/backend/commit/911426c5e4cc7d896d0cc1438a8f12427562cf86))
- **database:** new extension added ([ddbaf90](https://github.com/nclarity/backend/commit/ddbaf9049eeb7059aef3aa83cc533916c3416c60))
- **database:** removed notifications template model ([22c8fcb](https://github.com/nclarity/backend/commit/22c8fcb8d9a616b9a93e01c7d0995414914f994b))
- **database:** removed templateId reference ([0874815](https://github.com/nclarity/backend/commit/0874815bbccea7c6f13714f72a91d78459af4ead))
- **database:** removed unique constraint in telemetry ([de8a107](https://github.com/nclarity/backend/commit/de8a107a46defdbd9a1c785fc298c458bc2df494))
- **database:** updated commands for prisma ([b2de3bf](https://github.com/nclarity/backend/commit/b2de3bf5d149d984ed015d4aca714418f86786da))
- email template finished ([3d35172](https://github.com/nclarity/backend/commit/3d35172d8a7a149d2721402a4df8ff77447428df))
- **emails:** added new email template ([0e93995](https://github.com/nclarity/backend/commit/0e9399584d498a270bb66d7f11ec7be075259d50))
- **emails:** single alert email images ([4436f1d](https://github.com/nclarity/backend/commit/4436f1d6c21de494f000a343271ec42f2f1dd9b2))
- export emails ([511b7ec](https://github.com/nclarity/backend/commit/511b7ec557c01fee2565419525eb19873265b497))
- **functions:** [DEV-316] improved online status updates ([#350](https://github.com/nclarity/backend/issues/350)) ([5161013](https://github.com/nclarity/backend/commit/5161013538422e2d552c4f6751f36af57a7409be))
- **functions:** [DEV-421] rh clamper added ([#353](https://github.com/nclarity/backend/issues/353)) ([7157862](https://github.com/nclarity/backend/commit/71578624fbc533f5c3d812759c1087cc52c8dda5))
- **functions:** added clamper for sarh as well ([f700d24](https://github.com/nclarity/backend/commit/f700d2411e842d2c9841339f525c9c6794d5d62d))
- **functions:** added new signalwave application ([3e6e7b6](https://github.com/nclarity/backend/commit/3e6e7b6b0acf6d07e02c1c892a32d389297f2de2))
- **functions:** database exporter improvements ([a674855](https://github.com/nclarity/backend/commit/a67485598468607cf0b2199ac06c2b6c07f60da4))
- **functions:** improved online status updates ([a40949f](https://github.com/nclarity/backend/commit/a40949f23fba1bd7343ece38d8ef3cf24a9d7b6f))
- **functions:** removed mysqlclient file ([78e01fc](https://github.com/nclarity/backend/commit/78e01fc89deef91cdc67a0b0905c0dc6e29ab447))
- **functions:** removed unnecessary data for alert notification ([15f425f](https://github.com/nclarity/backend/commit/15f425f20865904dd0409a511e27c9854a07dd6a))
- **functions:** rh clamper added ([748c200](https://github.com/nclarity/backend/commit/748c200ab7a8f71b1f418fc4191cbf1c451fbe74))
- **functions:** updated telemetry creation api ([3511f62](https://github.com/nclarity/backend/commit/3511f62251e191d96944840f9edfac0a7f35c52e))
- scripts api fix ([7058e58](https://github.com/nclarity/backend/commit/7058e58cff3087c98fc6b7b1b7fa4fb4af2b9162))
- **scripts:** changed scripts package to copy new files ([59db1c3](https://github.com/nclarity/backend/commit/59db1c3f237d82eba51bf00c159cf32ea291862b))
- **scripts:** improved build function app process ([f0bed60](https://github.com/nclarity/backend/commit/f0bed60433fcab710db176826311217753aa8f56))
- **signalwave:** added email template to signalwave ([33e5da9](https://github.com/nclarity/backend/commit/33e5da9bd7ea49ee3dbb1527d2226f9ddbe272c4))
- **signalwave:** added random emails templates for single notification ([838bb55](https://github.com/nclarity/backend/commit/838bb555003f67e27e5d3920d6f5c8ed05b10b54))
- **signalwave:** created initial handlers ([1230aaa](https://github.com/nclarity/backend/commit/1230aaac813d9bb59f62e4c1a51e2e0074307bb6))
- **signalwave:** created new email adapters ([d6688ce](https://github.com/nclarity/backend/commit/d6688cece4ac940b9c5aadc5b14b8fb2822d26ac))
- **signalwave:** notifications enablement ([571bc2f](https://github.com/nclarity/backend/commit/571bc2f8c4fa76bbd9abd1ab71885f11573bda28))
- single alert modified to use api image ([f74fb98](https://github.com/nclarity/backend/commit/f74fb9822faea4d9def4eb729045bfe86d36aa6d))
- telemetry data for analytics db ([#361](https://github.com/nclarity/backend/issues/361)) ([776aade](https://github.com/nclarity/backend/commit/776aadecd2cda830b5368e4cf51380430900764d))
- updated selected equipment for user pref ([5eedaad](https://github.com/nclarity/backend/commit/5eedaad422d8aa12168dc54903ba23686d52d515))
- updated user preference creation schema ([0013351](https://github.com/nclarity/backend/commit/00133513d60f758d52e6738907057bbacb655e99))
- wip ([bdd4715](https://github.com/nclarity/backend/commit/bdd47151e10c40219489a6060c289d27cb87988d))

# [1.4.0-dev.3](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.2...@nclarity/contracts-v1.4.0-dev.3) (2023-10-03)

### Features

- updated user preference creation schema ([0013351](https://github.com/nclarity/backend/commit/00133513d60f758d52e6738907057bbacb655e99))

# [1.4.0-dev.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.4.0-dev.1...@nclarity/contracts-v1.4.0-dev.2) (2023-09-29)

### Bug Fixes

- fixed user creation ([560a307](https://github.com/nclarity/backend/commit/560a307428bc2803e5027303389536db307d605e))

# [1.4.0-dev.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.3.0...@nclarity/contracts-v1.4.0-dev.1) (2023-09-29)

### Bug Fixes

- [DEV-420] added volts sensor optional field in model ([#359](https://github.com/nclarity/backend/issues/359)) ([02cd9b9](https://github.com/nclarity/backend/commit/02cd9b99bbfe03069bd0c9b2aaeef4818e2548c7))
- [DEV-439] equipment condition donut ([#357](https://github.com/nclarity/backend/issues/357)) ([e8e7d77](https://github.com/nclarity/backend/commit/e8e7d77155404648ed3db78f2b686195f77e403a))
- added unassigned pulse unit deletion ([2bf7403](https://github.com/nclarity/backend/commit/2bf740336e086edca3063f7b24cbd17e42a88627))
- added volts sensor optional field in model ([406c8e3](https://github.com/nclarity/backend/commit/406c8e3675f4a75dd48afbcf28ec8677b2316aeb))
- **api:** summary for corporate excluded repeated pulseids ([ac0db7b](https://github.com/nclarity/backend/commit/ac0db7b72a99c1974c4d42293995db55d96b3cb2))

### Features

- added emails templates apps ([d655431](https://github.com/nclarity/backend/commit/d655431e69cbbc86759e9b22c4c818538d1086aa))
- **all:** [DEV-424] create phone number field ([#352](https://github.com/nclarity/backend/issues/352)) ([4507a84](https://github.com/nclarity/backend/commit/4507a84c116826d3086c78eb2eee918b0e044a3f))
- **all:** [DEV-427] notifications enablement ([#356](https://github.com/nclarity/backend/issues/356)) ([0bedee9](https://github.com/nclarity/backend/commit/0bedee992ded927f24fcd410dcba240da61e62b5))
- **all:** [DEV-447] email templates for single notification ([#362](https://github.com/nclarity/backend/issues/362)) ([10fb5ff](https://github.com/nclarity/backend/commit/10fb5ff38f55f3ea4f377a3d34395c0dd8553550))
- **all:** added adapters and schema for prisma ([16b45e2](https://github.com/nclarity/backend/commit/16b45e239f3eee667078f8a7f5118b5bb782d8c9))
- **all:** create phone number field ([1024036](https://github.com/nclarity/backend/commit/102403660d63d600936e805a191f5ee6f954f42f))
- **all:** created endpoints to manage user preferences ([2589e8b](https://github.com/nclarity/backend/commit/2589e8bb16ffca42872a26cc1d1ef998599ea480))
- **all:** created initial draft for notifications ([a742eaf](https://github.com/nclarity/backend/commit/a742eafa907f77a93fd0dbb0bec0638d88397762))
- **all:** removed template references ([ae9a1c6](https://github.com/nclarity/backend/commit/ae9a1c6a66e1223900614578554baf059768ddaa))
- api endpoint to see images ([497a878](https://github.com/nclarity/backend/commit/497a87854ba267808f4c35739ff902babbc9b09d))
- **api:** added api to get images ([43b1b1c](https://github.com/nclarity/backend/commit/43b1b1c1d858ca0b79f1e2bb97c49c9fb23e2afe))
- **api:** added custom methods to get the user and ops user ([da0b9bd](https://github.com/nclarity/backend/commit/da0b9bd47fd5f2add36cb4ab1c515df67b6ad0fa))
- **contracts:** updated contracts to include preferences ([976cc05](https://github.com/nclarity/backend/commit/976cc05291b8b01da1b8aeebd94999fd22c7c15f))
- **database:** added analytics db schema ([629e25a](https://github.com/nclarity/backend/commit/629e25a9084f10e95d8d7125be09dea8f9627806))
- **database:** changed client path ([cd0a21c](https://github.com/nclarity/backend/commit/cd0a21c7ab23ed8c0b22d149cdfc53ccb0250d30))
- **database:** created new commands and improved client ([719615a](https://github.com/nclarity/backend/commit/719615a68b5ae918b18becc8e9445a87f3a97e4c))
- **database:** created new tables for notifications ([d64d29d](https://github.com/nclarity/backend/commit/d64d29d90df4b5036aa7e1d2ffc5e906f4238981))
- **database:** exporter telemetry for analytics ([911426c](https://github.com/nclarity/backend/commit/911426c5e4cc7d896d0cc1438a8f12427562cf86))
- **database:** new extension added ([ddbaf90](https://github.com/nclarity/backend/commit/ddbaf9049eeb7059aef3aa83cc533916c3416c60))
- **database:** removed notifications template model ([22c8fcb](https://github.com/nclarity/backend/commit/22c8fcb8d9a616b9a93e01c7d0995414914f994b))
- **database:** removed templateId reference ([0874815](https://github.com/nclarity/backend/commit/0874815bbccea7c6f13714f72a91d78459af4ead))
- **database:** removed unique constraint in telemetry ([de8a107](https://github.com/nclarity/backend/commit/de8a107a46defdbd9a1c785fc298c458bc2df494))
- **database:** updated commands for prisma ([b2de3bf](https://github.com/nclarity/backend/commit/b2de3bf5d149d984ed015d4aca714418f86786da))
- email template finished ([3d35172](https://github.com/nclarity/backend/commit/3d35172d8a7a149d2721402a4df8ff77447428df))
- **emails:** added new email template ([0e93995](https://github.com/nclarity/backend/commit/0e9399584d498a270bb66d7f11ec7be075259d50))
- **emails:** single alert email images ([4436f1d](https://github.com/nclarity/backend/commit/4436f1d6c21de494f000a343271ec42f2f1dd9b2))
- export emails ([511b7ec](https://github.com/nclarity/backend/commit/511b7ec557c01fee2565419525eb19873265b497))
- **functions:** [DEV-316] improved online status updates ([#350](https://github.com/nclarity/backend/issues/350)) ([5161013](https://github.com/nclarity/backend/commit/5161013538422e2d552c4f6751f36af57a7409be))
- **functions:** [DEV-421] rh clamper added ([#353](https://github.com/nclarity/backend/issues/353)) ([7157862](https://github.com/nclarity/backend/commit/71578624fbc533f5c3d812759c1087cc52c8dda5))
- **functions:** added clamper for sarh as well ([f700d24](https://github.com/nclarity/backend/commit/f700d2411e842d2c9841339f525c9c6794d5d62d))
- **functions:** added new signalwave application ([3e6e7b6](https://github.com/nclarity/backend/commit/3e6e7b6b0acf6d07e02c1c892a32d389297f2de2))
- **functions:** database exporter improvements ([a674855](https://github.com/nclarity/backend/commit/a67485598468607cf0b2199ac06c2b6c07f60da4))
- **functions:** improved online status updates ([a40949f](https://github.com/nclarity/backend/commit/a40949f23fba1bd7343ece38d8ef3cf24a9d7b6f))
- **functions:** removed mysqlclient file ([78e01fc](https://github.com/nclarity/backend/commit/78e01fc89deef91cdc67a0b0905c0dc6e29ab447))
- **functions:** removed unnecessary data for alert notification ([15f425f](https://github.com/nclarity/backend/commit/15f425f20865904dd0409a511e27c9854a07dd6a))
- **functions:** rh clamper added ([748c200](https://github.com/nclarity/backend/commit/748c200ab7a8f71b1f418fc4191cbf1c451fbe74))
- **functions:** updated telemetry creation api ([3511f62](https://github.com/nclarity/backend/commit/3511f62251e191d96944840f9edfac0a7f35c52e))
- scripts api fix ([7058e58](https://github.com/nclarity/backend/commit/7058e58cff3087c98fc6b7b1b7fa4fb4af2b9162))
- **scripts:** changed scripts package to copy new files ([59db1c3](https://github.com/nclarity/backend/commit/59db1c3f237d82eba51bf00c159cf32ea291862b))
- **scripts:** improved build function app process ([f0bed60](https://github.com/nclarity/backend/commit/f0bed60433fcab710db176826311217753aa8f56))
- **signalwave:** added email template to signalwave ([33e5da9](https://github.com/nclarity/backend/commit/33e5da9bd7ea49ee3dbb1527d2226f9ddbe272c4))
- **signalwave:** added random emails templates for single notification ([838bb55](https://github.com/nclarity/backend/commit/838bb555003f67e27e5d3920d6f5c8ed05b10b54))
- **signalwave:** created initial handlers ([1230aaa](https://github.com/nclarity/backend/commit/1230aaac813d9bb59f62e4c1a51e2e0074307bb6))
- **signalwave:** created new email adapters ([d6688ce](https://github.com/nclarity/backend/commit/d6688cece4ac940b9c5aadc5b14b8fb2822d26ac))
- **signalwave:** notifications enablement ([571bc2f](https://github.com/nclarity/backend/commit/571bc2f8c4fa76bbd9abd1ab71885f11573bda28))
- single alert modified to use api image ([f74fb98](https://github.com/nclarity/backend/commit/f74fb9822faea4d9def4eb729045bfe86d36aa6d))
- telemetry data for analytics db ([#361](https://github.com/nclarity/backend/issues/361)) ([776aade](https://github.com/nclarity/backend/commit/776aadecd2cda830b5368e4cf51380430900764d))
- wip ([bdd4715](https://github.com/nclarity/backend/commit/bdd47151e10c40219489a6060c289d27cb87988d))

# [1.2.0-dev.8](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.2.0-dev.7...@nclarity/contracts-v1.2.0-dev.8) (2023-09-28)

### Features

- scripts api fix ([7058e58](https://github.com/nclarity/backend/commit/7058e58cff3087c98fc6b7b1b7fa4fb4af2b9162))

# [1.2.0-dev.7](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.2.0-dev.6...@nclarity/contracts-v1.2.0-dev.7) (2023-09-28)

### Features

- added emails templates apps ([d655431](https://github.com/nclarity/backend/commit/d655431e69cbbc86759e9b22c4c818538d1086aa))
- **all:** [DEV-447] email templates for single notification ([#362](https://github.com/nclarity/backend/issues/362)) ([10fb5ff](https://github.com/nclarity/backend/commit/10fb5ff38f55f3ea4f377a3d34395c0dd8553550))
- api endpoint to see images ([497a878](https://github.com/nclarity/backend/commit/497a87854ba267808f4c35739ff902babbc9b09d))
- **api:** added api to get images ([43b1b1c](https://github.com/nclarity/backend/commit/43b1b1c1d858ca0b79f1e2bb97c49c9fb23e2afe))
- email template finished ([3d35172](https://github.com/nclarity/backend/commit/3d35172d8a7a149d2721402a4df8ff77447428df))
- **emails:** added new email template ([0e93995](https://github.com/nclarity/backend/commit/0e9399584d498a270bb66d7f11ec7be075259d50))
- **emails:** single alert email images ([4436f1d](https://github.com/nclarity/backend/commit/4436f1d6c21de494f000a343271ec42f2f1dd9b2))
- export emails ([511b7ec](https://github.com/nclarity/backend/commit/511b7ec557c01fee2565419525eb19873265b497))
- **signalwave:** added email template to signalwave ([33e5da9](https://github.com/nclarity/backend/commit/33e5da9bd7ea49ee3dbb1527d2226f9ddbe272c4))
- **signalwave:** added random emails templates for single notification ([838bb55](https://github.com/nclarity/backend/commit/838bb555003f67e27e5d3920d6f5c8ed05b10b54))
- single alert modified to use api image ([f74fb98](https://github.com/nclarity/backend/commit/f74fb9822faea4d9def4eb729045bfe86d36aa6d))
- wip ([bdd4715](https://github.com/nclarity/backend/commit/bdd47151e10c40219489a6060c289d27cb87988d))

# [1.2.0-dev.6](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.2.0-dev.5...@nclarity/contracts-v1.2.0-dev.6) (2023-09-27)

# [1.3.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.2.0...@nclarity/contracts-v1.3.0) (2023-09-20)

### Bug Fixes

- [DEV-441] updated derived states getter ([#358](https://github.com/nclarity/backend/issues/358)) ([1d24cc5](https://github.com/nclarity/backend/commit/1d24cc5f90d6844f31967ad65e58cd1203022aaf))
- updated derived states getter ([12d08f4](https://github.com/nclarity/backend/commit/12d08f45c5076f1f435031414990f87b13603e48))

### Features

- [DEV-413] updated users endpoint ([d161a08](https://github.com/nclarity/backend/commit/d161a082cc50193a6a987bc27e08547368d63a6a))
- added more context for functions execution in process rule function ([7a7dc8f](https://github.com/nclarity/backend/commit/7a7dc8f0870a6bf5e33713f33a912d17d9c9679d))

# [1.2.0-dev.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.2.0-dev.1...@nclarity/contracts-v1.2.0-dev.2) (2023-08-29)

### Features

- **all:** [DEV-424] create phone number field ([#352](https://github.com/nclarity/backend/issues/352)) ([4507a84](https://github.com/nclarity/backend/commit/4507a84c116826d3086c78eb2eee918b0e044a3f))
- **all:** create phone number field ([1024036](https://github.com/nclarity/backend/commit/102403660d63d600936e805a191f5ee6f954f42f))
- **functions:** [DEV-316] improved online status updates ([#350](https://github.com/nclarity/backend/issues/350)) ([5161013](https://github.com/nclarity/backend/commit/5161013538422e2d552c4f6751f36af57a7409be))
- **functions:** improved online status updates ([a40949f](https://github.com/nclarity/backend/commit/a40949f23fba1bd7343ece38d8ef3cf24a9d7b6f))

# [1.2.0-dev.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.1.0...@nclarity/contracts-v1.2.0-dev.1) (2023-08-25)

### Bug Fixes

- [DEV-438] building missing ([#346](https://github.com/nclarity/backend/issues/346)) ([f21ae18](https://github.com/nclarity/backend/commit/f21ae180b04a7ac307e69fdd6551f40365e6ed14))
- **api:** fixed pagination poping on listing endpoints ([2295f1c](https://github.com/nclarity/backend/commit/2295f1c1af0f124ef620cf74df6796986a5b4faa))
- devices report fixes ([26ec5b9](https://github.com/nclarity/backend/commit/26ec5b9e0530da2591e74a54f938c514f086ecf7))
- devices report fixes ([#348](https://github.com/nclarity/backend/issues/348)) ([c009702](https://github.com/nclarity/backend/commit/c00970254bb0e88d262911a44498f7beda8152b2))

### Features

- **all:** [DEV-316] offline management for pulses ([#347](https://github.com/nclarity/backend/issues/347)) ([0fc385f](https://github.com/nclarity/backend/commit/0fc385ff404b845cf8074921f226c54435bb5fd8))
- **all:** changed status usage in endpoints ([a7ed883](https://github.com/nclarity/backend/commit/a7ed8837cd26dec7819b789a01bf41cdbf215116))
- **all:** new online implementation ([e993f9a](https://github.com/nclarity/backend/commit/e993f9a8a20bbd4054d8acdbd4afaa9afb834e0d))
- **api:** [DEV-436] pagination for listing endpoints ([#344](https://github.com/nclarity/backend/issues/344)) ([973e351](https://github.com/nclarity/backend/commit/973e3511f1e966fcf8286e9abf2570323d6cabc4))
- **api:** added pagination for customers, buildings and equipment ([c14bb20](https://github.com/nclarity/backend/commit/c14bb2002f01bf7320cde7a33f3c024a8c55fa01))
- **api:** fixed custom profile creation ([52bf964](https://github.com/nclarity/backend/commit/52bf96488f98052460151a8ce877b66f4bad1fac))
- **api:** fixed operations endpoint ([62e1414](https://github.com/nclarity/backend/commit/62e14147cf8f827163a00bb0a1096542031f2f36))
- **api:** pagination fixes ([13bf1a1](https://github.com/nclarity/backend/commit/13bf1a1e8d7265eef218747c48f59c85d1c45c4f))
- **api:** pagination in all listing endpoints ([56a1b42](https://github.com/nclarity/backend/commit/56a1b42c50584b3d6fbef362fe4645ecab600701))
- **contracts:** updated contracts ([a5af051](https://github.com/nclarity/backend/commit/a5af051f338bf12afb266ed49aaac6cad896b766))
- **database:** changed migrations order ([50eed42](https://github.com/nclarity/backend/commit/50eed42c28c50624cc2f9d0079865d8f259c1291))
- **database:** migration created ([3e30de2](https://github.com/nclarity/backend/commit/3e30de2b6a84954bf3e6b9ee81ff5acdf152293f))
- **functions:** added online and offline management for pulses ([79fce26](https://github.com/nclarity/backend/commit/79fce26a9ea50751a6af7ce66a5a867dc64d7bd0))

# [1.1.0-dev.4](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.1.0-dev.3...@nclarity/contracts-v1.1.0-dev.4) (2023-08-25)

### Features

- **all:** [DEV-316] offline management for pulses ([#347](https://github.com/nclarity/backend/issues/347)) ([0fc385f](https://github.com/nclarity/backend/commit/0fc385ff404b845cf8074921f226c54435bb5fd8))
- **all:** changed status usage in endpoints ([a7ed883](https://github.com/nclarity/backend/commit/a7ed8837cd26dec7819b789a01bf41cdbf215116))
- **all:** new online implementation ([e993f9a](https://github.com/nclarity/backend/commit/e993f9a8a20bbd4054d8acdbd4afaa9afb834e0d))
- **api:** [DEV-436] pagination for listing endpoints ([#344](https://github.com/nclarity/backend/issues/344)) ([973e351](https://github.com/nclarity/backend/commit/973e3511f1e966fcf8286e9abf2570323d6cabc4))
- **api:** added pagination for customers, buildings and equipment ([c14bb20](https://github.com/nclarity/backend/commit/c14bb2002f01bf7320cde7a33f3c024a8c55fa01))
- **api:** fixed operations endpoint ([62e1414](https://github.com/nclarity/backend/commit/62e14147cf8f827163a00bb0a1096542031f2f36))
- **api:** pagination in all listing endpoints ([56a1b42](https://github.com/nclarity/backend/commit/56a1b42c50584b3d6fbef362fe4645ecab600701))
- **contracts:** updated contracts ([a5af051](https://github.com/nclarity/backend/commit/a5af051f338bf12afb266ed49aaac6cad896b766))
- **database:** changed migrations order ([50eed42](https://github.com/nclarity/backend/commit/50eed42c28c50624cc2f9d0079865d8f259c1291))
- **database:** migration created ([3e30de2](https://github.com/nclarity/backend/commit/3e30de2b6a84954bf3e6b9ee81ff5acdf152293f))
- **functions:** added online and offline management for pulses ([79fce26](https://github.com/nclarity/backend/commit/79fce26a9ea50751a6af7ce66a5a867dc64d7bd0))

# [1.1.0](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0...@nclarity/contracts-v1.1.0) (2023-08-23)

### Features

- **all:** [DEV-395] buildings region ([#342](https://github.com/nclarity/backend/issues/342)) ([c92dd5d](https://github.com/nclarity/backend/commit/c92dd5d28d49b09b16fb285e4e4f68e565bf6964))
- **all:** [DEV-403] stabilization time for custom insights ([#341](https://github.com/nclarity/backend/issues/341)) ([f5fe7ac](https://github.com/nclarity/backend/commit/f5fe7ac27deaa2b48a2546360b672bc7f0aeea82))
- **all:** added region assignment per building ([754a647](https://github.com/nclarity/backend/commit/754a647e9460855d8e97a4558a80a5a5bbb48fc5))
- **all:** logger improvements ([499df4d](https://github.com/nclarity/backend/commit/499df4d63f0338a4d1182e23f282f35b7e6d23f1))
- **api:** sorting for event hub simulated messages ([55026f0](https://github.com/nclarity/backend/commit/55026f0ef54e81e17790a9abafd9a4ca5f2f5eec))
- **functions:** added computing for new derived states ([36d966a](https://github.com/nclarity/backend/commit/36d966a4295c59d30fafcfa2428524dc8d199353))
- **functions:** added heating state ([5cb8000](https://github.com/nclarity/backend/commit/5cb800076fb9eff6193b40d2a35162101a9409f6))
- **functions:** added telemetry effects ([d4999a7](https://github.com/nclarity/backend/commit/d4999a7682e6183b6371bf94b05a8d872fe8d776))
- **functions:** added time sensitive value ([90ac3f0](https://github.com/nclarity/backend/commit/90ac3f01f4a6844d3e2f727a9fa71690c292bd15))
- **functions:** documentation for functions ([efcae1d](https://github.com/nclarity/backend/commit/efcae1dbe02f6be69d1e3b3db7c617525e05f07f))
- **functions:** removed try block from set item adapter ([9ac3337](https://github.com/nclarity/backend/commit/9ac3337ec442bc408fefd4bbe049f3c614f531b1))
- **functions:** removed unnecessary logic for cooling state ([462864e](https://github.com/nclarity/backend/commit/462864e838965210abfd51caa266da3a1a4dba23))
- **functions:** terminology and documentation ([6e9ca31](https://github.com/nclarity/backend/commit/6e9ca31c1bb756cbd8eec757b17443651fc025c3))
- **packages:** updated wording for device state keys ([dc28da5](https://github.com/nclarity/backend/commit/dc28da5e5d02455db7efbfad77f6748549c6f221))

# [1.1.0-dev.3](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.1.0-dev.2...@nclarity/contracts-v1.1.0-dev.3) (2023-08-22)

### Features

- **all:** logger improvements ([499df4d](https://github.com/nclarity/backend/commit/499df4d63f0338a4d1182e23f282f35b7e6d23f1))

# [1.1.0-dev.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.1.0-dev.1...@nclarity/contracts-v1.1.0-dev.2) (2023-08-21)

### Features

- **all:** [DEV-395] buildings region ([#342](https://github.com/nclarity/backend/issues/342)) ([c92dd5d](https://github.com/nclarity/backend/commit/c92dd5d28d49b09b16fb285e4e4f68e565bf6964))
- **all:** added region assignment per building ([754a647](https://github.com/nclarity/backend/commit/754a647e9460855d8e97a4558a80a5a5bbb48fc5))

# [1.1.0-dev.1](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0...@nclarity/contracts-v1.1.0-dev.1) (2023-08-19)

### Features

- **all:** [DEV-403] stabilization time for custom insights ([#341](https://github.com/nclarity/backend/issues/341)) ([f5fe7ac](https://github.com/nclarity/backend/commit/f5fe7ac27deaa2b48a2546360b672bc7f0aeea82))
- **api:** sorting for event hub simulated messages ([55026f0](https://github.com/nclarity/backend/commit/55026f0ef54e81e17790a9abafd9a4ca5f2f5eec))
- **functions:** added computing for new derived states ([36d966a](https://github.com/nclarity/backend/commit/36d966a4295c59d30fafcfa2428524dc8d199353))
- **functions:** added heating state ([5cb8000](https://github.com/nclarity/backend/commit/5cb800076fb9eff6193b40d2a35162101a9409f6))
- **functions:** added telemetry effects ([d4999a7](https://github.com/nclarity/backend/commit/d4999a7682e6183b6371bf94b05a8d872fe8d776))
- **functions:** added time sensitive value ([90ac3f0](https://github.com/nclarity/backend/commit/90ac3f01f4a6844d3e2f727a9fa71690c292bd15))
- **functions:** documentation for functions ([efcae1d](https://github.com/nclarity/backend/commit/efcae1dbe02f6be69d1e3b3db7c617525e05f07f))
- **functions:** removed try block from set item adapter ([9ac3337](https://github.com/nclarity/backend/commit/9ac3337ec442bc408fefd4bbe049f3c614f531b1))
- **functions:** removed unnecessary logic for cooling state ([462864e](https://github.com/nclarity/backend/commit/462864e838965210abfd51caa266da3a1a4dba23))
- **functions:** terminology and documentation ([6e9ca31](https://github.com/nclarity/backend/commit/6e9ca31c1bb756cbd8eec757b17443651fc025c3))
- **packages:** updated wording for device state keys ([dc28da5](https://github.com/nclarity/backend/commit/dc28da5e5d02455db7efbfad77f6748549c6f221))

# 1.0.0 (2023-08-15)

### Bug Fixes

- [DEV-232] fixed the frequency undefined issue ([#261](https://github.com/nclarity/backend/issues/261)) ([71c1ac8](https://github.com/nclarity/backend/commit/71c1ac86b8368e5759928ff3d0ddf311e40af34e))
- [DEV-237] added lastsignstamp value in service ([#266](https://github.com/nclarity/backend/issues/266)) ([25273a0](https://github.com/nclarity/backend/commit/25273a01980a31c789797141cdc7e9501117fcf0))
- [DEV-245] fixed update for equipment ([#282](https://github.com/nclarity/backend/issues/282)) ([dbdc4d6](https://github.com/nclarity/backend/commit/dbdc4d6bff670cecfdf8e5a5b8c0ab6b0e7254a6))
- [DEV-248] added nullish operator in device config ([#285](https://github.com/nclarity/backend/issues/285)) ([7221130](https://github.com/nclarity/backend/commit/7221130439fb0755fc5bb07caffc69968b004406))
- [DEV-313] fixed system profile creation ([#301](https://github.com/nclarity/backend/issues/301)) ([b9bbd89](https://github.com/nclarity/backend/commit/b9bbd89746c28ed399e911884342afeea8ba4ed2))
- [DEV-335] sorting order fix ([c8f54f3](https://github.com/nclarity/backend/commit/c8f54f35f40e1cfdd8f964b9e8def3448c771fda))
- [DEV-352] calculations ([#318](https://github.com/nclarity/backend/issues/318)) ([c50c2f9](https://github.com/nclarity/backend/commit/c50c2f9730bf3fdcdeb48fae9763bc073a0304f7))
- [DEV-388] removed duplication of reviewed alerts ([#337](https://github.com/nclarity/backend/issues/337)) ([7d56fb6](https://github.com/nclarity/backend/commit/7d56fb60ed5db306b0e8b3c6babd881ece6d1c7b))
- [DEV-389] removed duplication of reviewed alerts ([8c70a86](https://github.com/nclarity/backend/commit/8c70a860a5c57eb0b913e9ed743fd7f8bab04c4f))
- [DEV-389] removed unique keys ([#336](https://github.com/nclarity/backend/issues/336)) ([ca7e28c](https://github.com/nclarity/backend/commit/ca7e28c0d55e0b9cda121fc47758df043938768f))
- added config fix for device config endpoint ([51f0288](https://github.com/nclarity/backend/commit/51f028840303f31290a7ce472dd633cb26d4fcea))
- added guard for customers deletion ([#278](https://github.com/nclarity/backend/issues/278)) ([ef4e39d](https://github.com/nclarity/backend/commit/ef4e39def74f9c51c1b704929e3fc7bc33e95e1f))
- **all:** [DEV-352] review insight feedback ([#317](https://github.com/nclarity/backend/issues/317)) ([24b3fad](https://github.com/nclarity/backend/commit/24b3fad47145183fc42b0d7c8601e19f54ba13bc))
- **all:** fixed types for use cases ([#269](https://github.com/nclarity/backend/issues/269)) ([7090275](https://github.com/nclarity/backend/commit/7090275e3b1d1ba9af89121b82beff939a336ab1)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- allowed headers cors ([1279668](https://github.com/nclarity/backend/commit/1279668ba3945583c4f64c4beb5a3ba40d43344e))
- **all:** refactoring buildFunctionApp script ([#321](https://github.com/nclarity/backend/issues/321)) ([cc26757](https://github.com/nclarity/backend/commit/cc2675776c194a609d93d5237a36e45b36ca67cc))
- **api:** [DEV-183] fixed user creation ([#233](https://github.com/nclarity/backend/issues/233)) ([1426174](https://github.com/nclarity/backend/commit/142617480cd393602d339e27d69aa29c1e3e433f))
- **api:** [DEV-215] updated getallpulses service ([#248](https://github.com/nclarity/backend/issues/248)) ([a2ad24f](https://github.com/nclarity/backend/commit/a2ad24faa192771187d7edc0a4ac0296bad370d2))
- **api:** [DEV-235] no dashboard data ([#268](https://github.com/nclarity/backend/issues/268)) ([eed96ba](https://github.com/nclarity/backend/commit/eed96ba20447243fa22fcff4218be5732fc696a9))
- **api:** [DEV-235] updated default values in condenser ([#265](https://github.com/nclarity/backend/issues/265)) ([7851617](https://github.com/nclarity/backend/commit/785161753f2d8ce472be0cfdfd47b2e59af614c1))
- **api:** [DEV-236] updated default values in condenser ([#267](https://github.com/nclarity/backend/issues/267)) ([bf5eb25](https://github.com/nclarity/backend/commit/bf5eb25355ddabacf44ac73ff9c04d13e4928f60))
- **api:** fixed abort signal in bundle ([7bcba10](https://github.com/nclarity/backend/commit/7bcba1026f04ba0b3cb64fb8b0a57eb366f20a62))
- **api:** fixed account user creation for dev ([bcea6e2](https://github.com/nclarity/backend/commit/bcea6e2cd6b9556ca977875a06aa81a32ecf7b7a))
- **api:** fixed ecosystem file ([c2fe2cc](https://github.com/nclarity/backend/commit/c2fe2cc049e8c8921a2cff4e68af4a7b58aabf7c))
- **api:** fixed the token payload for the verify function ([#247](https://github.com/nclarity/backend/issues/247)) ([48f4504](https://github.com/nclarity/backend/commit/48f4504a07c997e9f665173c1552065515d95a4e))
- **api:** fixed the user creation with the group ([da25894](https://github.com/nclarity/backend/commit/da2589468a4cdccf8558a86e6fbae5fa4317e8c5))
- **api:** improved salt generation ([df8faf2](https://github.com/nclarity/backend/commit/df8faf2d839813823a9cf5f10429f7af80dd1e81))
- **charts:** added null chart point when there is no data ([abff391](https://github.com/nclarity/backend/commit/abff391c21ee37522d3c447aab7baba798abdfee))
- **contracts:** [DEV-180] updated package json in contracts ([#229](https://github.com/nclarity/backend/issues/229)) ([c20175e](https://github.com/nclarity/backend/commit/c20175ef5c468e31e233b7ca667e090fbf8c8f6b))
- **contracts:** removed unnecessary command in package.json ([5a37db1](https://github.com/nclarity/backend/commit/5a37db1cb65772a4c5692d0adcd67d44248f2c62))
- cors middleware ([0ddac58](https://github.com/nclarity/backend/commit/0ddac5870ebee45feb768f3e678b5cee08419993))
- cors middleware ([dbbdb47](https://github.com/nclarity/backend/commit/dbbdb4786fee211f788465d5b7514becb07eaa5c))
- cors origin ([85b4e9f](https://github.com/nclarity/backend/commit/85b4e9f59489da9759b64ff64be974fc6cc86c12))
- deployment and prisma generation for function ([#272](https://github.com/nclarity/backend/issues/272)) ([26b2d75](https://github.com/nclarity/backend/commit/26b2d750d41119a832084545b883b4856ec45f6f))
- deployment script function app ([66d3af4](https://github.com/nclarity/backend/commit/66d3af4da931849afa1c2807241c1fc96a943eaf))
- email template id for rtu scorecard sharing ([#65](https://github.com/nclarity/backend/issues/65)) ([5ab5666](https://github.com/nclarity/backend/commit/5ab56669dce5e00eebd2bd178fcc9ca57629b817))
- excluded local settings json ([7e3c5fd](https://github.com/nclarity/backend/commit/7e3c5fd9165de4488e7a59b4e5ca3f30dcf48e46))
- fixed account creation script ([#281](https://github.com/nclarity/backend/issues/281)) ([c2ab073](https://github.com/nclarity/backend/commit/c2ab0730e9115d30850f65d72fcefee15429622e))
- **functions:** added multiple any for rules ([c046a16](https://github.com/nclarity/backend/commit/c046a169505903824327e0b589a7f5f70ac63da3))
- **functions:** improved transformations ([#309](https://github.com/nclarity/backend/issues/309)) ([5b67137](https://github.com/nclarity/backend/commit/5b671374c4cd901eb32683aa709f0d28f9808bb7))
- module resolution for contracts package ([#273](https://github.com/nclarity/backend/issues/273)) ([a564fd0](https://github.com/nclarity/backend/commit/a564fd04ad7a9945e706847523f726b42e5d4a17))
- refrigerants seeder ([#306](https://github.com/nclarity/backend/issues/306)) ([1f38a64](https://github.com/nclarity/backend/commit/1f38a6461d1a0e753d0fa4cd1365648cc7dbb671))
- removed cors config ([f0bf749](https://github.com/nclarity/backend/commit/f0bf749dbaf738d1fe98dd55693c03327ea345e8))
- template id for rtu scorecard ([#62](https://github.com/nclarity/backend/issues/62)) ([f2d5e9f](https://github.com/nclarity/backend/commit/f2d5e9f15158432baf24b82b41a39bd988fe9e46))
- tsc errors ([f8a2c89](https://github.com/nclarity/backend/commit/f8a2c896f349101fccd61230e0e62cf8c6d512ce))
- updated regions in auth context and edit customer ([#308](https://github.com/nclarity/backend/issues/308)) ([b664cd0](https://github.com/nclarity/backend/commit/b664cd052b7ff019dae2b3e972ea979460f8dc6a))

- feat(contracts)!: updated contracts based in prisma output ([68248ed](https://github.com/nclarity/backend/commit/68248ed0957f302b987c86997ad97984837ae00d))
- feat(contracts)!: changed from commonjs to esm ([c5b136c](https://github.com/nclarity/backend/commit/c5b136c7c8108b2a9625726781c10d276238effa))

### Features

- [DEV-177] add logger package ([#238](https://github.com/nclarity/backend/issues/238)) ([a008bc8](https://github.com/nclarity/backend/commit/a008bc8af3ec1962ca40e12dc5a1ff69de869526))
- [DEV-177] deployment to fix jwt token ([#236](https://github.com/nclarity/backend/issues/236)) ([13c1531](https://github.com/nclarity/backend/commit/13c153111f31ab0aaedbf56ff7e98be3dc6a0947))
- [DEV-177] no app deployment ([#237](https://github.com/nclarity/backend/issues/237)) ([14474f9](https://github.com/nclarity/backend/commit/14474f97a6ce9ebae44bdae4523d1e9ca5ae7d43))
- [DEV-177] updated contracts ([#240](https://github.com/nclarity/backend/issues/240)) ([dcdc8f5](https://github.com/nclarity/backend/commit/dcdc8f5b336656c53dd117365f3f21ddfe595e55))
- **accounts:** account deletion enhanced ([2586297](https://github.com/nclarity/backend/commit/258629752979a17f475ec30f5c9729514eceba81))
- **accounts:** deleted info from st-tech table ([0867e4f](https://github.com/nclarity/backend/commit/0867e4fd7222f1d7a96b7b058c79a3d03e0e3f78))
- added systemProfile model ([e165498](https://github.com/nclarity/backend/commit/e16549838c4046f749d55877f4ce66bd55d8b219))
- **all:** [DEV-268] phase based telemetry values sum ([#296](https://github.com/nclarity/backend/issues/296)) ([2c07cf6](https://github.com/nclarity/backend/commit/2c07cf68377502ef5ace7efc47f7cd1466678343))
- **all:** [DEV-270] edit insight ([#314](https://github.com/nclarity/backend/issues/314)) ([bedc5ed](https://github.com/nclarity/backend/commit/bedc5ed5b95ac0c20d8ffb4b360364b052861cee))
- **all:** [DEV-305] delete insight ([#313](https://github.com/nclarity/backend/issues/313)) ([a750d48](https://github.com/nclarity/backend/commit/a750d4898a1afb7f08fc3a9a607baba46fade03d))
- **all:** [DEV-352] review insight ([#312](https://github.com/nclarity/backend/issues/312)) ([67565a0](https://github.com/nclarity/backend/commit/67565a0a9eac7b8c0831c6c029a7664cbe806b59))
- **all:** [DEV-354] telemetry calculations ([#310](https://github.com/nclarity/backend/issues/310)) ([8b4109c](https://github.com/nclarity/backend/commit/8b4109c0466e0c1603d8574ad3871cca969aad10))
- **all:** [DEV-372] contact info optional and schema updates ([#332](https://github.com/nclarity/backend/issues/332)) ([771b475](https://github.com/nclarity/backend/commit/771b4759747045e20b0a84448266bc453aa4feb4))
- **all:** [DEV-374] insights not tripping ([#325](https://github.com/nclarity/backend/issues/325)) ([01420a1](https://github.com/nclarity/backend/commit/01420a137e2aef432fbf56f6a8a8ab3d85c8fdfe))
- **all:** [DEV-64] send data issues ([#299](https://github.com/nclarity/backend/issues/299)) ([b78deef](https://github.com/nclarity/backend/commit/b78deef287cd149849022d140ab20ad4a1fd45b3))
- **all:** [DEV-7] Data Hierarchy ([#249](https://github.com/nclarity/backend/issues/249)) ([ea40ddc](https://github.com/nclarity/backend/commit/ea40ddc9440fff31d8b2a790c71cdb20a9544c71)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **all:** added call for cool and call for heat calculations ([#320](https://github.com/nclarity/backend/issues/320)) ([985063d](https://github.com/nclarity/backend/commit/985063d76e6b0873dea3505ee5a7d3dfd83aa062))
- **all:** added new packages and publish config ([#241](https://github.com/nclarity/backend/issues/241)) ([cc58fae](https://github.com/nclarity/backend/commit/cc58faed694c66f739ac754e4d4b68d0a97330f6))
- **all:** changed system type reference ([994c2a6](https://github.com/nclarity/backend/commit/994c2a687b1c354c604654e555b98bd3da2a60e9))
- **all:** changed system type reference ([#326](https://github.com/nclarity/backend/issues/326)) ([58a9e75](https://github.com/nclarity/backend/commit/58a9e75892eb11452bbb873461362c62901d5a0f))
- **all:** contact info optional and schema updates ([0a93e76](https://github.com/nclarity/backend/commit/0a93e76be5b5a4f79134fae7fa6e710f9112df3a))
- **all:** equipment addition logic ([#287](https://github.com/nclarity/backend/issues/287)) ([49e5413](https://github.com/nclarity/backend/commit/49e541300eb35f7d55e9e058dbf83e411e1248b4))
- **all:** production prepareness ([#307](https://github.com/nclarity/backend/issues/307)) ([8144e27](https://github.com/nclarity/backend/commit/8144e27ed384b992d2ad012b716464218cb021c8))
- **all:** production readiness ([bf6656f](https://github.com/nclarity/backend/commit/bf6656fd1508f30e802d9bb8cb87dcb32d0886dc))
- **all:** ruleset fixes divided ([f7a594b](https://github.com/nclarity/backend/commit/f7a594b53643b3b68a3551bdbcdc659b53edcc3c))
- **all:** ruleset fixes divided ([#327](https://github.com/nclarity/backend/issues/327)) ([7c4e362](https://github.com/nclarity/backend/commit/7c4e362226ad03083148ba3f81e601cd68b0508a))
- **all:** updated use cases and added new fields ([#275](https://github.com/nclarity/backend/issues/275)) ([db2f553](https://github.com/nclarity/backend/commit/db2f5538fa41bf4dc76ae2fcb417391f48f114ff))
- **api:** [DEV-162] user list improvements ([#277](https://github.com/nclarity/backend/issues/277)) ([a102ba4](https://github.com/nclarity/backend/commit/a102ba479506903ab0e995fce2471cb49bcd44e5))
- **api:** [DEV-171] remove default values for equipment profile ([a75bb11](https://github.com/nclarity/backend/commit/a75bb11783bdad820647db53555d60510c031b12))
- **api:** [DEV-172] updated defaults for equipment profile ([53b6e0d](https://github.com/nclarity/backend/commit/53b6e0d33a32adcd1f440c2a0d5732663a896b6c))
- **api:** [DEV-190] added new suggested dispatcher actions ([#280](https://github.com/nclarity/backend/issues/280)) ([5f8ad67](https://github.com/nclarity/backend/commit/5f8ad67729a705f76092c82ce3236d4c3d8bfb50))
- **api:** [DEV-211] equipment addition ([#279](https://github.com/nclarity/backend/issues/279)) ([bc01255](https://github.com/nclarity/backend/commit/bc01255f5e354b41005be5e0623ee5089c6f158e))
- **api:** [DEV-229] updated blackbox helpers ([#260](https://github.com/nclarity/backend/issues/260)) ([c8ed5c2](https://github.com/nclarity/backend/commit/c8ed5c250db0c1684b4ea6a8ae6a1d9cb9d7a1ce))
- **api:** [DEV-242] dispatcher page apis ([#283](https://github.com/nclarity/backend/issues/283)) ([c1a6928](https://github.com/nclarity/backend/commit/c1a69283393251cd70722186185ca81a8fdbb8d5))
- **api:** [DEV-242] dispatcher page apis ([#286](https://github.com/nclarity/backend/issues/286)) ([db69351](https://github.com/nclarity/backend/commit/db69351964a567947faad5aa1d6a1e3a2473b52e))
- **api:** [DEV-260] equipment photos ([#291](https://github.com/nclarity/backend/issues/291)) ([523df0f](https://github.com/nclarity/backend/commit/523df0f6902b82008ecc2e6227bdd9e232a04ba0))
- **api:** [DEV-266] user safeguards ([#290](https://github.com/nclarity/backend/issues/290)) ([600d01e](https://github.com/nclarity/backend/commit/600d01efc86552f94e262ba9a4f3e9f36d7b55a2))
- **api:** [DEV-271] refactored equipment profile creation ([#298](https://github.com/nclarity/backend/issues/298)) ([24730d1](https://github.com/nclarity/backend/commit/24730d1cd94039064a359de31df6303fcf2a8364))
- **api:** [DEV-292] simulated data endpoints ([#292](https://github.com/nclarity/backend/issues/292)) ([ad49034](https://github.com/nclarity/backend/commit/ad49034c9e22bd6f0f9dbf698eee57354948e5cc))
- **api:** [DEV-309] added insight history and pulse history list endpoints ([#300](https://github.com/nclarity/backend/issues/300)) ([43303ed](https://github.com/nclarity/backend/commit/43303ed4056cf94bf9df64a2ff157f5b510fdf7d))
- **api:** [DEV-318] added building equipment condition ([#304](https://github.com/nclarity/backend/issues/304)) ([fe05b59](https://github.com/nclarity/backend/commit/fe05b59125983fcbc71c421e3b05441d4ea0f3f1))
- **api:** added alerts endpoints for dispatcher page and equipment ([#276](https://github.com/nclarity/backend/issues/276)) ([05a60e5](https://github.com/nclarity/backend/commit/05a60e59138fb740a2f82f44e431d48127894e19))
- **api:** added building severity use case ([#303](https://github.com/nclarity/backend/issues/303)) ([692ca26](https://github.com/nclarity/backend/commit/692ca26c7cb3d962ba50639ab4b645111c716fa1))
- **api:** added config fix for device config endpoint ([05c3460](https://github.com/nclarity/backend/commit/05c3460d92e10c6e01568bd9148861463cc1f75e))
- **api:** added create rule endpoint ([3e2a623](https://github.com/nclarity/backend/commit/3e2a6236bd48c4780b1c7760c345444743a316e4))
- **api:** added customer buildings in customer detail api ([200ddf4](https://github.com/nclarity/backend/commit/200ddf482c9daaa58f421b0f772963692125edc0))
- **api:** added customer status for building ([#305](https://github.com/nclarity/backend/issues/305)) ([bef3e5c](https://github.com/nclarity/backend/commit/bef3e5c001c91b14417f7b1597a45a40e46afc87))
- **api:** added default creation for rules endpoint ([fe7960b](https://github.com/nclarity/backend/commit/fe7960baa349ab0ef8f00a370985242971a2e04d))
- **api:** added endpoint to get alerts for an account ([f3839e8](https://github.com/nclarity/backend/commit/f3839e852e525687284ea655de6dbae2111feae7))
- **api:** added error expects for ts ([6cee564](https://github.com/nclarity/backend/commit/6cee56493853e4847c8bce6dad4b315867aecd77))
- **api:** added get alerts for pulse endpoint ([a3b1843](https://github.com/nclarity/backend/commit/a3b1843e21dcfc58dac61e21f69058c46b7c233c))
- **api:** added get rules endpoint ([81e2f0e](https://github.com/nclarity/backend/commit/81e2f0e3d557efd4d3302433fdd5f9424924cc18))
- **api:** device init configuration ([57ecffd](https://github.com/nclarity/backend/commit/57ecffd7edad6ede0506fcc509fd1604670a13f2))
- **api:** equipment condition reviewed ([e3b667a](https://github.com/nclarity/backend/commit/e3b667a14f10a85c6180ca0509c5b71d5eba7f97))
- **api:** equipment condition reviewed ([#330](https://github.com/nclarity/backend/issues/330)) ([c10e983](https://github.com/nclarity/backend/commit/c10e9831b93db417e71fa109db2cecacb75fa30d))
- **api:** esm enablement fixes ([61074b3](https://github.com/nclarity/backend/commit/61074b35d01d172742a6851876eb40fcb3734d81))
- **api:** esm support ([eb27cf1](https://github.com/nclarity/backend/commit/eb27cf1be85f36ffaddd1fd1656221c076ffbbae))
- **api:** fixed account creation ([6356fd4](https://github.com/nclarity/backend/commit/6356fd47b922e6f2a2e495506703214a394867f7))
- **api:** fixed user password reset ([#288](https://github.com/nclarity/backend/issues/288)) ([686c9f7](https://github.com/nclarity/backend/commit/686c9f7caab709e55bd6b8800294b96974b913d2))
- **api:** fixes based on tests ([1a08d10](https://github.com/nclarity/backend/commit/1a08d107064325b718b4b69a33d8d6b28bb4220a))
- **api:** improved region creation and update ([#293](https://github.com/nclarity/backend/issues/293)) ([6d9c160](https://github.com/nclarity/backend/commit/6d9c160d974080e33ddda5707db97301b06e892b))
- **api:** migrated from axios to got ([5dbdfc9](https://github.com/nclarity/backend/commit/5dbdfc9a46bd1bfc514a882d5e695aec87b849b1))
- **api:** refactored to be a esm package ([8804e73](https://github.com/nclarity/backend/commit/8804e73dbfd5786f53e52baf40f0bf1559efca48))
- **api:** updated blackbox helpers ([e443ff7](https://github.com/nclarity/backend/commit/e443ff7cac5ca4574990cb6e6651c9983ccdf7d5))
- **api:** updated getallpulses service ([806dc6f](https://github.com/nclarity/backend/commit/806dc6f5452a8e73f9e3898faffbd3b96eebc688))
- **api:** updated logging configuration and isolated database packages ([48bfeb5](https://github.com/nclarity/backend/commit/48bfeb55d6872be162e529df0fb80f0fd1be0bda))
- **api:** updated mysql middleware ([1e86dfd](https://github.com/nclarity/backend/commit/1e86dfdf48021bf4da40f723b6e44b1fe1d37665))
- **api:** updated ruleset ([d067859](https://github.com/nclarity/backend/commit/d06785962bdfbd6831389555d6e45875449a28ee))
- **auth:** added params to access token needed to get compability with cloud-api ([7210e24](https://github.com/nclarity/backend/commit/********************12a27adacad06980476e))
- **authentication:** added better authentication for dashboard ([6b93898](https://github.com/nclarity/backend/commit/6b938981e8387392e4326dcc753fdf9e8dae6fd2))
- **auth:** improved auth tokens ([516c993](https://github.com/nclarity/backend/commit/516c99348b6269578398d407b33b235c7d42b9b0))
- **auth:** removed unused items on model ([cb64fcc](https://github.com/nclarity/backend/commit/cb64fcc0106e55455ce6dd6aab644aa495ca338d))
- **authtentication:** added authentication for operations ([8b8ff40](https://github.com/nclarity/backend/commit/8b8ff40d1586a16bb27a6b9ffd28dd0df6a64d8f))
- **backend:** cors config ([6bd51a9](https://github.com/nclarity/backend/commit/6bd51a9b5bfa77d474730c963e2c261beba5776c))
- **calculations:** [DEV-22] updated calculations library to use new names in target values ([1577dcf](https://github.com/nclarity/backend/commit/1577dcfb0d0722c5855e61fbeae7dd4efde36773))
- **calculations:** added condition to calculate values only where there is a call ([1572241](https://github.com/nclarity/backend/commit/15722411d596adfe865c08aaa84774b54c4ca5fe))
- **calculations:** added more calculations ([13711b2](https://github.com/nclarity/backend/commit/13711b2a0b86846e08fa002eb03672f3a74e5d3d))
- **calculations:** added target values calculations ([bc5d677](https://github.com/nclarity/backend/commit/bc5d677044c3d5ae6f02b791d7785844e6d09c1d))
- **calculations:** added telemetry examiner helper ([911e7ec](https://github.com/nclarity/backend/commit/911e7ec1f4acfd4399143831cf5b462c01524e76))
- **calculations:** change from commonjs to esm ([aa76833](https://github.com/nclarity/backend/commit/aa76833da0d09750a5af4c27a7ba139d51398b57))
- **calculations:** fixed to use new types in contracts ([1bc72f4](https://github.com/nclarity/backend/commit/1bc72f4651a7d6e7872b44e78eb31fb71d14376a))
- **calculations:** used new types defined in contracts ([3e8d5b6](https://github.com/nclarity/backend/commit/3e8d5b6b2dfd67c85f8275601d2d9465abe630e8))
- **chart-data:** added service to get chart data based on telemetry and timestamps ([448e7e9](https://github.com/nclarity/backend/commit/448e7e936b0cc4ccdf8061602c1a3e2825cd2ec9))
- **charts:** added compability for colors with frontend ([afe3a18](https://github.com/nclarity/backend/commit/afe3a1849c7567750b7c8cf5a650625126089df4))
- **charts:** added electrical coversion ([b5ebfe4](https://github.com/nclarity/backend/commit/b5ebfe41bbaeff4558c99530719278a403ae3a7b))
- **charts:** added endpoint to get chart data based on timestamps ([2888f28](https://github.com/nclarity/backend/commit/2888f2870b0f20d43ebbbcf6c1fe11e3bea27191))
- **charts:** added factories for chart data ([25f7325](https://github.com/nclarity/backend/commit/25f73253c46bcf4ad227d042962bcbd3c9904e99))
- **charts:** added global setting for luxon to be on utc timezone ([d80ebc2](https://github.com/nclarity/backend/commit/d80ebc27fd61ec111f37fc3ef05acc675f33559a))
- **charts:** added kilo scale to electrical chart ([b176b21](https://github.com/nclarity/backend/commit/b176b2192eb8d86d91f310d64e8b24b063e0d139))
- **charts:** added plotting rules for erroneous data ([51965c4](https://github.com/nclarity/backend/commit/51965c42e10529e9553db56027410e3cc6605acc))
- **charts:** added timestamps to each data point sent on chart data ([5117a6b](https://github.com/nclarity/backend/commit/5117a6b1b5450714bbddf033dcb00f28836ea13f))
- **charts:** changed colors from lowercased colors to uppercased colors ([f45845f](https://github.com/nclarity/backend/commit/f45845f8628c1977620b85ad03ff97cafea6f4c5))
- **charts:** now telemetry data is projected in a more efficient way ([156ffb8](https://github.com/nclarity/backend/commit/156ffb847064abfc58a5783283a00afd781226ff))
- **charts:** removed comments ([aa12740](https://github.com/nclarity/backend/commit/aa12740f84a12c590764a43b1803db10c1cae6e4))
- **charts:** removed projections ([24b38c4](https://github.com/nclarity/backend/commit/24b38c47147f088815df0e092499bffd3f8debd4))
- **charts:** removed temperature conversion on rule applier ([71fcf53](https://github.com/nclarity/backend/commit/71fcf53380cb0bb821ae04533f3c6be93246669c))
- **config:** added config endpoint ([25d6f75](https://github.com/nclarity/backend/commit/25d6f753de480eab5772553c5549e78f69a89697))
- **contracts:** [DEV-180] added contracts package ([#227](https://github.com/nclarity/backend/issues/227)) ([2330f5f](https://github.com/nclarity/backend/commit/2330f5fba9ba926f16e12a6ef1ddec60c22efa33))
- **contracts:** [DEV-269] added new value for rule input value ([#294](https://github.com/nclarity/backend/issues/294)) ([a9abeac](https://github.com/nclarity/backend/commit/a9abeac92dea2f4b96b3f4f58ba0245fe336cadf))
- **contracts:** added calculations types ([b8dc6da](https://github.com/nclarity/backend/commit/b8dc6daf005305da0fe83bb1c9b991e7adb14dfc))
- **contracts:** added call labels ([120fa81](https://github.com/nclarity/backend/commit/120fa810e55a01f671f515ac7153f820532b2a83))
- **contracts:** added cjs and esm exports ([de3fbe5](https://github.com/nclarity/backend/commit/de3fbe5e731e4c8678397427479a3679b56d21db))
- **contracts:** added cjs and esm exports ([#231](https://github.com/nclarity/backend/issues/231)) ([adfb7f0](https://github.com/nclarity/backend/commit/adfb7f0486a2b10add7f65be357c32fafd4ce4c0))
- **contracts:** added default flag for system profile ([#297](https://github.com/nclarity/backend/issues/297)) ([43286ae](https://github.com/nclarity/backend/commit/43286ae42871ad96cecd0d325295a5dc7c51d877))
- **contracts:** added dev branch for release ([4e49f2c](https://github.com/nclarity/backend/commit/4e49f2c2877f709692c6e4421fffeb9f03bf95b7))
- **contracts:** added new exports for pulse config data ([006af64](https://github.com/nclarity/backend/commit/006af6464943d18483d5dcc904722f30d9fe16e3))
- **contracts:** added new exports for pulse config data ([#235](https://github.com/nclarity/backend/issues/235)) ([c35f6ce](https://github.com/nclarity/backend/commit/c35f6cefe6c41e4f30a1cdf7a706402a439a87bc))
- **contracts:** automatic releases enablement ([b4e495f](https://github.com/nclarity/backend/commit/b4e495f5b97bcff7b186fb5c012f4cdcac4b05bd))
- **contracts:** reflected last prisma migrations ([61e8eb3](https://github.com/nclarity/backend/commit/61e8eb3ff1001ff91dd5d27111b8406be0620112))
- **contracts:** updated contracts package ([#274](https://github.com/nclarity/backend/issues/274)) ([bc1bd0a](https://github.com/nclarity/backend/commit/bc1bd0a4f3a98555d292928fcb3156322b4dad73))
- **contracts:** updated readme in contracts package ([e2168c9](https://github.com/nclarity/backend/commit/e2168c9ef44e90b9a09efba4e4224bfc47f0580c))
- **contracts:** updated release config file ([2beb609](https://github.com/nclarity/backend/commit/2beb609bc1ca8c268a28f60c36935488ec6030c4))
- **contracts:** updated release config file ([#230](https://github.com/nclarity/backend/issues/230)) ([62e460c](https://github.com/nclarity/backend/commit/62e460c3341985bba9662d4c4c61263e870ee91f))
- **contracts:** updated type for customer phone ([dadbb9f](https://github.com/nclarity/backend/commit/dadbb9fdc3f4a50674b13cd93e35f4d11d658319))
- **contracts:** updated workflow ([7ac8a71](https://github.com/nclarity/backend/commit/7ac8a71ce71f58e1de3242a6cbff63c6b83ae49a))
- **contracts:** updated workflow ([#228](https://github.com/nclarity/backend/issues/228)) ([a17f45a](https://github.com/nclarity/backend/commit/a17f45a5def648b8b24dd30958a9ce9d13f45b32))
- **customInisghts:** added cache adapter ([a84aa96](https://github.com/nclarity/backend/commit/a84aa96555ef23393d92ce34c26358422648fd7f))
- **customInsights:** [DEV-22] added better logging for ruleProcessor ([a2d048d](https://github.com/nclarity/backend/commit/a2d048d2c8192fcf8433b033261a53469db1f3bb))
- **customInsights:** [DEV-22] update rule processor ([8f39a80](https://github.com/nclarity/backend/commit/8f39a800a5717d13c4aaf131347a0f6b543c25f7))
- **customInsights:** [NP-22] updated ruleProcessor ([ef0bf88](https://github.com/nclarity/backend/commit/ef0bf88d9e0d99baad99331ad07d6167b91861e2))
- **customInsights:** added account id for enqueuing function ([2e9528f](https://github.com/nclarity/backend/commit/2e9528f9278c88c5ca733fcf19439ef5219aa983))
- **customInsights:** added alerts triggering for process rule ([c5c3173](https://github.com/nclarity/backend/commit/c5c3173bdf0347bc621a2bbb0a46308dd125a91a))
- **customInsights:** added building options ([cd6a630](https://github.com/nclarity/backend/commit/cd6a630e20b5ad1046dcbea41c8f339c39a30f0e))
- **customInsights:** added bulkGetRefrigerants adapter ([5e3d143](https://github.com/nclarity/backend/commit/5e3d143dc38df6f75f821fd3fb296a8d3ab236ae))
- **customInsights:** added calculated values getter ([91459b1](https://github.com/nclarity/backend/commit/91459b165a8dadb94f62d03e47eb27f7c0ade54f))
- **customInsights:** added correct usage for refrigerant ([ca0bbca](https://github.com/nclarity/backend/commit/ca0bbcad78b766272c4c5ca136cec0b54fb2fd5a))
- **customInsights:** added createRule endpoint ([fa6fb7f](https://github.com/nclarity/backend/commit/fa6fb7f14cd397f1677fe26d90e27c6f25ba763c))
- **customInsights:** added function to start processing all rules ([5d98a0e](https://github.com/nclarity/backend/commit/5d98a0e983dc49679542ae937c30261ca4047a1a))
- **customInsights:** added new apis for database adapter ([5779896](https://github.com/nclarity/backend/commit/57798968e4d1faeb736606020b3fa7174c91e604))
- **customInsights:** added sequelize adapter ([1d8388f](https://github.com/nclarity/backend/commit/1d8388f15f60ddedb9db1f83560fed85b47f971a))
- **customInsights:** added target values calculations ([cf731d7](https://github.com/nclarity/backend/commit/cf731d7506724f9a891cb35ad2e24f7a791be616))
- **customInsights:** added telemetry schema ([aad783e](https://github.com/nclarity/backend/commit/aad783e0b093554606ea91570c49c0b6eda6ab46))
- **customInsights:** added telemetryTransformations ([9b0e40a](https://github.com/nclarity/backend/commit/9b0e40a58de1a959b9611d733ad39062df3fa05c))
- **customInsights:** added transformer ([8fd4514](https://github.com/nclarity/backend/commit/8fd4514a2b49bb8fb9913d1d645ea853c576223e))
- **customInsights:** added transformer usage ([51de564](https://github.com/nclarity/backend/commit/51de564e29b5f96d5be342121fce28edb7241702))
- **customInsights:** added weather adapter ([80892fd](https://github.com/nclarity/backend/commit/80892fdcd56958b5da9d72b9e215edee04bf48cf))
- **customInsights:** changed business rules based on new rule set model ([c1ba87e](https://github.com/nclarity/backend/commit/c1ba87ed6956d0e0343c4acb132f1887f74275fb))
- **customInsights:** changed rule model ([c6d30e2](https://github.com/nclarity/backend/commit/c6d30e2ccae2566100cbc5b22b0abbe4e418c900))
- **customInsights:** improved adapters based on tsconfig changes ([55be4a8](https://github.com/nclarity/backend/commit/55be4a89b56b5efb8466b857a82dd0dea263fdcb))
- **customInsights:** improved connections to services ([bf6366a](https://github.com/nclarity/backend/commit/bf6366a265b7e417e718cfadbbf459d9c7d64c70))
- **customInsights:** improved model and added module type ([df87697](https://github.com/nclarity/backend/commit/df87697b857c8cde6c13edd2819582f136c1adda))
- **customInsights:** initial rule processor created ([7aa42aa](https://github.com/nclarity/backend/commit/7aa42aa73aa3c2b1fddd4e62d6459eda05dd0dc6))
- **customInsights:** installed telemetry package ([3f2ddc8](https://github.com/nclarity/backend/commit/3f2ddc8e61c3ab4569a02251423dc0954db45d14))
- **customInsights:** modified adapter to use account id ([98e8beb](https://github.com/nclarity/backend/commit/98e8bebb1a2ee53c390d8006ac30435654455b9f))
- **customInsights:** updated params for processRule function ([998cdde](https://github.com/nclarity/backend/commit/998cdde50bbf463bbd1ddd564df7be9bd2a5e9a2))
- **customInsights:** usage for processRule improved ([de372ea](https://github.com/nclarity/backend/commit/de372eac4f8b9d078ffec0fa0737c4ff44fbe43f))
- **dashboard:** fixed data in pulse view ([efc87e9](https://github.com/nclarity/backend/commit/efc87e99bd075795f8531412bab5036279676022))
- **database:** [DEV-167] updated references to foreign key in account model ([2953a47](https://github.com/nclarity/backend/commit/2953a479ab0975ddf34324a9f338dbf68fd86438))
- **database:** [DEV-258] building addition fields ([#289](https://github.com/nclarity/backend/issues/289)) ([b9a4e01](https://github.com/nclarity/backend/commit/b9a4e010797167cd7e5e11e0575adf392278960b))
- **database:** [NP-20] updated mongo model ([5781d11](https://github.com/nclarity/backend/commit/5781d11f8459795cebe088ed0a31e00b37f1032f))
- **database:** [NP-21] update schema to use groups and sets ([aa2813a](https://github.com/nclarity/backend/commit/aa2813a89bcfc916fbb11c96b913ca58ae2acff4))
- **database:** added accountId field for alerts ([1b18510](https://github.com/nclarity/backend/commit/1b185102ba1dabb0ddb1e3cad0805ce8b4f18626))
- **database:** added model for a rule ([920e8f8](https://github.com/nclarity/backend/commit/920e8f81546378aa2e97aa3f9ae68c227adc2ab0))
- **database:** added readme and updated package json ([3047917](https://github.com/nclarity/backend/commit/30479178544bd705abcf0c77f564285a80e8ba74))
- **database:** added refrigerants model ([29bbd1a](https://github.com/nclarity/backend/commit/29bbd1a1277f57423e7dd84cb32cc8bc8a630585))
- **database:** added semantic-release ([8a20478](https://github.com/nclarity/backend/commit/8a204782a324a508788b32b281d7b126049522d1))
- **database:** added telemetry schema validation ([14085d3](https://github.com/nclarity/backend/commit/14085d355f903c5885e7d2a35990e035f0416495))
- **database:** added usage of groups and account id ([68415d3](https://github.com/nclarity/backend/commit/68415d3ffccfad783e32285d2ac90f4d20db63a3))
- **database:** added validation for comparisonValueLabel ([c59563e](https://github.com/nclarity/backend/commit/c59563e4933ae5449eff4ef82ccddd985e3022fe))
- **database:** added validationSchemas folder ([94c01c7](https://github.com/nclarity/backend/commit/94c01c7d2e8c9b1d885caf4875fce0b4594d6e09))
- **database:** improved alerts and rules documents ([80d2939](https://github.com/nclarity/backend/commit/80d29391a520bcec2b642a488da019ac3ff8f02b))
- **database:** improved refrigerant models ([d1916e4](https://github.com/nclarity/backend/commit/d1916e4e8785184dfeb0c507e40e6d2fa365cd7d))
- **database:** improved rule model ([4f76212](https://github.com/nclarity/backend/commit/4f762127cc269d26d6f6307649b9451988f582ee))
- **database:** improved rule model with mixed type ([6f1a321](https://github.com/nclarity/backend/commit/6f1a3218815086260cea30bdaee4d8416af0e3d8))
- **database:** improved telemetry data object ([5528ca9](https://github.com/nclarity/backend/commit/5528ca9180dab197aba9463155a14613e99e2010))
- **database:** imrpoved rule definition and added alert model ([375cc93](https://github.com/nclarity/backend/commit/375cc934747d082bec0eb1e6fb20ca2820225111))
- **database:** migrated to esm type ([52b471f](https://github.com/nclarity/backend/commit/52b471f13ae18f9589a8080d3281bc55cdc3fbbe))
- **database:** model synchronization ([a5b5090](https://github.com/nclarity/backend/commit/a5b5090e402d36e069993f080088b4e46f9f86e0))
- **database:** removed unique names ([3c706ae](https://github.com/nclarity/backend/commit/3c706ae7f38bbc77fc33db59b45f1f272d5ea5b8))
- **database:** split rules validations ([0b16136](https://github.com/nclarity/backend/commit/0b16136bf9bffd9c13c4bfe8db1dd5efd620b20d))
- **database:** typescript checks improvements ([34ea281](https://github.com/nclarity/backend/commit/34ea2813c2d31089ae03e2a09a39278b333135d5))
- **database:** updated models according to rulesets ([985a195](https://github.com/nclarity/backend/commit/985a1952c8dbe10ed2062763ab5ded3ac09c3aee))
- **database:** updated rule to match requirements ([5aa47e4](https://github.com/nclarity/backend/commit/5aa47e44b6f4aa22cf637fe1b2fef011b01a9425))
- **database:** updated target value labels ([f64b5dc](https://github.com/nclarity/backend/commit/f64b5dcd475077759a9b2f3ef289d158aa2fcd2d))
- **database:** updated target values to be separated ([387b920](https://github.com/nclarity/backend/commit/387b920ef031ac4cb09aed74e96d05307455b75d))
- **database:** updated validation schemas ([f19016c](https://github.com/nclarity/backend/commit/f19016c8b054a342657063079a1ec9723046b020))
- default rules ([#328](https://github.com/nclarity/backend/issues/328)) ([ed64793](https://github.com/nclarity/backend/commit/ed647933599cb329755d9765f42eda84ffc0fa4f))
- **DEV-28:** custom insights backend integration ([#234](https://github.com/nclarity/backend/issues/234)) ([47a1f16](https://github.com/nclarity/backend/commit/47a1f16980642ec6c671f2d5773bd6e1707b67e8))
- equipment addition and updates made ([07dc1ef](https://github.com/nclarity/backend/commit/07dc1ef80848dd5d9bd336e1535282d225b29300))
- **equipment:** added create equipment use case ([50ab1fe](https://github.com/nclarity/backend/commit/50ab1fe882ff0cc4a61eb22d0d933d7fdc34df1c))
- **equipment:** added deletion of equipment ([5144b45](https://github.com/nclarity/backend/commit/5144b4597d7ee04e8897b0d3925b74854b5f941d))
- **equipment:** added equipment router ([499db04](https://github.com/nclarity/backend/commit/499db04146a8075ed7f574f893ff6f2e842da416))
- **equipment:** created controller and use to get equipment by id ([e7c98f0](https://github.com/nclarity/backend/commit/e7c98f0b33e000cc831dab135e05d727bc69f9df))
- **equipment:** created use case to update equipment profile ([0a6a92c](https://github.com/nclarity/backend/commit/0a6a92cf913bffca9e1bc98c11525bd5f4ee131f))
- **equipmentProfile:** enabled virtual to refer to system profile ([ab2bc15](https://github.com/nclarity/backend/commit/ab2bc15338f01db00199d3f136f68ac28e2a0e42))
- **equipmentTags:** delete tags endpoint ([c99f2df](https://github.com/nclarity/backend/commit/c99f2df2601f3b7e5dbf86f050336e51b582b9a5))
- **factories:** added factories for users and pulses with typings ([bcd9068](https://github.com/nclarity/backend/commit/bcd90683f55156bffc847a58a27d499c633dfbfb))
- **functions:** added back eventhub name ([530b6aa](https://github.com/nclarity/backend/commit/530b6aa1dca7063964bf55ab44a5f4cc46290144))
- **functions:** added device config fuction ([fd6d2a5](https://github.com/nclarity/backend/commit/fd6d2a587f278fced4b08c5b5bff6e6bd650f306))
- **functions:** added endpoint to get pulses report ([79142be](https://github.com/nclarity/backend/commit/79142be0f1a73dafec3339de634892a7c0f67229))
- **functions:** added functions from backend function repo ([25c1334](https://github.com/nclarity/backend/commit/25c1334512dd806be33b99a00726f67e51e4c1b3))
- **functions:** added recoveredTelemetry exporter ([c6048a6](https://github.com/nclarity/backend/commit/c6048a6fbde32246c349d1a29759f04f0f70ed91))
- **functions:** added truthy keywords for custom value ([#319](https://github.com/nclarity/backend/issues/319)) ([2e0447a](https://github.com/nclarity/backend/commit/2e0447a6d92859aa7f972cab82c3ca8eb61f530e))
- **functions:** change prod eventhub ([d5dbd67](https://github.com/nclarity/backend/commit/d5dbd67f756a454f5574636168e3433cb8f24723))
- **functions:** device init config ([7b3e186](https://github.com/nclarity/backend/commit/7b3e186f3ab83f2f5ae39b3d67a7cd00eeb133c5))
- **functions:** esm enablement for functions app ([df64e32](https://github.com/nclarity/backend/commit/df64e32151a95e2875e29e18467fab8524593c27))
- **functions:** esm support ([1622ec7](https://github.com/nclarity/backend/commit/1622ec735bd90f5fe2454fe134262ebdb87ddbca))
- **functions:** improved functions configuration ([d437cb4](https://github.com/nclarity/backend/commit/d437cb4119c8f5663cebdee4a20a65bf67ab40e5))
- **functions:** reduced logging for database exporter ([badd736](https://github.com/nclarity/backend/commit/badd736abb2597e1a384f75494750d965b329a40))
- **functions:** reduced logging for database exporter ([#246](https://github.com/nclarity/backend/issues/246)) ([a19e44f](https://github.com/nclarity/backend/commit/a19e44f4e5a6b5719661f6d9d5ddc9973e311fdd))
- **functions:** reduced the size for the batches in the event hub ([001facc](https://github.com/nclarity/backend/commit/001facca0d8844084acf63f9384320b179bb5416))
- **functions:** refactored to be a esm ([ea204af](https://github.com/nclarity/backend/commit/ea204af2ab43e48e25ee29f49b178bc55d211559))
- **functions:** removed undesired prop assignment ([42b1d90](https://github.com/nclarity/backend/commit/42b1d90946471ca7ec1094af285feaa181099f2f))
- **functions:** updated deployment function ([12866f0](https://github.com/nclarity/backend/commit/12866f03ac5493346d4be6b21f46e7fa7835d47b))
- **functions:** updated device template id in device init ([36a4e70](https://github.com/nclarity/backend/commit/36a4e70c81e5e95275a9825fa85187fa67a91ead))
- **functions:** updated event hub name ([7049bb7](https://github.com/nclarity/backend/commit/7049bb71f3bc037e1ca706aecda38cf49c14f677))
- **functions:** updated eventHubName ([93ac887](https://github.com/nclarity/backend/commit/93ac887b1bec89c9c8c6c76bba27b29eec3bb057))
- **functions:** updated frequency for pulses ([a4c3b00](https://github.com/nclarity/backend/commit/a4c3b0098930bb7bbd84e9fdb5dc443286c92f2a))
- **functions:** updated function def to use params and queries ([fcc8759](https://github.com/nclarity/backend/commit/fcc8759cc8c854489234787d4c35dcfb90800b6e))
- **functions:** updated host json and report endpoint ([a44a1dd](https://github.com/nclarity/backend/commit/a44a1ddd64b33fc308d4093ea4f65ec73e452747))
- **functions:** updated ruleInputGetter to use new types ([2078489](https://github.com/nclarity/backend/commit/207848998dca807ccc4eb2d73a388f652b2e04d7))
- **functions:** updated the event hub name for production ([fee8016](https://github.com/nclarity/backend/commit/fee80166565511fc2f0b5e0bc9cef03e89d37292))
- **getTelemetryData:** finished add chart data methods ([8eecd37](https://github.com/nclarity/backend/commit/8eecd370f5b415a14ebfafa022566eb60ea83581))
- **insights:** added account filtering and edge cases ([6941e8d](https://github.com/nclarity/backend/commit/6941e8db1397c0cb3a3964b6f8831a3774173bab))
- **insights:** added getInsightsByPulses service ([eadfacb](https://github.com/nclarity/backend/commit/eadfacb4c356994eea7bce9fb9497865b682436e))
- **insights:** improved pagination ([e82e783](https://github.com/nclarity/backend/commit/e82e7838fe0b59765650c80c0b6294a103bfc83c))
- **insights:** minimal endpoint to get insight history ([223fd59](https://github.com/nclarity/backend/commit/223fd593ceb4f1b6903d4e42b4149849c7f6c03f))
- **insights:** pulses by user selected ([857f30b](https://github.com/nclarity/backend/commit/857f30b164ffba3687767bf0fb3dd0e4883d921d))
- **logger:** made application insights transport optional locally ([913970c](https://github.com/nclarity/backend/commit/913970c39b49b4753d6ae2bc12a3037f89ed6d4b))
- **newUserCredentials:** changed emails ([62027c5](https://github.com/nclarity/backend/commit/62027c543de03a701cea869cd07eb27d740ab38d))
- no app deplpoy ([#259](https://github.com/nclarity/backend/issues/259)) ([81cc9bd](https://github.com/nclarity/backend/commit/81cc9bd364c25486ad01a6a39f406647e8ba6601))
- **opsUser:** reset password feature ([40bcd37](https://github.com/nclarity/backend/commit/40bcd37a18225708e0411c2aed84c92155267ab3))
- **opsUsers:** added edit ops user service ([0386249](https://github.com/nclarity/backend/commit/0386249c0807c51febcb03712d1de7cc8f6d4887))
- **opsUsers:** added router and service to get all ops users ([75ded15](https://github.com/nclarity/backend/commit/75ded1561da7d75ee13848b4fc4fe63b12d8ffb7))
- **opsUsers:** delete and get ops user by id service added ([3907161](https://github.com/nclarity/backend/commit/3907161a3aa6b61ff7edd0cff118320b77294e1f))
- **pulse:** added get pulse config endpoint ([d5edab1](https://github.com/nclarity/backend/commit/d5edab16835dc59c5fe2f8c6f326e8bb4f2c8f2e))
- **pulses:** changed type naming on pulses factory ([981c0cd](https://github.com/nclarity/backend/commit/981c0cd3990fa92ec76a1c3b6769dc0ee1978f5c))
- **pulses:** converted saturation temperature values to celcius ([3aae667](https://github.com/nclarity/backend/commit/3aae667c054a9494bbc64f4437b23a8a78973a69))
- **pulses:** only send eer data when there is a call for cool ([fbcc7ae](https://github.com/nclarity/backend/commit/fbcc7aec28de0622d4e7544dbe3a30a97024de56))
- **pulses:** pulses deletion corrected ([b0949e8](https://github.com/nclarity/backend/commit/b0949e810c96c89aa5f2ad9e371b467c6ad8a44c))
- **pulses:** type telemetry data and removed unnecessary code ([117f8c7](https://github.com/nclarity/backend/commit/117f8c7ffbd44935bc650374e118279098ec05c1))
- replaced bcrypt with bcryptjs ([a74c9b3](https://github.com/nclarity/backend/commit/a74c9b347354134b4776c2d777c46415e95c7f30))
- reviewed insights ([198e7ce](https://github.com/nclarity/backend/commit/198e7ce1e69dd9e69671ac4f41b46938f6818211))
- **services/createUser:** added new email for user credentials ([ade5e85](https://github.com/nclarity/backend/commit/ade5e85ec90cd86845fe3446dd0f9b3030b19b1f))
- share RTU Scorecard ([#61](https://github.com/nclarity/backend/issues/61)) ([5ffb4a9](https://github.com/nclarity/backend/commit/5ffb4a9a12a2deb0e47222b0326b7eada29625ca))
- **systemprofiled:** started entity ([3de1db3](https://github.com/nclarity/backend/commit/3de1db34a6e742b4f79ce6794e4fec39f2766e1e))
- **systemProfiles:** added router ([e08d943](https://github.com/nclarity/backend/commit/e08d9431c1957fcce7747bd2a45e19623b8fd5a9))
- **tags:** added services to get tags by pulses and account ([8f07919](https://github.com/nclarity/backend/commit/8f0791967113c790fd073a8e9d71108facd48d0e))
- **tags:** added tags services to create and list ([29c891d](https://github.com/nclarity/backend/commit/29c891d3227e5cb39899450b4769645ee33ad50e))
- **telemetry:** added calculations ([80022bf](https://github.com/nclarity/backend/commit/80022bf2de6ff433ed6d032e671b01efa3b8d38a))
- **telemetry:** added calculations ([8ca1564](https://github.com/nclarity/backend/commit/8ca15641e93a51e184a932b8206b58999a4decd0))
- **telemetry:** added customPsychometrics ([18686d6](https://github.com/nclarity/backend/commit/18686d68e7a8650789ffb807f3fd15511cfaca12))
- **telemetry:** added documentation for telemetry values ([919c66b](https://github.com/nclarity/backend/commit/919c66bfd5509de06359ce249e15eb1b1e3c06e0))
- **telemetry:** added more calculations ([9aed02a](https://github.com/nclarity/backend/commit/9aed02a37ed075effa158755e9035cb7a9343e72))
- **telemetry:** added sideEffects field in telemetry package ([fe2cab8](https://github.com/nclarity/backend/commit/fe2cab8d9ee6c4b133b24886f6ecbe28158ae9bc))
- **telemetryData:** modified getTelemetryData endpoint to manage lastCall data ([c255f2b](https://github.com/nclarity/backend/commit/c255f2b17ed62ac7d192bbc9ffbdfd30c5fefae1))
- **telemetry:** simplified calculations ([615ef4a](https://github.com/nclarity/backend/commit/615ef4abb6481fb2d0fad0559146bd430c9e3aa1))
- **telemetry:** started to add calculations ([0c5b794](https://github.com/nclarity/backend/commit/0c5b7949b63fabb0469dca2fada975bb615bc75e))
- **user:** added re-add users when deleted ([be515e2](https://github.com/nclarity/backend/commit/be515e2ce64ffca22f7d53e56ade4dc766f6fccd))
- **user:** modified display name ([22d448b](https://github.com/nclarity/backend/commit/22d448bb19c4392990d6a88508a337ea2017e368))
- **users:** added active clause when deleted and re-adding user ([74f7588](https://github.com/nclarity/backend/commit/74f75881617d8ffd8266602fec4118c494267940))
- **users:** added get users endpoint ([77e04f4](https://github.com/nclarity/backend/commit/77e04f4137821a5af058fd9540c001ff2ffe1698))
- **users:** added user addition endpoint ([0841a88](https://github.com/nclarity/backend/commit/0841a884d744c2a581252ededc0f57b7152d0f84))
- **users:** added user deletion endpoint dashboard[#394](https://github.com/nclarity/backend/issues/394) ([ccfc6bb](https://github.com/nclarity/backend/commit/ccfc6bbef9276250f8948c136b14f514f728aa4d))
- **users:** added user edition endpoint dashboard[#393](https://github.com/nclarity/backend/issues/393) ([55d908f](https://github.com/nclarity/backend/commit/55d908fe5cf7a1f8d96d6a12554bfdc6c3f22a9e))
- **users:** make service to not be able to delete self ([9f2a3b6](https://github.com/nclarity/backend/commit/9f2a3b600795339e0e408fb71a9e2ae23bb432f9))

### Reverts

- Revert "chore(release): @nclarity/contracts-v1.0.0 [skip ci]" ([fb7ccc4](https://github.com/nclarity/backend/commit/fb7ccc4a70db5f2bdce51674948cc4b9df6cb07e))
- Revert "chore(release): @nclarity/contracts-v1.0.0@1.0.0 [skip ci]" ([93a8b17](https://github.com/nclarity/backend/commit/93a8b1766e8877a72dbb894a2b813c461ad4fe51))
- Revert "chore(release): @nclarity/contracts-v1.0.0@1.0.0 [skip ci]" ([0079a5e](https://github.com/nclarity/backend/commit/0079a5e82141be54e4a6252ef70f849c1816bcd2))
- Revert "chore(release): @nclarity/contracts-v1.0.0@1.0.0 [skip ci]" ([26f5c6a](https://github.com/nclarity/backend/commit/26f5c6a09e15bae2b24f1cd0f5df7d0bc346f384))
- Revert "chore(release): @nclarity/contracts-v1.1.0@1.1.0 [skip ci]" ([25f28c5](https://github.com/nclarity/backend/commit/25f28c5c4d5ee613091002ddeef538acce45c190))
- Revert "chore(release): @nclarity/contracts-v1.1.0@1.1.0 [skip ci]" ([7011233](https://github.com/nclarity/backend/commit/7011233ae3a3420e310618e588616954ff562103))
- Revert "chore: release 04/08/2023" [skip ci] (#323) ([dd0671d](https://github.com/nclarity/backend/commit/dd0671dbe1fb5a004020e5a7a87855b177e68536)), closes [#323](https://github.com/nclarity/backend/issues/323) [#322](https://github.com/nclarity/backend/issues/322)
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.3@1.0.0-beta.3 [skip ci]" ([21bcbee](https://github.com/nclarity/backend/commit/21bcbeee6704ebda3e3ce43a219d3f09a533a91f))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([1d965b0](https://github.com/nclarity/backend/commit/1d965b0d9d377a30b567ad289c114be8076408a9))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([644ebd8](https://github.com/nclarity/backend/commit/644ebd8caebac67404ee4484c15e18e6dc599c87))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([037f7e2](https://github.com/nclarity/backend/commit/037f7e2d76d4a2d1ee5df4529ef4fc5fd8e865db))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([5ad3d2b](https://github.com/nclarity/backend/commit/5ad3d2bc2cb74f11c7a0ab8252b4045796d9119b))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([532af29](https://github.com/nclarity/backend/commit/532af29aba22a40aa36d7620b72585375ca47863))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.5@1.0.0-beta.5 [skip ci]" ([67f0049](https://github.com/nclarity/backend/commit/67f0049806941a8a948572ec7ff0fbea9cdceae1))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([fc35fd7](https://github.com/nclarity/backend/commit/fc35fd77d671f81c6c2f78e25154d170f63d3a93))
- Revert "bugfix(authentication): fixed auth middleware" ([d1ef2a3](https://github.com/nclarity/backend/commit/d1ef2a38c923223f3cca3f51b0d971bb14105de5))
- Revert "fix(pulses): update of pulse warranty data" ([b7a2965](https://github.com/nclarity/backend/commit/b7a29657ce443e5349027f5d76704992313b0be5))

### BREAKING CHANGES

- updated relational database models based in the prisma output
- this package is now esm

# 1.0.0 (2023-08-05)

### Bug Fixes

- [DEV-232] fixed the frequency undefined issue ([#261](https://github.com/nclarity/backend/issues/261)) ([71c1ac8](https://github.com/nclarity/backend/commit/71c1ac86b8368e5759928ff3d0ddf311e40af34e))
- [DEV-237] added lastsignstamp value in service ([#266](https://github.com/nclarity/backend/issues/266)) ([25273a0](https://github.com/nclarity/backend/commit/25273a01980a31c789797141cdc7e9501117fcf0))
- [DEV-245] fixed update for equipment ([#282](https://github.com/nclarity/backend/issues/282)) ([dbdc4d6](https://github.com/nclarity/backend/commit/dbdc4d6bff670cecfdf8e5a5b8c0ab6b0e7254a6))
- [DEV-248] added nullish operator in device config ([#285](https://github.com/nclarity/backend/issues/285)) ([7221130](https://github.com/nclarity/backend/commit/7221130439fb0755fc5bb07caffc69968b004406))
- [DEV-313] fixed system profile creation ([#301](https://github.com/nclarity/backend/issues/301)) ([b9bbd89](https://github.com/nclarity/backend/commit/b9bbd89746c28ed399e911884342afeea8ba4ed2))
- [DEV-335] sorting order fix ([c8f54f3](https://github.com/nclarity/backend/commit/c8f54f35f40e1cfdd8f964b9e8def3448c771fda))
- [DEV-352] calculations ([#318](https://github.com/nclarity/backend/issues/318)) ([c50c2f9](https://github.com/nclarity/backend/commit/c50c2f9730bf3fdcdeb48fae9763bc073a0304f7))
- added config fix for device config endpoint ([51f0288](https://github.com/nclarity/backend/commit/51f028840303f31290a7ce472dd633cb26d4fcea))
- added guard for customers deletion ([#278](https://github.com/nclarity/backend/issues/278)) ([ef4e39d](https://github.com/nclarity/backend/commit/ef4e39def74f9c51c1b704929e3fc7bc33e95e1f))
- **all:** [DEV-352] review insight feedback ([#317](https://github.com/nclarity/backend/issues/317)) ([24b3fad](https://github.com/nclarity/backend/commit/24b3fad47145183fc42b0d7c8601e19f54ba13bc))
- **all:** fixed types for use cases ([#269](https://github.com/nclarity/backend/issues/269)) ([7090275](https://github.com/nclarity/backend/commit/7090275e3b1d1ba9af89121b82beff939a336ab1)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **all:** refactoring buildFunctionApp script ([#321](https://github.com/nclarity/backend/issues/321)) ([cc26757](https://github.com/nclarity/backend/commit/cc2675776c194a609d93d5237a36e45b36ca67cc))
- **api:** [DEV-183] fixed user creation ([#233](https://github.com/nclarity/backend/issues/233)) ([1426174](https://github.com/nclarity/backend/commit/142617480cd393602d339e27d69aa29c1e3e433f))
- **api:** [DEV-215] updated getallpulses service ([#248](https://github.com/nclarity/backend/issues/248)) ([a2ad24f](https://github.com/nclarity/backend/commit/a2ad24faa192771187d7edc0a4ac0296bad370d2))
- **api:** [DEV-235] no dashboard data ([#268](https://github.com/nclarity/backend/issues/268)) ([eed96ba](https://github.com/nclarity/backend/commit/eed96ba20447243fa22fcff4218be5732fc696a9))
- **api:** [DEV-235] updated default values in condenser ([#265](https://github.com/nclarity/backend/issues/265)) ([7851617](https://github.com/nclarity/backend/commit/785161753f2d8ce472be0cfdfd47b2e59af614c1))
- **api:** [DEV-236] updated default values in condenser ([#267](https://github.com/nclarity/backend/issues/267)) ([bf5eb25](https://github.com/nclarity/backend/commit/bf5eb25355ddabacf44ac73ff9c04d13e4928f60))
- **api:** fixed abort signal in bundle ([7bcba10](https://github.com/nclarity/backend/commit/7bcba1026f04ba0b3cb64fb8b0a57eb366f20a62))
- **api:** fixed account user creation for dev ([bcea6e2](https://github.com/nclarity/backend/commit/bcea6e2cd6b9556ca977875a06aa81a32ecf7b7a))
- **api:** fixed ecosystem file ([c2fe2cc](https://github.com/nclarity/backend/commit/c2fe2cc049e8c8921a2cff4e68af4a7b58aabf7c))
- **api:** fixed the token payload for the verify function ([#247](https://github.com/nclarity/backend/issues/247)) ([48f4504](https://github.com/nclarity/backend/commit/48f4504a07c997e9f665173c1552065515d95a4e))
- **api:** fixed the user creation with the group ([da25894](https://github.com/nclarity/backend/commit/da2589468a4cdccf8558a86e6fbae5fa4317e8c5))
- **api:** improved salt generation ([df8faf2](https://github.com/nclarity/backend/commit/df8faf2d839813823a9cf5f10429f7af80dd1e81))
- **charts:** added null chart point when there is no data ([abff391](https://github.com/nclarity/backend/commit/abff391c21ee37522d3c447aab7baba798abdfee))
- **contracts:** [DEV-180] updated package json in contracts ([#229](https://github.com/nclarity/backend/issues/229)) ([c20175e](https://github.com/nclarity/backend/commit/c20175ef5c468e31e233b7ca667e090fbf8c8f6b))
- **contracts:** removed unnecessary command in package.json ([5a37db1](https://github.com/nclarity/backend/commit/5a37db1cb65772a4c5692d0adcd67d44248f2c62))
- deployment and prisma generation for function ([#272](https://github.com/nclarity/backend/issues/272)) ([26b2d75](https://github.com/nclarity/backend/commit/26b2d750d41119a832084545b883b4856ec45f6f))
- deployment script function app ([66d3af4](https://github.com/nclarity/backend/commit/66d3af4da931849afa1c2807241c1fc96a943eaf))
- email template id for rtu scorecard sharing ([#65](https://github.com/nclarity/backend/issues/65)) ([5ab5666](https://github.com/nclarity/backend/commit/5ab56669dce5e00eebd2bd178fcc9ca57629b817))
- excluded local settings json ([7e3c5fd](https://github.com/nclarity/backend/commit/7e3c5fd9165de4488e7a59b4e5ca3f30dcf48e46))
- fixed account creation script ([#281](https://github.com/nclarity/backend/issues/281)) ([c2ab073](https://github.com/nclarity/backend/commit/c2ab0730e9115d30850f65d72fcefee15429622e))
- **functions:** added multiple any for rules ([c046a16](https://github.com/nclarity/backend/commit/c046a169505903824327e0b589a7f5f70ac63da3))
- **functions:** improved transformations ([#309](https://github.com/nclarity/backend/issues/309)) ([5b67137](https://github.com/nclarity/backend/commit/5b671374c4cd901eb32683aa709f0d28f9808bb7))
- module resolution for contracts package ([#273](https://github.com/nclarity/backend/issues/273)) ([a564fd0](https://github.com/nclarity/backend/commit/a564fd04ad7a9945e706847523f726b42e5d4a17))
- refrigerants seeder ([#306](https://github.com/nclarity/backend/issues/306)) ([1f38a64](https://github.com/nclarity/backend/commit/1f38a6461d1a0e753d0fa4cd1365648cc7dbb671))
- template id for rtu scorecard ([#62](https://github.com/nclarity/backend/issues/62)) ([f2d5e9f](https://github.com/nclarity/backend/commit/f2d5e9f15158432baf24b82b41a39bd988fe9e46))
- tsc errors ([f8a2c89](https://github.com/nclarity/backend/commit/f8a2c896f349101fccd61230e0e62cf8c6d512ce))
- updated regions in auth context and edit customer ([#308](https://github.com/nclarity/backend/issues/308)) ([b664cd0](https://github.com/nclarity/backend/commit/b664cd052b7ff019dae2b3e972ea979460f8dc6a))

- feat(contracts)!: updated contracts based in prisma output ([68248ed](https://github.com/nclarity/backend/commit/68248ed0957f302b987c86997ad97984837ae00d))
- feat(contracts)!: changed from commonjs to esm ([c5b136c](https://github.com/nclarity/backend/commit/c5b136c7c8108b2a9625726781c10d276238effa))

### Features

- [DEV-177] add logger package ([#238](https://github.com/nclarity/backend/issues/238)) ([a008bc8](https://github.com/nclarity/backend/commit/a008bc8af3ec1962ca40e12dc5a1ff69de869526))
- [DEV-177] deployment to fix jwt token ([#236](https://github.com/nclarity/backend/issues/236)) ([13c1531](https://github.com/nclarity/backend/commit/13c153111f31ab0aaedbf56ff7e98be3dc6a0947))
- [DEV-177] no app deployment ([#237](https://github.com/nclarity/backend/issues/237)) ([14474f9](https://github.com/nclarity/backend/commit/14474f97a6ce9ebae44bdae4523d1e9ca5ae7d43))
- [DEV-177] updated contracts ([#240](https://github.com/nclarity/backend/issues/240)) ([dcdc8f5](https://github.com/nclarity/backend/commit/dcdc8f5b336656c53dd117365f3f21ddfe595e55))
- **accounts:** account deletion enhanced ([2586297](https://github.com/nclarity/backend/commit/258629752979a17f475ec30f5c9729514eceba81))
- **accounts:** deleted info from st-tech table ([0867e4f](https://github.com/nclarity/backend/commit/0867e4fd7222f1d7a96b7b058c79a3d03e0e3f78))
- added systemProfile model ([e165498](https://github.com/nclarity/backend/commit/e16549838c4046f749d55877f4ce66bd55d8b219))
- **all:** [DEV-268] phase based telemetry values sum ([#296](https://github.com/nclarity/backend/issues/296)) ([2c07cf6](https://github.com/nclarity/backend/commit/2c07cf68377502ef5ace7efc47f7cd1466678343))
- **all:** [DEV-270] edit insight ([#314](https://github.com/nclarity/backend/issues/314)) ([bedc5ed](https://github.com/nclarity/backend/commit/bedc5ed5b95ac0c20d8ffb4b360364b052861cee))
- **all:** [DEV-305] delete insight ([#313](https://github.com/nclarity/backend/issues/313)) ([a750d48](https://github.com/nclarity/backend/commit/a750d4898a1afb7f08fc3a9a607baba46fade03d))
- **all:** [DEV-352] review insight ([#312](https://github.com/nclarity/backend/issues/312)) ([67565a0](https://github.com/nclarity/backend/commit/67565a0a9eac7b8c0831c6c029a7664cbe806b59))
- **all:** [DEV-354] telemetry calculations ([#310](https://github.com/nclarity/backend/issues/310)) ([8b4109c](https://github.com/nclarity/backend/commit/8b4109c0466e0c1603d8574ad3871cca969aad10))
- **all:** [DEV-374] insights not tripping ([#325](https://github.com/nclarity/backend/issues/325)) ([01420a1](https://github.com/nclarity/backend/commit/01420a137e2aef432fbf56f6a8a8ab3d85c8fdfe))
- **all:** [DEV-64] send data issues ([#299](https://github.com/nclarity/backend/issues/299)) ([b78deef](https://github.com/nclarity/backend/commit/b78deef287cd149849022d140ab20ad4a1fd45b3))
- **all:** [DEV-7] Data Hierarchy ([#249](https://github.com/nclarity/backend/issues/249)) ([ea40ddc](https://github.com/nclarity/backend/commit/ea40ddc9440fff31d8b2a790c71cdb20a9544c71)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **all:** added call for cool and call for heat calculations ([#320](https://github.com/nclarity/backend/issues/320)) ([985063d](https://github.com/nclarity/backend/commit/985063d76e6b0873dea3505ee5a7d3dfd83aa062))
- **all:** added new packages and publish config ([#241](https://github.com/nclarity/backend/issues/241)) ([cc58fae](https://github.com/nclarity/backend/commit/cc58faed694c66f739ac754e4d4b68d0a97330f6))
- **all:** changed system type reference ([994c2a6](https://github.com/nclarity/backend/commit/994c2a687b1c354c604654e555b98bd3da2a60e9))
- **all:** changed system type reference ([#326](https://github.com/nclarity/backend/issues/326)) ([58a9e75](https://github.com/nclarity/backend/commit/58a9e75892eb11452bbb873461362c62901d5a0f))
- **all:** equipment addition logic ([#287](https://github.com/nclarity/backend/issues/287)) ([49e5413](https://github.com/nclarity/backend/commit/49e541300eb35f7d55e9e058dbf83e411e1248b4))
- **all:** production prepareness ([#307](https://github.com/nclarity/backend/issues/307)) ([8144e27](https://github.com/nclarity/backend/commit/8144e27ed384b992d2ad012b716464218cb021c8))
- **all:** production readiness ([bf6656f](https://github.com/nclarity/backend/commit/bf6656fd1508f30e802d9bb8cb87dcb32d0886dc))
- **all:** ruleset fixes divided ([f7a594b](https://github.com/nclarity/backend/commit/f7a594b53643b3b68a3551bdbcdc659b53edcc3c))
- **all:** ruleset fixes divided ([#327](https://github.com/nclarity/backend/issues/327)) ([7c4e362](https://github.com/nclarity/backend/commit/7c4e362226ad03083148ba3f81e601cd68b0508a))
- **all:** updated use cases and added new fields ([#275](https://github.com/nclarity/backend/issues/275)) ([db2f553](https://github.com/nclarity/backend/commit/db2f5538fa41bf4dc76ae2fcb417391f48f114ff))
- **api:** [DEV-162] user list improvements ([#277](https://github.com/nclarity/backend/issues/277)) ([a102ba4](https://github.com/nclarity/backend/commit/a102ba479506903ab0e995fce2471cb49bcd44e5))
- **api:** [DEV-171] remove default values for equipment profile ([a75bb11](https://github.com/nclarity/backend/commit/a75bb11783bdad820647db53555d60510c031b12))
- **api:** [DEV-172] updated defaults for equipment profile ([53b6e0d](https://github.com/nclarity/backend/commit/53b6e0d33a32adcd1f440c2a0d5732663a896b6c))
- **api:** [DEV-190] added new suggested dispatcher actions ([#280](https://github.com/nclarity/backend/issues/280)) ([5f8ad67](https://github.com/nclarity/backend/commit/5f8ad67729a705f76092c82ce3236d4c3d8bfb50))
- **api:** [DEV-211] equipment addition ([#279](https://github.com/nclarity/backend/issues/279)) ([bc01255](https://github.com/nclarity/backend/commit/bc01255f5e354b41005be5e0623ee5089c6f158e))
- **api:** [DEV-229] updated blackbox helpers ([#260](https://github.com/nclarity/backend/issues/260)) ([c8ed5c2](https://github.com/nclarity/backend/commit/c8ed5c250db0c1684b4ea6a8ae6a1d9cb9d7a1ce))
- **api:** [DEV-242] dispatcher page apis ([#283](https://github.com/nclarity/backend/issues/283)) ([c1a6928](https://github.com/nclarity/backend/commit/c1a69283393251cd70722186185ca81a8fdbb8d5))
- **api:** [DEV-242] dispatcher page apis ([#286](https://github.com/nclarity/backend/issues/286)) ([db69351](https://github.com/nclarity/backend/commit/db69351964a567947faad5aa1d6a1e3a2473b52e))
- **api:** [DEV-260] equipment photos ([#291](https://github.com/nclarity/backend/issues/291)) ([523df0f](https://github.com/nclarity/backend/commit/523df0f6902b82008ecc2e6227bdd9e232a04ba0))
- **api:** [DEV-266] user safeguards ([#290](https://github.com/nclarity/backend/issues/290)) ([600d01e](https://github.com/nclarity/backend/commit/600d01efc86552f94e262ba9a4f3e9f36d7b55a2))
- **api:** [DEV-271] refactored equipment profile creation ([#298](https://github.com/nclarity/backend/issues/298)) ([24730d1](https://github.com/nclarity/backend/commit/24730d1cd94039064a359de31df6303fcf2a8364))
- **api:** [DEV-292] simulated data endpoints ([#292](https://github.com/nclarity/backend/issues/292)) ([ad49034](https://github.com/nclarity/backend/commit/ad49034c9e22bd6f0f9dbf698eee57354948e5cc))
- **api:** [DEV-309] added insight history and pulse history list endpoints ([#300](https://github.com/nclarity/backend/issues/300)) ([43303ed](https://github.com/nclarity/backend/commit/43303ed4056cf94bf9df64a2ff157f5b510fdf7d))
- **api:** [DEV-318] added building equipment condition ([#304](https://github.com/nclarity/backend/issues/304)) ([fe05b59](https://github.com/nclarity/backend/commit/fe05b59125983fcbc71c421e3b05441d4ea0f3f1))
- **api:** added alerts endpoints for dispatcher page and equipment ([#276](https://github.com/nclarity/backend/issues/276)) ([05a60e5](https://github.com/nclarity/backend/commit/05a60e59138fb740a2f82f44e431d48127894e19))
- **api:** added building severity use case ([#303](https://github.com/nclarity/backend/issues/303)) ([692ca26](https://github.com/nclarity/backend/commit/692ca26c7cb3d962ba50639ab4b645111c716fa1))
- **api:** added config fix for device config endpoint ([05c3460](https://github.com/nclarity/backend/commit/05c3460d92e10c6e01568bd9148861463cc1f75e))
- **api:** added create rule endpoint ([3e2a623](https://github.com/nclarity/backend/commit/3e2a6236bd48c4780b1c7760c345444743a316e4))
- **api:** added customer buildings in customer detail api ([200ddf4](https://github.com/nclarity/backend/commit/200ddf482c9daaa58f421b0f772963692125edc0))
- **api:** added customer status for building ([#305](https://github.com/nclarity/backend/issues/305)) ([bef3e5c](https://github.com/nclarity/backend/commit/bef3e5c001c91b14417f7b1597a45a40e46afc87))
- **api:** added default creation for rules endpoint ([fe7960b](https://github.com/nclarity/backend/commit/fe7960baa349ab0ef8f00a370985242971a2e04d))
- **api:** added endpoint to get alerts for an account ([f3839e8](https://github.com/nclarity/backend/commit/f3839e852e525687284ea655de6dbae2111feae7))
- **api:** added error expects for ts ([6cee564](https://github.com/nclarity/backend/commit/6cee56493853e4847c8bce6dad4b315867aecd77))
- **api:** added get alerts for pulse endpoint ([a3b1843](https://github.com/nclarity/backend/commit/a3b1843e21dcfc58dac61e21f69058c46b7c233c))
- **api:** added get rules endpoint ([81e2f0e](https://github.com/nclarity/backend/commit/81e2f0e3d557efd4d3302433fdd5f9424924cc18))
- **api:** device init configuration ([57ecffd](https://github.com/nclarity/backend/commit/57ecffd7edad6ede0506fcc509fd1604670a13f2))
- **api:** esm enablement fixes ([61074b3](https://github.com/nclarity/backend/commit/61074b35d01d172742a6851876eb40fcb3734d81))
- **api:** esm support ([eb27cf1](https://github.com/nclarity/backend/commit/eb27cf1be85f36ffaddd1fd1656221c076ffbbae))
- **api:** fixed account creation ([6356fd4](https://github.com/nclarity/backend/commit/6356fd47b922e6f2a2e495506703214a394867f7))
- **api:** fixed user password reset ([#288](https://github.com/nclarity/backend/issues/288)) ([686c9f7](https://github.com/nclarity/backend/commit/686c9f7caab709e55bd6b8800294b96974b913d2))
- **api:** fixes based on tests ([1a08d10](https://github.com/nclarity/backend/commit/1a08d107064325b718b4b69a33d8d6b28bb4220a))
- **api:** improved region creation and update ([#293](https://github.com/nclarity/backend/issues/293)) ([6d9c160](https://github.com/nclarity/backend/commit/6d9c160d974080e33ddda5707db97301b06e892b))
- **api:** migrated from axios to got ([5dbdfc9](https://github.com/nclarity/backend/commit/5dbdfc9a46bd1bfc514a882d5e695aec87b849b1))
- **api:** refactored to be a esm package ([8804e73](https://github.com/nclarity/backend/commit/8804e73dbfd5786f53e52baf40f0bf1559efca48))
- **api:** updated blackbox helpers ([e443ff7](https://github.com/nclarity/backend/commit/e443ff7cac5ca4574990cb6e6651c9983ccdf7d5))
- **api:** updated getallpulses service ([806dc6f](https://github.com/nclarity/backend/commit/806dc6f5452a8e73f9e3898faffbd3b96eebc688))
- **api:** updated logging configuration and isolated database packages ([48bfeb5](https://github.com/nclarity/backend/commit/48bfeb55d6872be162e529df0fb80f0fd1be0bda))
- **api:** updated mysql middleware ([1e86dfd](https://github.com/nclarity/backend/commit/1e86dfdf48021bf4da40f723b6e44b1fe1d37665))
- **api:** updated ruleset ([d067859](https://github.com/nclarity/backend/commit/d06785962bdfbd6831389555d6e45875449a28ee))
- **auth:** added params to access token needed to get compability with cloud-api ([7210e24](https://github.com/nclarity/backend/commit/********************12a27adacad06980476e))
- **authentication:** added better authentication for dashboard ([6b93898](https://github.com/nclarity/backend/commit/6b938981e8387392e4326dcc753fdf9e8dae6fd2))
- **auth:** improved auth tokens ([516c993](https://github.com/nclarity/backend/commit/516c99348b6269578398d407b33b235c7d42b9b0))
- **auth:** removed unused items on model ([cb64fcc](https://github.com/nclarity/backend/commit/cb64fcc0106e55455ce6dd6aab644aa495ca338d))
- **authtentication:** added authentication for operations ([8b8ff40](https://github.com/nclarity/backend/commit/8b8ff40d1586a16bb27a6b9ffd28dd0df6a64d8f))
- **calculations:** [DEV-22] updated calculations library to use new names in target values ([1577dcf](https://github.com/nclarity/backend/commit/1577dcfb0d0722c5855e61fbeae7dd4efde36773))
- **calculations:** added condition to calculate values only where there is a call ([1572241](https://github.com/nclarity/backend/commit/15722411d596adfe865c08aaa84774b54c4ca5fe))
- **calculations:** added more calculations ([13711b2](https://github.com/nclarity/backend/commit/13711b2a0b86846e08fa002eb03672f3a74e5d3d))
- **calculations:** added target values calculations ([bc5d677](https://github.com/nclarity/backend/commit/bc5d677044c3d5ae6f02b791d7785844e6d09c1d))
- **calculations:** added telemetry examiner helper ([911e7ec](https://github.com/nclarity/backend/commit/911e7ec1f4acfd4399143831cf5b462c01524e76))
- **calculations:** change from commonjs to esm ([aa76833](https://github.com/nclarity/backend/commit/aa76833da0d09750a5af4c27a7ba139d51398b57))
- **calculations:** fixed to use new types in contracts ([1bc72f4](https://github.com/nclarity/backend/commit/1bc72f4651a7d6e7872b44e78eb31fb71d14376a))
- **calculations:** used new types defined in contracts ([3e8d5b6](https://github.com/nclarity/backend/commit/3e8d5b6b2dfd67c85f8275601d2d9465abe630e8))
- **chart-data:** added service to get chart data based on telemetry and timestamps ([448e7e9](https://github.com/nclarity/backend/commit/448e7e936b0cc4ccdf8061602c1a3e2825cd2ec9))
- **charts:** added compability for colors with frontend ([afe3a18](https://github.com/nclarity/backend/commit/afe3a1849c7567750b7c8cf5a650625126089df4))
- **charts:** added electrical coversion ([b5ebfe4](https://github.com/nclarity/backend/commit/b5ebfe41bbaeff4558c99530719278a403ae3a7b))
- **charts:** added endpoint to get chart data based on timestamps ([2888f28](https://github.com/nclarity/backend/commit/2888f2870b0f20d43ebbbcf6c1fe11e3bea27191))
- **charts:** added factories for chart data ([25f7325](https://github.com/nclarity/backend/commit/25f73253c46bcf4ad227d042962bcbd3c9904e99))
- **charts:** added global setting for luxon to be on utc timezone ([d80ebc2](https://github.com/nclarity/backend/commit/d80ebc27fd61ec111f37fc3ef05acc675f33559a))
- **charts:** added kilo scale to electrical chart ([b176b21](https://github.com/nclarity/backend/commit/b176b2192eb8d86d91f310d64e8b24b063e0d139))
- **charts:** added plotting rules for erroneous data ([51965c4](https://github.com/nclarity/backend/commit/51965c42e10529e9553db56027410e3cc6605acc))
- **charts:** added timestamps to each data point sent on chart data ([5117a6b](https://github.com/nclarity/backend/commit/5117a6b1b5450714bbddf033dcb00f28836ea13f))
- **charts:** changed colors from lowercased colors to uppercased colors ([f45845f](https://github.com/nclarity/backend/commit/f45845f8628c1977620b85ad03ff97cafea6f4c5))
- **charts:** now telemetry data is projected in a more efficient way ([156ffb8](https://github.com/nclarity/backend/commit/156ffb847064abfc58a5783283a00afd781226ff))
- **charts:** removed comments ([aa12740](https://github.com/nclarity/backend/commit/aa12740f84a12c590764a43b1803db10c1cae6e4))
- **charts:** removed projections ([24b38c4](https://github.com/nclarity/backend/commit/24b38c47147f088815df0e092499bffd3f8debd4))
- **charts:** removed temperature conversion on rule applier ([71fcf53](https://github.com/nclarity/backend/commit/71fcf53380cb0bb821ae04533f3c6be93246669c))
- **config:** added config endpoint ([25d6f75](https://github.com/nclarity/backend/commit/25d6f753de480eab5772553c5549e78f69a89697))
- **contracts:** [DEV-180] added contracts package ([#227](https://github.com/nclarity/backend/issues/227)) ([2330f5f](https://github.com/nclarity/backend/commit/2330f5fba9ba926f16e12a6ef1ddec60c22efa33))
- **contracts:** [DEV-269] added new value for rule input value ([#294](https://github.com/nclarity/backend/issues/294)) ([a9abeac](https://github.com/nclarity/backend/commit/a9abeac92dea2f4b96b3f4f58ba0245fe336cadf))
- **contracts:** added calculations types ([b8dc6da](https://github.com/nclarity/backend/commit/b8dc6daf005305da0fe83bb1c9b991e7adb14dfc))
- **contracts:** added call labels ([120fa81](https://github.com/nclarity/backend/commit/120fa810e55a01f671f515ac7153f820532b2a83))
- **contracts:** added cjs and esm exports ([de3fbe5](https://github.com/nclarity/backend/commit/de3fbe5e731e4c8678397427479a3679b56d21db))
- **contracts:** added cjs and esm exports ([#231](https://github.com/nclarity/backend/issues/231)) ([adfb7f0](https://github.com/nclarity/backend/commit/adfb7f0486a2b10add7f65be357c32fafd4ce4c0))
- **contracts:** added default flag for system profile ([#297](https://github.com/nclarity/backend/issues/297)) ([43286ae](https://github.com/nclarity/backend/commit/43286ae42871ad96cecd0d325295a5dc7c51d877))
- **contracts:** added dev branch for release ([4e49f2c](https://github.com/nclarity/backend/commit/4e49f2c2877f709692c6e4421fffeb9f03bf95b7))
- **contracts:** added new exports for pulse config data ([006af64](https://github.com/nclarity/backend/commit/006af6464943d18483d5dcc904722f30d9fe16e3))
- **contracts:** added new exports for pulse config data ([#235](https://github.com/nclarity/backend/issues/235)) ([c35f6ce](https://github.com/nclarity/backend/commit/c35f6cefe6c41e4f30a1cdf7a706402a439a87bc))
- **contracts:** automatic releases enablement ([b4e495f](https://github.com/nclarity/backend/commit/b4e495f5b97bcff7b186fb5c012f4cdcac4b05bd))
- **contracts:** updated contracts package ([#274](https://github.com/nclarity/backend/issues/274)) ([bc1bd0a](https://github.com/nclarity/backend/commit/bc1bd0a4f3a98555d292928fcb3156322b4dad73))
- **contracts:** updated readme in contracts package ([e2168c9](https://github.com/nclarity/backend/commit/e2168c9ef44e90b9a09efba4e4224bfc47f0580c))
- **contracts:** updated release config file ([2beb609](https://github.com/nclarity/backend/commit/2beb609bc1ca8c268a28f60c36935488ec6030c4))
- **contracts:** updated release config file ([#230](https://github.com/nclarity/backend/issues/230)) ([62e460c](https://github.com/nclarity/backend/commit/62e460c3341985bba9662d4c4c61263e870ee91f))
- **contracts:** updated type for customer phone ([dadbb9f](https://github.com/nclarity/backend/commit/dadbb9fdc3f4a50674b13cd93e35f4d11d658319))
- **contracts:** updated workflow ([7ac8a71](https://github.com/nclarity/backend/commit/7ac8a71ce71f58e1de3242a6cbff63c6b83ae49a))
- **contracts:** updated workflow ([#228](https://github.com/nclarity/backend/issues/228)) ([a17f45a](https://github.com/nclarity/backend/commit/a17f45a5def648b8b24dd30958a9ce9d13f45b32))
- **customInisghts:** added cache adapter ([a84aa96](https://github.com/nclarity/backend/commit/a84aa96555ef23393d92ce34c26358422648fd7f))
- **customInsights:** [DEV-22] added better logging for ruleProcessor ([a2d048d](https://github.com/nclarity/backend/commit/a2d048d2c8192fcf8433b033261a53469db1f3bb))
- **customInsights:** [DEV-22] update rule processor ([8f39a80](https://github.com/nclarity/backend/commit/8f39a800a5717d13c4aaf131347a0f6b543c25f7))
- **customInsights:** [NP-22] updated ruleProcessor ([ef0bf88](https://github.com/nclarity/backend/commit/ef0bf88d9e0d99baad99331ad07d6167b91861e2))
- **customInsights:** added account id for enqueuing function ([2e9528f](https://github.com/nclarity/backend/commit/2e9528f9278c88c5ca733fcf19439ef5219aa983))
- **customInsights:** added alerts triggering for process rule ([c5c3173](https://github.com/nclarity/backend/commit/c5c3173bdf0347bc621a2bbb0a46308dd125a91a))
- **customInsights:** added building options ([cd6a630](https://github.com/nclarity/backend/commit/cd6a630e20b5ad1046dcbea41c8f339c39a30f0e))
- **customInsights:** added bulkGetRefrigerants adapter ([5e3d143](https://github.com/nclarity/backend/commit/5e3d143dc38df6f75f821fd3fb296a8d3ab236ae))
- **customInsights:** added calculated values getter ([91459b1](https://github.com/nclarity/backend/commit/91459b165a8dadb94f62d03e47eb27f7c0ade54f))
- **customInsights:** added correct usage for refrigerant ([ca0bbca](https://github.com/nclarity/backend/commit/ca0bbcad78b766272c4c5ca136cec0b54fb2fd5a))
- **customInsights:** added createRule endpoint ([fa6fb7f](https://github.com/nclarity/backend/commit/fa6fb7f14cd397f1677fe26d90e27c6f25ba763c))
- **customInsights:** added function to start processing all rules ([5d98a0e](https://github.com/nclarity/backend/commit/5d98a0e983dc49679542ae937c30261ca4047a1a))
- **customInsights:** added new apis for database adapter ([5779896](https://github.com/nclarity/backend/commit/57798968e4d1faeb736606020b3fa7174c91e604))
- **customInsights:** added sequelize adapter ([1d8388f](https://github.com/nclarity/backend/commit/1d8388f15f60ddedb9db1f83560fed85b47f971a))
- **customInsights:** added target values calculations ([cf731d7](https://github.com/nclarity/backend/commit/cf731d7506724f9a891cb35ad2e24f7a791be616))
- **customInsights:** added telemetry schema ([aad783e](https://github.com/nclarity/backend/commit/aad783e0b093554606ea91570c49c0b6eda6ab46))
- **customInsights:** added telemetryTransformations ([9b0e40a](https://github.com/nclarity/backend/commit/9b0e40a58de1a959b9611d733ad39062df3fa05c))
- **customInsights:** added transformer ([8fd4514](https://github.com/nclarity/backend/commit/8fd4514a2b49bb8fb9913d1d645ea853c576223e))
- **customInsights:** added transformer usage ([51de564](https://github.com/nclarity/backend/commit/51de564e29b5f96d5be342121fce28edb7241702))
- **customInsights:** added weather adapter ([80892fd](https://github.com/nclarity/backend/commit/80892fdcd56958b5da9d72b9e215edee04bf48cf))
- **customInsights:** changed business rules based on new rule set model ([c1ba87e](https://github.com/nclarity/backend/commit/c1ba87ed6956d0e0343c4acb132f1887f74275fb))
- **customInsights:** changed rule model ([c6d30e2](https://github.com/nclarity/backend/commit/c6d30e2ccae2566100cbc5b22b0abbe4e418c900))
- **customInsights:** improved adapters based on tsconfig changes ([55be4a8](https://github.com/nclarity/backend/commit/55be4a89b56b5efb8466b857a82dd0dea263fdcb))
- **customInsights:** improved connections to services ([bf6366a](https://github.com/nclarity/backend/commit/bf6366a265b7e417e718cfadbbf459d9c7d64c70))
- **customInsights:** improved model and added module type ([df87697](https://github.com/nclarity/backend/commit/df87697b857c8cde6c13edd2819582f136c1adda))
- **customInsights:** initial rule processor created ([7aa42aa](https://github.com/nclarity/backend/commit/7aa42aa73aa3c2b1fddd4e62d6459eda05dd0dc6))
- **customInsights:** installed telemetry package ([3f2ddc8](https://github.com/nclarity/backend/commit/3f2ddc8e61c3ab4569a02251423dc0954db45d14))
- **customInsights:** modified adapter to use account id ([98e8beb](https://github.com/nclarity/backend/commit/98e8bebb1a2ee53c390d8006ac30435654455b9f))
- **customInsights:** updated params for processRule function ([998cdde](https://github.com/nclarity/backend/commit/998cdde50bbf463bbd1ddd564df7be9bd2a5e9a2))
- **customInsights:** usage for processRule improved ([de372ea](https://github.com/nclarity/backend/commit/de372eac4f8b9d078ffec0fa0737c4ff44fbe43f))
- **dashboard:** fixed data in pulse view ([efc87e9](https://github.com/nclarity/backend/commit/efc87e99bd075795f8531412bab5036279676022))
- **database:** [DEV-167] updated references to foreign key in account model ([2953a47](https://github.com/nclarity/backend/commit/2953a479ab0975ddf34324a9f338dbf68fd86438))
- **database:** [DEV-258] building addition fields ([#289](https://github.com/nclarity/backend/issues/289)) ([b9a4e01](https://github.com/nclarity/backend/commit/b9a4e010797167cd7e5e11e0575adf392278960b))
- **database:** [NP-20] updated mongo model ([5781d11](https://github.com/nclarity/backend/commit/5781d11f8459795cebe088ed0a31e00b37f1032f))
- **database:** [NP-21] update schema to use groups and sets ([aa2813a](https://github.com/nclarity/backend/commit/aa2813a89bcfc916fbb11c96b913ca58ae2acff4))
- **database:** added accountId field for alerts ([1b18510](https://github.com/nclarity/backend/commit/1b185102ba1dabb0ddb1e3cad0805ce8b4f18626))
- **database:** added model for a rule ([920e8f8](https://github.com/nclarity/backend/commit/920e8f81546378aa2e97aa3f9ae68c227adc2ab0))
- **database:** added readme and updated package json ([3047917](https://github.com/nclarity/backend/commit/30479178544bd705abcf0c77f564285a80e8ba74))
- **database:** added refrigerants model ([29bbd1a](https://github.com/nclarity/backend/commit/29bbd1a1277f57423e7dd84cb32cc8bc8a630585))
- **database:** added semantic-release ([8a20478](https://github.com/nclarity/backend/commit/8a204782a324a508788b32b281d7b126049522d1))
- **database:** added telemetry schema validation ([14085d3](https://github.com/nclarity/backend/commit/14085d355f903c5885e7d2a35990e035f0416495))
- **database:** added usage of groups and account id ([68415d3](https://github.com/nclarity/backend/commit/68415d3ffccfad783e32285d2ac90f4d20db63a3))
- **database:** added validation for comparisonValueLabel ([c59563e](https://github.com/nclarity/backend/commit/c59563e4933ae5449eff4ef82ccddd985e3022fe))
- **database:** added validationSchemas folder ([94c01c7](https://github.com/nclarity/backend/commit/94c01c7d2e8c9b1d885caf4875fce0b4594d6e09))
- **database:** improved alerts and rules documents ([80d2939](https://github.com/nclarity/backend/commit/80d29391a520bcec2b642a488da019ac3ff8f02b))
- **database:** improved refrigerant models ([d1916e4](https://github.com/nclarity/backend/commit/d1916e4e8785184dfeb0c507e40e6d2fa365cd7d))
- **database:** improved rule model ([4f76212](https://github.com/nclarity/backend/commit/4f762127cc269d26d6f6307649b9451988f582ee))
- **database:** improved rule model with mixed type ([6f1a321](https://github.com/nclarity/backend/commit/6f1a3218815086260cea30bdaee4d8416af0e3d8))
- **database:** improved telemetry data object ([5528ca9](https://github.com/nclarity/backend/commit/5528ca9180dab197aba9463155a14613e99e2010))
- **database:** imrpoved rule definition and added alert model ([375cc93](https://github.com/nclarity/backend/commit/375cc934747d082bec0eb1e6fb20ca2820225111))
- **database:** migrated to esm type ([52b471f](https://github.com/nclarity/backend/commit/52b471f13ae18f9589a8080d3281bc55cdc3fbbe))
- **database:** model synchronization ([a5b5090](https://github.com/nclarity/backend/commit/a5b5090e402d36e069993f080088b4e46f9f86e0))
- **database:** split rules validations ([0b16136](https://github.com/nclarity/backend/commit/0b16136bf9bffd9c13c4bfe8db1dd5efd620b20d))
- **database:** typescript checks improvements ([34ea281](https://github.com/nclarity/backend/commit/34ea2813c2d31089ae03e2a09a39278b333135d5))
- **database:** updated models according to rulesets ([985a195](https://github.com/nclarity/backend/commit/985a1952c8dbe10ed2062763ab5ded3ac09c3aee))
- **database:** updated rule to match requirements ([5aa47e4](https://github.com/nclarity/backend/commit/5aa47e44b6f4aa22cf637fe1b2fef011b01a9425))
- **database:** updated target value labels ([f64b5dc](https://github.com/nclarity/backend/commit/f64b5dcd475077759a9b2f3ef289d158aa2fcd2d))
- **database:** updated target values to be separated ([387b920](https://github.com/nclarity/backend/commit/387b920ef031ac4cb09aed74e96d05307455b75d))
- **database:** updated validation schemas ([f19016c](https://github.com/nclarity/backend/commit/f19016c8b054a342657063079a1ec9723046b020))
- default rules ([#328](https://github.com/nclarity/backend/issues/328)) ([ed64793](https://github.com/nclarity/backend/commit/ed647933599cb329755d9765f42eda84ffc0fa4f))
- **DEV-28:** custom insights backend integration ([#234](https://github.com/nclarity/backend/issues/234)) ([47a1f16](https://github.com/nclarity/backend/commit/47a1f16980642ec6c671f2d5773bd6e1707b67e8))
- equipment addition and updates made ([07dc1ef](https://github.com/nclarity/backend/commit/07dc1ef80848dd5d9bd336e1535282d225b29300))
- **equipment:** added create equipment use case ([50ab1fe](https://github.com/nclarity/backend/commit/50ab1fe882ff0cc4a61eb22d0d933d7fdc34df1c))
- **equipment:** added deletion of equipment ([5144b45](https://github.com/nclarity/backend/commit/5144b4597d7ee04e8897b0d3925b74854b5f941d))
- **equipment:** added equipment router ([499db04](https://github.com/nclarity/backend/commit/499db04146a8075ed7f574f893ff6f2e842da416))
- **equipment:** created controller and use to get equipment by id ([e7c98f0](https://github.com/nclarity/backend/commit/e7c98f0b33e000cc831dab135e05d727bc69f9df))
- **equipment:** created use case to update equipment profile ([0a6a92c](https://github.com/nclarity/backend/commit/0a6a92cf913bffca9e1bc98c11525bd5f4ee131f))
- **equipmentProfile:** enabled virtual to refer to system profile ([ab2bc15](https://github.com/nclarity/backend/commit/ab2bc15338f01db00199d3f136f68ac28e2a0e42))
- **equipmentTags:** delete tags endpoint ([c99f2df](https://github.com/nclarity/backend/commit/c99f2df2601f3b7e5dbf86f050336e51b582b9a5))
- **factories:** added factories for users and pulses with typings ([bcd9068](https://github.com/nclarity/backend/commit/bcd90683f55156bffc847a58a27d499c633dfbfb))
- **functions:** added back eventhub name ([530b6aa](https://github.com/nclarity/backend/commit/530b6aa1dca7063964bf55ab44a5f4cc46290144))
- **functions:** added device config fuction ([fd6d2a5](https://github.com/nclarity/backend/commit/fd6d2a587f278fced4b08c5b5bff6e6bd650f306))
- **functions:** added endpoint to get pulses report ([79142be](https://github.com/nclarity/backend/commit/79142be0f1a73dafec3339de634892a7c0f67229))
- **functions:** added functions from backend function repo ([25c1334](https://github.com/nclarity/backend/commit/25c1334512dd806be33b99a00726f67e51e4c1b3))
- **functions:** added recoveredTelemetry exporter ([c6048a6](https://github.com/nclarity/backend/commit/c6048a6fbde32246c349d1a29759f04f0f70ed91))
- **functions:** added truthy keywords for custom value ([#319](https://github.com/nclarity/backend/issues/319)) ([2e0447a](https://github.com/nclarity/backend/commit/2e0447a6d92859aa7f972cab82c3ca8eb61f530e))
- **functions:** change prod eventhub ([d5dbd67](https://github.com/nclarity/backend/commit/d5dbd67f756a454f5574636168e3433cb8f24723))
- **functions:** device init config ([7b3e186](https://github.com/nclarity/backend/commit/7b3e186f3ab83f2f5ae39b3d67a7cd00eeb133c5))
- **functions:** esm enablement for functions app ([df64e32](https://github.com/nclarity/backend/commit/df64e32151a95e2875e29e18467fab8524593c27))
- **functions:** esm support ([1622ec7](https://github.com/nclarity/backend/commit/1622ec735bd90f5fe2454fe134262ebdb87ddbca))
- **functions:** improved functions configuration ([d437cb4](https://github.com/nclarity/backend/commit/d437cb4119c8f5663cebdee4a20a65bf67ab40e5))
- **functions:** reduced logging for database exporter ([badd736](https://github.com/nclarity/backend/commit/badd736abb2597e1a384f75494750d965b329a40))
- **functions:** reduced logging for database exporter ([#246](https://github.com/nclarity/backend/issues/246)) ([a19e44f](https://github.com/nclarity/backend/commit/a19e44f4e5a6b5719661f6d9d5ddc9973e311fdd))
- **functions:** reduced the size for the batches in the event hub ([001facc](https://github.com/nclarity/backend/commit/001facca0d8844084acf63f9384320b179bb5416))
- **functions:** refactored to be a esm ([ea204af](https://github.com/nclarity/backend/commit/ea204af2ab43e48e25ee29f49b178bc55d211559))
- **functions:** removed undesired prop assignment ([42b1d90](https://github.com/nclarity/backend/commit/42b1d90946471ca7ec1094af285feaa181099f2f))
- **functions:** updated deployment function ([12866f0](https://github.com/nclarity/backend/commit/12866f03ac5493346d4be6b21f46e7fa7835d47b))
- **functions:** updated device template id in device init ([36a4e70](https://github.com/nclarity/backend/commit/36a4e70c81e5e95275a9825fa85187fa67a91ead))
- **functions:** updated event hub name ([7049bb7](https://github.com/nclarity/backend/commit/7049bb71f3bc037e1ca706aecda38cf49c14f677))
- **functions:** updated eventHubName ([93ac887](https://github.com/nclarity/backend/commit/93ac887b1bec89c9c8c6c76bba27b29eec3bb057))
- **functions:** updated frequency for pulses ([a4c3b00](https://github.com/nclarity/backend/commit/a4c3b0098930bb7bbd84e9fdb5dc443286c92f2a))
- **functions:** updated function def to use params and queries ([fcc8759](https://github.com/nclarity/backend/commit/fcc8759cc8c854489234787d4c35dcfb90800b6e))
- **functions:** updated host json and report endpoint ([a44a1dd](https://github.com/nclarity/backend/commit/a44a1ddd64b33fc308d4093ea4f65ec73e452747))
- **functions:** updated ruleInputGetter to use new types ([2078489](https://github.com/nclarity/backend/commit/207848998dca807ccc4eb2d73a388f652b2e04d7))
- **functions:** updated the event hub name for production ([fee8016](https://github.com/nclarity/backend/commit/fee80166565511fc2f0b5e0bc9cef03e89d37292))
- **getTelemetryData:** finished add chart data methods ([8eecd37](https://github.com/nclarity/backend/commit/8eecd370f5b415a14ebfafa022566eb60ea83581))
- **insights:** added account filtering and edge cases ([6941e8d](https://github.com/nclarity/backend/commit/6941e8db1397c0cb3a3964b6f8831a3774173bab))
- **insights:** added getInsightsByPulses service ([eadfacb](https://github.com/nclarity/backend/commit/eadfacb4c356994eea7bce9fb9497865b682436e))
- **insights:** improved pagination ([e82e783](https://github.com/nclarity/backend/commit/e82e7838fe0b59765650c80c0b6294a103bfc83c))
- **insights:** minimal endpoint to get insight history ([223fd59](https://github.com/nclarity/backend/commit/223fd593ceb4f1b6903d4e42b4149849c7f6c03f))
- **insights:** pulses by user selected ([857f30b](https://github.com/nclarity/backend/commit/857f30b164ffba3687767bf0fb3dd0e4883d921d))
- **logger:** made application insights transport optional locally ([913970c](https://github.com/nclarity/backend/commit/913970c39b49b4753d6ae2bc12a3037f89ed6d4b))
- **newUserCredentials:** changed emails ([62027c5](https://github.com/nclarity/backend/commit/62027c543de03a701cea869cd07eb27d740ab38d))
- no app deplpoy ([#259](https://github.com/nclarity/backend/issues/259)) ([81cc9bd](https://github.com/nclarity/backend/commit/81cc9bd364c25486ad01a6a39f406647e8ba6601))
- **opsUser:** reset password feature ([40bcd37](https://github.com/nclarity/backend/commit/40bcd37a18225708e0411c2aed84c92155267ab3))
- **opsUsers:** added edit ops user service ([0386249](https://github.com/nclarity/backend/commit/0386249c0807c51febcb03712d1de7cc8f6d4887))
- **opsUsers:** added router and service to get all ops users ([75ded15](https://github.com/nclarity/backend/commit/75ded1561da7d75ee13848b4fc4fe63b12d8ffb7))
- **opsUsers:** delete and get ops user by id service added ([3907161](https://github.com/nclarity/backend/commit/3907161a3aa6b61ff7edd0cff118320b77294e1f))
- **pulse:** added get pulse config endpoint ([d5edab1](https://github.com/nclarity/backend/commit/d5edab16835dc59c5fe2f8c6f326e8bb4f2c8f2e))
- **pulses:** changed type naming on pulses factory ([981c0cd](https://github.com/nclarity/backend/commit/981c0cd3990fa92ec76a1c3b6769dc0ee1978f5c))
- **pulses:** converted saturation temperature values to celcius ([3aae667](https://github.com/nclarity/backend/commit/3aae667c054a9494bbc64f4437b23a8a78973a69))
- **pulses:** only send eer data when there is a call for cool ([fbcc7ae](https://github.com/nclarity/backend/commit/fbcc7aec28de0622d4e7544dbe3a30a97024de56))
- **pulses:** pulses deletion corrected ([b0949e8](https://github.com/nclarity/backend/commit/b0949e810c96c89aa5f2ad9e371b467c6ad8a44c))
- **pulses:** type telemetry data and removed unnecessary code ([117f8c7](https://github.com/nclarity/backend/commit/117f8c7ffbd44935bc650374e118279098ec05c1))
- replaced bcrypt with bcryptjs ([a74c9b3](https://github.com/nclarity/backend/commit/a74c9b347354134b4776c2d777c46415e95c7f30))
- reviewed insights ([198e7ce](https://github.com/nclarity/backend/commit/198e7ce1e69dd9e69671ac4f41b46938f6818211))
- **services/createUser:** added new email for user credentials ([ade5e85](https://github.com/nclarity/backend/commit/ade5e85ec90cd86845fe3446dd0f9b3030b19b1f))
- share RTU Scorecard ([#61](https://github.com/nclarity/backend/issues/61)) ([5ffb4a9](https://github.com/nclarity/backend/commit/5ffb4a9a12a2deb0e47222b0326b7eada29625ca))
- **systemprofiled:** started entity ([3de1db3](https://github.com/nclarity/backend/commit/3de1db34a6e742b4f79ce6794e4fec39f2766e1e))
- **systemProfiles:** added router ([e08d943](https://github.com/nclarity/backend/commit/e08d9431c1957fcce7747bd2a45e19623b8fd5a9))
- **tags:** added services to get tags by pulses and account ([8f07919](https://github.com/nclarity/backend/commit/8f0791967113c790fd073a8e9d71108facd48d0e))
- **tags:** added tags services to create and list ([29c891d](https://github.com/nclarity/backend/commit/29c891d3227e5cb39899450b4769645ee33ad50e))
- **telemetry:** added calculations ([80022bf](https://github.com/nclarity/backend/commit/80022bf2de6ff433ed6d032e671b01efa3b8d38a))
- **telemetry:** added calculations ([8ca1564](https://github.com/nclarity/backend/commit/8ca15641e93a51e184a932b8206b58999a4decd0))
- **telemetry:** added customPsychometrics ([18686d6](https://github.com/nclarity/backend/commit/18686d68e7a8650789ffb807f3fd15511cfaca12))
- **telemetry:** added documentation for telemetry values ([919c66b](https://github.com/nclarity/backend/commit/919c66bfd5509de06359ce249e15eb1b1e3c06e0))
- **telemetry:** added more calculations ([9aed02a](https://github.com/nclarity/backend/commit/9aed02a37ed075effa158755e9035cb7a9343e72))
- **telemetry:** added sideEffects field in telemetry package ([fe2cab8](https://github.com/nclarity/backend/commit/fe2cab8d9ee6c4b133b24886f6ecbe28158ae9bc))
- **telemetryData:** modified getTelemetryData endpoint to manage lastCall data ([c255f2b](https://github.com/nclarity/backend/commit/c255f2b17ed62ac7d192bbc9ffbdfd30c5fefae1))
- **telemetry:** simplified calculations ([615ef4a](https://github.com/nclarity/backend/commit/615ef4abb6481fb2d0fad0559146bd430c9e3aa1))
- **telemetry:** started to add calculations ([0c5b794](https://github.com/nclarity/backend/commit/0c5b7949b63fabb0469dca2fada975bb615bc75e))
- **user:** added re-add users when deleted ([be515e2](https://github.com/nclarity/backend/commit/be515e2ce64ffca22f7d53e56ade4dc766f6fccd))
- **user:** modified display name ([22d448b](https://github.com/nclarity/backend/commit/22d448bb19c4392990d6a88508a337ea2017e368))
- **users:** added active clause when deleted and re-adding user ([74f7588](https://github.com/nclarity/backend/commit/74f75881617d8ffd8266602fec4118c494267940))
- **users:** added get users endpoint ([77e04f4](https://github.com/nclarity/backend/commit/77e04f4137821a5af058fd9540c001ff2ffe1698))
- **users:** added user addition endpoint ([0841a88](https://github.com/nclarity/backend/commit/0841a884d744c2a581252ededc0f57b7152d0f84))
- **users:** added user deletion endpoint dashboard[#394](https://github.com/nclarity/backend/issues/394) ([ccfc6bb](https://github.com/nclarity/backend/commit/ccfc6bbef9276250f8948c136b14f514f728aa4d))
- **users:** added user edition endpoint dashboard[#393](https://github.com/nclarity/backend/issues/393) ([55d908f](https://github.com/nclarity/backend/commit/55d908fe5cf7a1f8d96d6a12554bfdc6c3f22a9e))
- **users:** make service to not be able to delete self ([9f2a3b6](https://github.com/nclarity/backend/commit/9f2a3b600795339e0e408fb71a9e2ae23bb432f9))

### Reverts

- Revert "chore: release 04/08/2023" [skip ci] (#323) ([dd0671d](https://github.com/nclarity/backend/commit/dd0671dbe1fb5a004020e5a7a87855b177e68536)), closes [#323](https://github.com/nclarity/backend/issues/323) [#322](https://github.com/nclarity/backend/issues/322)
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([1d965b0](https://github.com/nclarity/backend/commit/1d965b0d9d377a30b567ad289c114be8076408a9))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([644ebd8](https://github.com/nclarity/backend/commit/644ebd8caebac67404ee4484c15e18e6dc599c87))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([037f7e2](https://github.com/nclarity/backend/commit/037f7e2d76d4a2d1ee5df4529ef4fc5fd8e865db))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([5ad3d2b](https://github.com/nclarity/backend/commit/5ad3d2bc2cb74f11c7a0ab8252b4045796d9119b))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([532af29](https://github.com/nclarity/backend/commit/532af29aba22a40aa36d7620b72585375ca47863))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.5@1.0.0-beta.5 [skip ci]" ([67f0049](https://github.com/nclarity/backend/commit/67f0049806941a8a948572ec7ff0fbea9cdceae1))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([fc35fd7](https://github.com/nclarity/backend/commit/fc35fd77d671f81c6c2f78e25154d170f63d3a93))
- Revert "bugfix(authentication): fixed auth middleware" ([d1ef2a3](https://github.com/nclarity/backend/commit/d1ef2a38c923223f3cca3f51b0d971bb14105de5))
- Revert "fix(pulses): update of pulse warranty data" ([b7a2965](https://github.com/nclarity/backend/commit/b7a29657ce443e5349027f5d76704992313b0be5))

### BREAKING CHANGES

- updated relational database models based in the prisma output
- this package is now esm

# [1.0.0-dev.26](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.25...@nclarity/contracts-v1.0.0-dev.26) (2023-08-04)

### Bug Fixes

- [DEV-237] added lastsignstamp value in service ([#266](https://github.com/nclarity/backend/issues/266)) ([25273a0](https://github.com/nclarity/backend/commit/25273a01980a31c789797141cdc7e9501117fcf0))
- [DEV-245] fixed update for equipment ([#282](https://github.com/nclarity/backend/issues/282)) ([dbdc4d6](https://github.com/nclarity/backend/commit/dbdc4d6bff670cecfdf8e5a5b8c0ab6b0e7254a6))
- [DEV-248] added nullish operator in device config ([#285](https://github.com/nclarity/backend/issues/285)) ([7221130](https://github.com/nclarity/backend/commit/7221130439fb0755fc5bb07caffc69968b004406))
- **api:** [DEV-235] no dashboard data ([#268](https://github.com/nclarity/backend/issues/268)) ([eed96ba](https://github.com/nclarity/backend/commit/eed96ba20447243fa22fcff4218be5732fc696a9))
- **api:** [DEV-235] updated default values in condenser ([#265](https://github.com/nclarity/backend/issues/265)) ([7851617](https://github.com/nclarity/backend/commit/785161753f2d8ce472be0cfdfd47b2e59af614c1))
- **api:** [DEV-236] updated default values in condenser ([#267](https://github.com/nclarity/backend/issues/267)) ([bf5eb25](https://github.com/nclarity/backend/commit/bf5eb25355ddabacf44ac73ff9c04d13e4928f60))
- deployment script function app ([66d3af4](https://github.com/nclarity/backend/commit/66d3af4da931849afa1c2807241c1fc96a943eaf))
- fixed account creation script ([#281](https://github.com/nclarity/backend/issues/281)) ([c2ab073](https://github.com/nclarity/backend/commit/c2ab0730e9115d30850f65d72fcefee15429622e))
- **functions:** added multiple any for rules ([c046a16](https://github.com/nclarity/backend/commit/c046a169505903824327e0b589a7f5f70ac63da3))
- **functions:** improved transformations ([#309](https://github.com/nclarity/backend/issues/309)) ([5b67137](https://github.com/nclarity/backend/commit/5b671374c4cd901eb32683aa709f0d28f9808bb7))

### Features

- **all:** [DEV-374] insights not tripping ([#325](https://github.com/nclarity/backend/issues/325)) ([01420a1](https://github.com/nclarity/backend/commit/01420a137e2aef432fbf56f6a8a8ab3d85c8fdfe))
- **all:** changed system type reference ([994c2a6](https://github.com/nclarity/backend/commit/994c2a687b1c354c604654e555b98bd3da2a60e9))
- **all:** changed system type reference ([#326](https://github.com/nclarity/backend/issues/326)) ([58a9e75](https://github.com/nclarity/backend/commit/58a9e75892eb11452bbb873461362c62901d5a0f))
- **api:** added default creation for rules endpoint ([fe7960b](https://github.com/nclarity/backend/commit/fe7960baa349ab0ef8f00a370985242971a2e04d))

### Reverts

- Revert "chore: release 04/08/2023" [skip ci] (#323) ([dd0671d](https://github.com/nclarity/backend/commit/dd0671dbe1fb5a004020e5a7a87855b177e68536)), closes [#323](https://github.com/nclarity/backend/issues/323) [#322](https://github.com/nclarity/backend/issues/322)

# [1.0.0-dev.25](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.24...@nclarity/contracts-v1.0.0-dev.25) (2023-08-04)

### Bug Fixes

- excluded local settings json ([7e3c5fd](https://github.com/nclarity/backend/commit/7e3c5fd9165de4488e7a59b4e5ca3f30dcf48e46))

# [1.0.0-dev.24](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.23...@nclarity/contracts-v1.0.0-dev.24) (2023-08-04)

### Bug Fixes

- **all:** refactoring buildFunctionApp script ([#321](https://github.com/nclarity/backend/issues/321)) ([cc26757](https://github.com/nclarity/backend/commit/cc2675776c194a609d93d5237a36e45b36ca67cc))

# [1.0.0-dev.23](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.22...@nclarity/contracts-v1.0.0-dev.23) (2023-08-03)

### Features

- **contracts:** added call labels ([120fa81](https://github.com/nclarity/backend/commit/120fa810e55a01f671f515ac7153f820532b2a83))

# [1.0.0-dev.22](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.21...@nclarity/contracts-v1.0.0-dev.22) (2023-08-03)

### Features

- **all:** added call for cool and call for heat calculations ([#320](https://github.com/nclarity/backend/issues/320)) ([985063d](https://github.com/nclarity/backend/commit/985063d76e6b0873dea3505ee5a7d3dfd83aa062))

# [1.0.0-dev.21](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.20...@nclarity/contracts-v1.0.0-dev.21) (2023-08-01)

### Features

- **functions:** added truthy keywords for custom value ([#319](https://github.com/nclarity/backend/issues/319)) ([2e0447a](https://github.com/nclarity/backend/commit/2e0447a6d92859aa7f972cab82c3ca8eb61f530e))

# [1.0.0-dev.20](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.19...@nclarity/contracts-v1.0.0-dev.20) (2023-08-01)

### Bug Fixes

- [DEV-352] calculations ([#318](https://github.com/nclarity/backend/issues/318)) ([c50c2f9](https://github.com/nclarity/backend/commit/c50c2f9730bf3fdcdeb48fae9763bc073a0304f7))
- **all:** [DEV-352] review insight feedback ([#317](https://github.com/nclarity/backend/issues/317)) ([24b3fad](https://github.com/nclarity/backend/commit/24b3fad47145183fc42b0d7c8601e19f54ba13bc))

# [1.0.0-dev.19](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.18...@nclarity/contracts-v1.0.0-dev.19) (2023-07-31)

### Bug Fixes

- tsc errors ([f8a2c89](https://github.com/nclarity/backend/commit/f8a2c896f349101fccd61230e0e62cf8c6d512ce))

### Features

- **all:** [DEV-270] edit insight ([#314](https://github.com/nclarity/backend/issues/314)) ([bedc5ed](https://github.com/nclarity/backend/commit/bedc5ed5b95ac0c20d8ffb4b360364b052861cee))
- **all:** [DEV-305] delete insight ([#313](https://github.com/nclarity/backend/issues/313)) ([a750d48](https://github.com/nclarity/backend/commit/a750d4898a1afb7f08fc3a9a607baba46fade03d))

# [1.0.0-dev.18](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.17...@nclarity/contracts-v1.0.0-dev.18) (2023-07-27)

### Features

- **all:** [DEV-352] review insight ([#312](https://github.com/nclarity/backend/issues/312)) ([67565a0](https://github.com/nclarity/backend/commit/67565a0a9eac7b8c0831c6c029a7664cbe806b59))

# [1.0.0-dev.17](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.16...@nclarity/contracts-v1.0.0-dev.17) (2023-07-26)

### Bug Fixes

- [DEV-335] sorting order fix ([c8f54f3](https://github.com/nclarity/backend/commit/c8f54f35f40e1cfdd8f964b9e8def3448c771fda))
- updated regions in auth context and edit customer ([#308](https://github.com/nclarity/backend/issues/308)) ([b664cd0](https://github.com/nclarity/backend/commit/b664cd052b7ff019dae2b3e972ea979460f8dc6a))

### Features

- **all:** [DEV-354] telemetry calculations ([#310](https://github.com/nclarity/backend/issues/310)) ([8b4109c](https://github.com/nclarity/backend/commit/8b4109c0466e0c1603d8574ad3871cca969aad10))

# [1.0.0-dev.16](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.15...@nclarity/contracts-v1.0.0-dev.16) (2023-07-21)

### Features

- **all:** production prepareness ([#307](https://github.com/nclarity/backend/issues/307)) ([8144e27](https://github.com/nclarity/backend/commit/8144e27ed384b992d2ad012b716464218cb021c8))

# [1.0.0-dev.15](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.14...@nclarity/contracts-v1.0.0-dev.15) (2023-07-20)

### Bug Fixes

- refrigerants seeder ([#306](https://github.com/nclarity/backend/issues/306)) ([1f38a64](https://github.com/nclarity/backend/commit/1f38a6461d1a0e753d0fa4cd1365648cc7dbb671))

### Features

- **api:** [DEV-318] added building equipment condition ([#304](https://github.com/nclarity/backend/issues/304)) ([fe05b59](https://github.com/nclarity/backend/commit/fe05b59125983fcbc71c421e3b05441d4ea0f3f1))
- **api:** added customer status for building ([#305](https://github.com/nclarity/backend/issues/305)) ([bef3e5c](https://github.com/nclarity/backend/commit/bef3e5c001c91b14417f7b1597a45a40e46afc87))

# [1.0.0-dev.14](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.13...@nclarity/contracts-v1.0.0-dev.14) (2023-07-18)

### Bug Fixes

- [DEV-313] fixed system profile creation ([#301](https://github.com/nclarity/backend/issues/301)) ([b9bbd89](https://github.com/nclarity/backend/commit/b9bbd89746c28ed399e911884342afeea8ba4ed2))

### Features

- **api:** [DEV-309] added insight history and pulse history list endpoints ([#300](https://github.com/nclarity/backend/issues/300)) ([43303ed](https://github.com/nclarity/backend/commit/43303ed4056cf94bf9df64a2ff157f5b510fdf7d))
- **api:** added building severity use case ([#303](https://github.com/nclarity/backend/issues/303)) ([692ca26](https://github.com/nclarity/backend/commit/692ca26c7cb3d962ba50639ab4b645111c716fa1))

# [1.0.0-dev.13](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.12...@nclarity/contracts-v1.0.0-dev.13) (2023-07-14)

### Features

- **all:** [DEV-64] send data issues ([#299](https://github.com/nclarity/backend/issues/299)) ([b78deef](https://github.com/nclarity/backend/commit/b78deef287cd149849022d140ab20ad4a1fd45b3))
- **api:** [DEV-271] refactored equipment profile creation ([#298](https://github.com/nclarity/backend/issues/298)) ([24730d1](https://github.com/nclarity/backend/commit/24730d1cd94039064a359de31df6303fcf2a8364))
- **dashboard:** fixed data in pulse view ([efc87e9](https://github.com/nclarity/backend/commit/efc87e99bd075795f8531412bab5036279676022))

# [1.0.0-dev.12](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.11...@nclarity/contracts-v1.0.0-dev.12) (2023-07-13)

### Features

- **contracts:** added default flag for system profile ([#297](https://github.com/nclarity/backend/issues/297)) ([43286ae](https://github.com/nclarity/backend/commit/43286ae42871ad96cecd0d325295a5dc7c51d877))

# [1.0.0-dev.11](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.10...@nclarity/contracts-v1.0.0-dev.11) (2023-07-13)

### Features

- **all:** [DEV-268] phase based telemetry values sum ([#296](https://github.com/nclarity/backend/issues/296)) ([2c07cf6](https://github.com/nclarity/backend/commit/2c07cf68377502ef5ace7efc47f7cd1466678343))

# [1.0.0-dev.10](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.9...@nclarity/contracts-v1.0.0-dev.10) (2023-07-12)

### Features

- **api:** improved region creation and update ([#293](https://github.com/nclarity/backend/issues/293)) ([6d9c160](https://github.com/nclarity/backend/commit/6d9c160d974080e33ddda5707db97301b06e892b))
- **contracts:** [DEV-269] added new value for rule input value ([#294](https://github.com/nclarity/backend/issues/294)) ([a9abeac](https://github.com/nclarity/backend/commit/a9abeac92dea2f4b96b3f4f58ba0245fe336cadf))

# [1.0.0-dev.9](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.8...@nclarity/contracts-v1.0.0-dev.9) (2023-07-12)

### Features

- **api:** [DEV-260] equipment photos ([#291](https://github.com/nclarity/backend/issues/291)) ([523df0f](https://github.com/nclarity/backend/commit/523df0f6902b82008ecc2e6227bdd9e232a04ba0))
- **api:** [DEV-266] user safeguards ([#290](https://github.com/nclarity/backend/issues/290)) ([600d01e](https://github.com/nclarity/backend/commit/600d01efc86552f94e262ba9a4f3e9f36d7b55a2))
- **api:** [DEV-292] simulated data endpoints ([#292](https://github.com/nclarity/backend/issues/292)) ([ad49034](https://github.com/nclarity/backend/commit/ad49034c9e22bd6f0f9dbf698eee57354948e5cc))

# [1.0.0-dev.8](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.7...@nclarity/contracts-v1.0.0-dev.8) (2023-07-06)

### Features

- **api:** fixed user password reset ([#288](https://github.com/nclarity/backend/issues/288)) ([686c9f7](https://github.com/nclarity/backend/commit/686c9f7caab709e55bd6b8800294b96974b913d2))
- **database:** [DEV-258] building addition fields ([#289](https://github.com/nclarity/backend/issues/289)) ([b9a4e01](https://github.com/nclarity/backend/commit/b9a4e010797167cd7e5e11e0575adf392278960b))

# [1.0.0-dev.7](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.6...@nclarity/contracts-v1.0.0-dev.7) (2023-07-05)

### Features

- **all:** equipment addition logic ([#287](https://github.com/nclarity/backend/issues/287)) ([49e5413](https://github.com/nclarity/backend/commit/49e541300eb35f7d55e9e058dbf83e411e1248b4))
- **api:** [DEV-242] dispatcher page apis ([#286](https://github.com/nclarity/backend/issues/286)) ([db69351](https://github.com/nclarity/backend/commit/db69351964a567947faad5aa1d6a1e3a2473b52e))

# [1.0.0-dev.6](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.5...@nclarity/contracts-v1.0.0-dev.6) (2023-06-27)

### Bug Fixes

- **api:** fixed abort signal in bundle ([7bcba10](https://github.com/nclarity/backend/commit/7bcba1026f04ba0b3cb64fb8b0a57eb366f20a62))
- **api:** fixed ecosystem file ([c2fe2cc](https://github.com/nclarity/backend/commit/c2fe2cc049e8c8921a2cff4e68af4a7b58aabf7c))

# [1.0.0-dev.5](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.4...@nclarity/contracts-v1.0.0-dev.5) (2023-06-27)

### Features

- **api:** [DEV-242] dispatcher page apis ([#283](https://github.com/nclarity/backend/issues/283)) ([c1a6928](https://github.com/nclarity/backend/commit/c1a69283393251cd70722186185ca81a8fdbb8d5))

# [1.0.0-dev.4](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.3...@nclarity/contracts-v1.0.0-dev.4) (2023-06-26)

### Bug Fixes

- added guard for customers deletion ([#278](https://github.com/nclarity/backend/issues/278)) ([ef4e39d](https://github.com/nclarity/backend/commit/ef4e39def74f9c51c1b704929e3fc7bc33e95e1f))

### Features

- **api:** [DEV-162] user list improvements ([#277](https://github.com/nclarity/backend/issues/277)) ([a102ba4](https://github.com/nclarity/backend/commit/a102ba479506903ab0e995fce2471cb49bcd44e5))
- **api:** [DEV-190] added new suggested dispatcher actions ([#280](https://github.com/nclarity/backend/issues/280)) ([5f8ad67](https://github.com/nclarity/backend/commit/5f8ad67729a705f76092c82ce3236d4c3d8bfb50))
- **api:** [DEV-211] equipment addition ([#279](https://github.com/nclarity/backend/issues/279)) ([bc01255](https://github.com/nclarity/backend/commit/bc01255f5e354b41005be5e0623ee5089c6f158e))
- **api:** added customer buildings in customer detail api ([200ddf4](https://github.com/nclarity/backend/commit/200ddf482c9daaa58f421b0f772963692125edc0))

# [1.0.0-dev.3](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.2...@nclarity/contracts-v1.0.0-dev.3) (2023-06-25)

### Features

- **api:** added alerts endpoints for dispatcher page and equipment ([#276](https://github.com/nclarity/backend/issues/276)) ([05a60e5](https://github.com/nclarity/backend/commit/05a60e59138fb740a2f82f44e431d48127894e19))

# [1.0.0-dev.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-dev.1...@nclarity/contracts-v1.0.0-dev.2) (2023-06-24)

### Features

- **all:** updated use cases and added new fields ([#275](https://github.com/nclarity/backend/issues/275)) ([db2f553](https://github.com/nclarity/backend/commit/db2f5538fa41bf4dc76ae2fcb417391f48f114ff))

# 1.0.0-dev.1 (2023-06-23)

### Bug Fixes

- [DEV-232] fixed the frequency undefined issue ([#261](https://github.com/nclarity/backend/issues/261)) ([71c1ac8](https://github.com/nclarity/backend/commit/71c1ac86b8368e5759928ff3d0ddf311e40af34e))
- added config fix for device config endpoint ([51f0288](https://github.com/nclarity/backend/commit/51f028840303f31290a7ce472dd633cb26d4fcea))
- **all:** fixed types for use cases ([#269](https://github.com/nclarity/backend/issues/269)) ([7090275](https://github.com/nclarity/backend/commit/7090275e3b1d1ba9af89121b82beff939a336ab1)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **api:** [DEV-183] fixed user creation ([#233](https://github.com/nclarity/backend/issues/233)) ([1426174](https://github.com/nclarity/backend/commit/142617480cd393602d339e27d69aa29c1e3e433f))
- **api:** [DEV-215] updated getallpulses service ([#248](https://github.com/nclarity/backend/issues/248)) ([a2ad24f](https://github.com/nclarity/backend/commit/a2ad24faa192771187d7edc0a4ac0296bad370d2))
- **api:** fixed account user creation for dev ([bcea6e2](https://github.com/nclarity/backend/commit/bcea6e2cd6b9556ca977875a06aa81a32ecf7b7a))
- **api:** fixed the token payload for the verify function ([#247](https://github.com/nclarity/backend/issues/247)) ([48f4504](https://github.com/nclarity/backend/commit/48f4504a07c997e9f665173c1552065515d95a4e))
- **api:** fixed the user creation with the group ([da25894](https://github.com/nclarity/backend/commit/da2589468a4cdccf8558a86e6fbae5fa4317e8c5))
- **api:** improved salt generation ([df8faf2](https://github.com/nclarity/backend/commit/df8faf2d839813823a9cf5f10429f7af80dd1e81))
- **charts:** added null chart point when there is no data ([abff391](https://github.com/nclarity/backend/commit/abff391c21ee37522d3c447aab7baba798abdfee))
- **contracts:** [DEV-180] updated package json in contracts ([#229](https://github.com/nclarity/backend/issues/229)) ([c20175e](https://github.com/nclarity/backend/commit/c20175ef5c468e31e233b7ca667e090fbf8c8f6b))
- **contracts:** removed unnecessary command in package.json ([5a37db1](https://github.com/nclarity/backend/commit/5a37db1cb65772a4c5692d0adcd67d44248f2c62))
- email template id for rtu scorecard sharing ([#65](https://github.com/nclarity/backend/issues/65)) ([5ab5666](https://github.com/nclarity/backend/commit/5ab56669dce5e00eebd2bd178fcc9ca57629b817))
- module resolution for contracts package ([#273](https://github.com/nclarity/backend/issues/273)) ([a564fd0](https://github.com/nclarity/backend/commit/a564fd04ad7a9945e706847523f726b42e5d4a17))
- template id for rtu scorecard ([#62](https://github.com/nclarity/backend/issues/62)) ([f2d5e9f](https://github.com/nclarity/backend/commit/f2d5e9f15158432baf24b82b41a39bd988fe9e46))

- feat(contracts)!: updated contracts based in prisma output ([68248ed](https://github.com/nclarity/backend/commit/68248ed0957f302b987c86997ad97984837ae00d))
- feat(contracts)!: changed from commonjs to esm ([c5b136c](https://github.com/nclarity/backend/commit/c5b136c7c8108b2a9625726781c10d276238effa))

### Features

- [DEV-177] add logger package ([#238](https://github.com/nclarity/backend/issues/238)) ([a008bc8](https://github.com/nclarity/backend/commit/a008bc8af3ec1962ca40e12dc5a1ff69de869526))
- [DEV-177] deployment to fix jwt token ([#236](https://github.com/nclarity/backend/issues/236)) ([13c1531](https://github.com/nclarity/backend/commit/13c153111f31ab0aaedbf56ff7e98be3dc6a0947))
- [DEV-177] no app deployment ([#237](https://github.com/nclarity/backend/issues/237)) ([14474f9](https://github.com/nclarity/backend/commit/14474f97a6ce9ebae44bdae4523d1e9ca5ae7d43))
- [DEV-177] updated contracts ([#240](https://github.com/nclarity/backend/issues/240)) ([dcdc8f5](https://github.com/nclarity/backend/commit/dcdc8f5b336656c53dd117365f3f21ddfe595e55))
- **accounts:** account deletion enhanced ([2586297](https://github.com/nclarity/backend/commit/258629752979a17f475ec30f5c9729514eceba81))
- **accounts:** deleted info from st-tech table ([0867e4f](https://github.com/nclarity/backend/commit/0867e4fd7222f1d7a96b7b058c79a3d03e0e3f78))
- added systemProfile model ([e165498](https://github.com/nclarity/backend/commit/e16549838c4046f749d55877f4ce66bd55d8b219))
- **all:** [DEV-7] Data Hierarchy ([#249](https://github.com/nclarity/backend/issues/249)) ([ea40ddc](https://github.com/nclarity/backend/commit/ea40ddc9440fff31d8b2a790c71cdb20a9544c71)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **all:** added new packages and publish config ([#241](https://github.com/nclarity/backend/issues/241)) ([cc58fae](https://github.com/nclarity/backend/commit/cc58faed694c66f739ac754e4d4b68d0a97330f6))
- **all:** production readiness ([bf6656f](https://github.com/nclarity/backend/commit/bf6656fd1508f30e802d9bb8cb87dcb32d0886dc))
- **api:** [DEV-171] remove default values for equipment profile ([a75bb11](https://github.com/nclarity/backend/commit/a75bb11783bdad820647db53555d60510c031b12))
- **api:** [DEV-172] updated defaults for equipment profile ([53b6e0d](https://github.com/nclarity/backend/commit/53b6e0d33a32adcd1f440c2a0d5732663a896b6c))
- **api:** [DEV-229] updated blackbox helpers ([#260](https://github.com/nclarity/backend/issues/260)) ([c8ed5c2](https://github.com/nclarity/backend/commit/c8ed5c250db0c1684b4ea6a8ae6a1d9cb9d7a1ce))
- **api:** added config fix for device config endpoint ([05c3460](https://github.com/nclarity/backend/commit/05c3460d92e10c6e01568bd9148861463cc1f75e))
- **api:** added create rule endpoint ([3e2a623](https://github.com/nclarity/backend/commit/3e2a6236bd48c4780b1c7760c345444743a316e4))
- **api:** added endpoint to get alerts for an account ([f3839e8](https://github.com/nclarity/backend/commit/f3839e852e525687284ea655de6dbae2111feae7))
- **api:** added error expects for ts ([6cee564](https://github.com/nclarity/backend/commit/6cee56493853e4847c8bce6dad4b315867aecd77))
- **api:** added get alerts for pulse endpoint ([a3b1843](https://github.com/nclarity/backend/commit/a3b1843e21dcfc58dac61e21f69058c46b7c233c))
- **api:** added get rules endpoint ([81e2f0e](https://github.com/nclarity/backend/commit/81e2f0e3d557efd4d3302433fdd5f9424924cc18))
- **api:** device init configuration ([57ecffd](https://github.com/nclarity/backend/commit/57ecffd7edad6ede0506fcc509fd1604670a13f2))
- **api:** esm enablement fixes ([61074b3](https://github.com/nclarity/backend/commit/61074b35d01d172742a6851876eb40fcb3734d81))
- **api:** esm support ([eb27cf1](https://github.com/nclarity/backend/commit/eb27cf1be85f36ffaddd1fd1656221c076ffbbae))
- **api:** fixed account creation ([6356fd4](https://github.com/nclarity/backend/commit/6356fd47b922e6f2a2e495506703214a394867f7))
- **api:** fixes based on tests ([1a08d10](https://github.com/nclarity/backend/commit/1a08d107064325b718b4b69a33d8d6b28bb4220a))
- **api:** migrated from axios to got ([5dbdfc9](https://github.com/nclarity/backend/commit/5dbdfc9a46bd1bfc514a882d5e695aec87b849b1))
- **api:** refactored to be a esm package ([8804e73](https://github.com/nclarity/backend/commit/8804e73dbfd5786f53e52baf40f0bf1559efca48))
- **api:** updated blackbox helpers ([e443ff7](https://github.com/nclarity/backend/commit/e443ff7cac5ca4574990cb6e6651c9983ccdf7d5))
- **api:** updated getallpulses service ([806dc6f](https://github.com/nclarity/backend/commit/806dc6f5452a8e73f9e3898faffbd3b96eebc688))
- **api:** updated logging configuration and isolated database packages ([48bfeb5](https://github.com/nclarity/backend/commit/48bfeb55d6872be162e529df0fb80f0fd1be0bda))
- **api:** updated mysql middleware ([1e86dfd](https://github.com/nclarity/backend/commit/1e86dfdf48021bf4da40f723b6e44b1fe1d37665))
- **api:** updated ruleset ([d067859](https://github.com/nclarity/backend/commit/d06785962bdfbd6831389555d6e45875449a28ee))
- **auth:** added params to access token needed to get compability with cloud-api ([7210e24](https://github.com/nclarity/backend/commit/********************12a27adacad06980476e))
- **authentication:** added better authentication for dashboard ([6b93898](https://github.com/nclarity/backend/commit/6b938981e8387392e4326dcc753fdf9e8dae6fd2))
- **auth:** improved auth tokens ([516c993](https://github.com/nclarity/backend/commit/516c99348b6269578398d407b33b235c7d42b9b0))
- **auth:** removed unused items on model ([cb64fcc](https://github.com/nclarity/backend/commit/cb64fcc0106e55455ce6dd6aab644aa495ca338d))
- **authtentication:** added authentication for operations ([8b8ff40](https://github.com/nclarity/backend/commit/8b8ff40d1586a16bb27a6b9ffd28dd0df6a64d8f))
- **calculations:** [DEV-22] updated calculations library to use new names in target values ([1577dcf](https://github.com/nclarity/backend/commit/1577dcfb0d0722c5855e61fbeae7dd4efde36773))
- **calculations:** added condition to calculate values only where there is a call ([1572241](https://github.com/nclarity/backend/commit/15722411d596adfe865c08aaa84774b54c4ca5fe))
- **calculations:** added more calculations ([13711b2](https://github.com/nclarity/backend/commit/13711b2a0b86846e08fa002eb03672f3a74e5d3d))
- **calculations:** added target values calculations ([bc5d677](https://github.com/nclarity/backend/commit/bc5d677044c3d5ae6f02b791d7785844e6d09c1d))
- **calculations:** added telemetry examiner helper ([911e7ec](https://github.com/nclarity/backend/commit/911e7ec1f4acfd4399143831cf5b462c01524e76))
- **calculations:** change from commonjs to esm ([aa76833](https://github.com/nclarity/backend/commit/aa76833da0d09750a5af4c27a7ba139d51398b57))
- **calculations:** fixed to use new types in contracts ([1bc72f4](https://github.com/nclarity/backend/commit/1bc72f4651a7d6e7872b44e78eb31fb71d14376a))
- **calculations:** used new types defined in contracts ([3e8d5b6](https://github.com/nclarity/backend/commit/3e8d5b6b2dfd67c85f8275601d2d9465abe630e8))
- **chart-data:** added service to get chart data based on telemetry and timestamps ([448e7e9](https://github.com/nclarity/backend/commit/448e7e936b0cc4ccdf8061602c1a3e2825cd2ec9))
- **charts:** added compability for colors with frontend ([afe3a18](https://github.com/nclarity/backend/commit/afe3a1849c7567750b7c8cf5a650625126089df4))
- **charts:** added electrical coversion ([b5ebfe4](https://github.com/nclarity/backend/commit/b5ebfe41bbaeff4558c99530719278a403ae3a7b))
- **charts:** added endpoint to get chart data based on timestamps ([2888f28](https://github.com/nclarity/backend/commit/2888f2870b0f20d43ebbbcf6c1fe11e3bea27191))
- **charts:** added factories for chart data ([25f7325](https://github.com/nclarity/backend/commit/25f73253c46bcf4ad227d042962bcbd3c9904e99))
- **charts:** added global setting for luxon to be on utc timezone ([d80ebc2](https://github.com/nclarity/backend/commit/d80ebc27fd61ec111f37fc3ef05acc675f33559a))
- **charts:** added kilo scale to electrical chart ([b176b21](https://github.com/nclarity/backend/commit/b176b2192eb8d86d91f310d64e8b24b063e0d139))
- **charts:** added plotting rules for erroneous data ([51965c4](https://github.com/nclarity/backend/commit/51965c42e10529e9553db56027410e3cc6605acc))
- **charts:** added timestamps to each data point sent on chart data ([5117a6b](https://github.com/nclarity/backend/commit/5117a6b1b5450714bbddf033dcb00f28836ea13f))
- **charts:** changed colors from lowercased colors to uppercased colors ([f45845f](https://github.com/nclarity/backend/commit/f45845f8628c1977620b85ad03ff97cafea6f4c5))
- **charts:** now telemetry data is projected in a more efficient way ([156ffb8](https://github.com/nclarity/backend/commit/156ffb847064abfc58a5783283a00afd781226ff))
- **charts:** removed comments ([aa12740](https://github.com/nclarity/backend/commit/aa12740f84a12c590764a43b1803db10c1cae6e4))
- **charts:** removed projections ([24b38c4](https://github.com/nclarity/backend/commit/24b38c47147f088815df0e092499bffd3f8debd4))
- **charts:** removed temperature conversion on rule applier ([71fcf53](https://github.com/nclarity/backend/commit/71fcf53380cb0bb821ae04533f3c6be93246669c))
- **config:** added config endpoint ([25d6f75](https://github.com/nclarity/backend/commit/25d6f753de480eab5772553c5549e78f69a89697))
- **contracts:** [DEV-180] added contracts package ([#227](https://github.com/nclarity/backend/issues/227)) ([2330f5f](https://github.com/nclarity/backend/commit/2330f5fba9ba926f16e12a6ef1ddec60c22efa33))
- **contracts:** added calculations types ([b8dc6da](https://github.com/nclarity/backend/commit/b8dc6daf005305da0fe83bb1c9b991e7adb14dfc))
- **contracts:** added cjs and esm exports ([de3fbe5](https://github.com/nclarity/backend/commit/de3fbe5e731e4c8678397427479a3679b56d21db))
- **contracts:** added cjs and esm exports ([#231](https://github.com/nclarity/backend/issues/231)) ([adfb7f0](https://github.com/nclarity/backend/commit/adfb7f0486a2b10add7f65be357c32fafd4ce4c0))
- **contracts:** added dev branch for release ([4e49f2c](https://github.com/nclarity/backend/commit/4e49f2c2877f709692c6e4421fffeb9f03bf95b7))
- **contracts:** added new exports for pulse config data ([006af64](https://github.com/nclarity/backend/commit/006af6464943d18483d5dcc904722f30d9fe16e3))
- **contracts:** added new exports for pulse config data ([#235](https://github.com/nclarity/backend/issues/235)) ([c35f6ce](https://github.com/nclarity/backend/commit/c35f6cefe6c41e4f30a1cdf7a706402a439a87bc))
- **contracts:** automatic releases enablement ([b4e495f](https://github.com/nclarity/backend/commit/b4e495f5b97bcff7b186fb5c012f4cdcac4b05bd))
- **contracts:** updated contracts package ([#274](https://github.com/nclarity/backend/issues/274)) ([bc1bd0a](https://github.com/nclarity/backend/commit/bc1bd0a4f3a98555d292928fcb3156322b4dad73))
- **contracts:** updated readme in contracts package ([e2168c9](https://github.com/nclarity/backend/commit/e2168c9ef44e90b9a09efba4e4224bfc47f0580c))
- **contracts:** updated release config file ([2beb609](https://github.com/nclarity/backend/commit/2beb609bc1ca8c268a28f60c36935488ec6030c4))
- **contracts:** updated release config file ([#230](https://github.com/nclarity/backend/issues/230)) ([62e460c](https://github.com/nclarity/backend/commit/62e460c3341985bba9662d4c4c61263e870ee91f))
- **contracts:** updated type for customer phone ([dadbb9f](https://github.com/nclarity/backend/commit/dadbb9fdc3f4a50674b13cd93e35f4d11d658319))
- **contracts:** updated workflow ([7ac8a71](https://github.com/nclarity/backend/commit/7ac8a71ce71f58e1de3242a6cbff63c6b83ae49a))
- **contracts:** updated workflow ([#228](https://github.com/nclarity/backend/issues/228)) ([a17f45a](https://github.com/nclarity/backend/commit/a17f45a5def648b8b24dd30958a9ce9d13f45b32))
- **customInisghts:** added cache adapter ([a84aa96](https://github.com/nclarity/backend/commit/a84aa96555ef23393d92ce34c26358422648fd7f))
- **customInsights:** [DEV-22] added better logging for ruleProcessor ([a2d048d](https://github.com/nclarity/backend/commit/a2d048d2c8192fcf8433b033261a53469db1f3bb))
- **customInsights:** [DEV-22] update rule processor ([8f39a80](https://github.com/nclarity/backend/commit/8f39a800a5717d13c4aaf131347a0f6b543c25f7))
- **customInsights:** [NP-22] updated ruleProcessor ([ef0bf88](https://github.com/nclarity/backend/commit/ef0bf88d9e0d99baad99331ad07d6167b91861e2))
- **customInsights:** added account id for enqueuing function ([2e9528f](https://github.com/nclarity/backend/commit/2e9528f9278c88c5ca733fcf19439ef5219aa983))
- **customInsights:** added alerts triggering for process rule ([c5c3173](https://github.com/nclarity/backend/commit/c5c3173bdf0347bc621a2bbb0a46308dd125a91a))
- **customInsights:** added building options ([cd6a630](https://github.com/nclarity/backend/commit/cd6a630e20b5ad1046dcbea41c8f339c39a30f0e))
- **customInsights:** added bulkGetRefrigerants adapter ([5e3d143](https://github.com/nclarity/backend/commit/5e3d143dc38df6f75f821fd3fb296a8d3ab236ae))
- **customInsights:** added calculated values getter ([91459b1](https://github.com/nclarity/backend/commit/91459b165a8dadb94f62d03e47eb27f7c0ade54f))
- **customInsights:** added correct usage for refrigerant ([ca0bbca](https://github.com/nclarity/backend/commit/ca0bbcad78b766272c4c5ca136cec0b54fb2fd5a))
- **customInsights:** added createRule endpoint ([fa6fb7f](https://github.com/nclarity/backend/commit/fa6fb7f14cd397f1677fe26d90e27c6f25ba763c))
- **customInsights:** added function to start processing all rules ([5d98a0e](https://github.com/nclarity/backend/commit/5d98a0e983dc49679542ae937c30261ca4047a1a))
- **customInsights:** added new apis for database adapter ([5779896](https://github.com/nclarity/backend/commit/57798968e4d1faeb736606020b3fa7174c91e604))
- **customInsights:** added sequelize adapter ([1d8388f](https://github.com/nclarity/backend/commit/1d8388f15f60ddedb9db1f83560fed85b47f971a))
- **customInsights:** added target values calculations ([cf731d7](https://github.com/nclarity/backend/commit/cf731d7506724f9a891cb35ad2e24f7a791be616))
- **customInsights:** added telemetry schema ([aad783e](https://github.com/nclarity/backend/commit/aad783e0b093554606ea91570c49c0b6eda6ab46))
- **customInsights:** added telemetryTransformations ([9b0e40a](https://github.com/nclarity/backend/commit/9b0e40a58de1a959b9611d733ad39062df3fa05c))
- **customInsights:** added transformer ([8fd4514](https://github.com/nclarity/backend/commit/8fd4514a2b49bb8fb9913d1d645ea853c576223e))
- **customInsights:** added transformer usage ([51de564](https://github.com/nclarity/backend/commit/51de564e29b5f96d5be342121fce28edb7241702))
- **customInsights:** added weather adapter ([80892fd](https://github.com/nclarity/backend/commit/80892fdcd56958b5da9d72b9e215edee04bf48cf))
- **customInsights:** changed business rules based on new rule set model ([c1ba87e](https://github.com/nclarity/backend/commit/c1ba87ed6956d0e0343c4acb132f1887f74275fb))
- **customInsights:** changed rule model ([c6d30e2](https://github.com/nclarity/backend/commit/c6d30e2ccae2566100cbc5b22b0abbe4e418c900))
- **customInsights:** improved adapters based on tsconfig changes ([55be4a8](https://github.com/nclarity/backend/commit/55be4a89b56b5efb8466b857a82dd0dea263fdcb))
- **customInsights:** improved connections to services ([bf6366a](https://github.com/nclarity/backend/commit/bf6366a265b7e417e718cfadbbf459d9c7d64c70))
- **customInsights:** improved model and added module type ([df87697](https://github.com/nclarity/backend/commit/df87697b857c8cde6c13edd2819582f136c1adda))
- **customInsights:** initial rule processor created ([7aa42aa](https://github.com/nclarity/backend/commit/7aa42aa73aa3c2b1fddd4e62d6459eda05dd0dc6))
- **customInsights:** installed telemetry package ([3f2ddc8](https://github.com/nclarity/backend/commit/3f2ddc8e61c3ab4569a02251423dc0954db45d14))
- **customInsights:** modified adapter to use account id ([98e8beb](https://github.com/nclarity/backend/commit/98e8bebb1a2ee53c390d8006ac30435654455b9f))
- **customInsights:** updated params for processRule function ([998cdde](https://github.com/nclarity/backend/commit/998cdde50bbf463bbd1ddd564df7be9bd2a5e9a2))
- **customInsights:** usage for processRule improved ([de372ea](https://github.com/nclarity/backend/commit/de372eac4f8b9d078ffec0fa0737c4ff44fbe43f))
- **database:** [DEV-167] updated references to foreign key in account model ([2953a47](https://github.com/nclarity/backend/commit/2953a479ab0975ddf34324a9f338dbf68fd86438))
- **database:** [NP-20] updated mongo model ([5781d11](https://github.com/nclarity/backend/commit/5781d11f8459795cebe088ed0a31e00b37f1032f))
- **database:** [NP-21] update schema to use groups and sets ([aa2813a](https://github.com/nclarity/backend/commit/aa2813a89bcfc916fbb11c96b913ca58ae2acff4))
- **database:** added accountId field for alerts ([1b18510](https://github.com/nclarity/backend/commit/1b185102ba1dabb0ddb1e3cad0805ce8b4f18626))
- **database:** added model for a rule ([920e8f8](https://github.com/nclarity/backend/commit/920e8f81546378aa2e97aa3f9ae68c227adc2ab0))
- **database:** added readme and updated package json ([3047917](https://github.com/nclarity/backend/commit/30479178544bd705abcf0c77f564285a80e8ba74))
- **database:** added refrigerants model ([29bbd1a](https://github.com/nclarity/backend/commit/29bbd1a1277f57423e7dd84cb32cc8bc8a630585))
- **database:** added semantic-release ([8a20478](https://github.com/nclarity/backend/commit/8a204782a324a508788b32b281d7b126049522d1))
- **database:** added telemetry schema validation ([14085d3](https://github.com/nclarity/backend/commit/14085d355f903c5885e7d2a35990e035f0416495))
- **database:** added usage of groups and account id ([68415d3](https://github.com/nclarity/backend/commit/68415d3ffccfad783e32285d2ac90f4d20db63a3))
- **database:** added validation for comparisonValueLabel ([c59563e](https://github.com/nclarity/backend/commit/c59563e4933ae5449eff4ef82ccddd985e3022fe))
- **database:** added validationSchemas folder ([94c01c7](https://github.com/nclarity/backend/commit/94c01c7d2e8c9b1d885caf4875fce0b4594d6e09))
- **database:** improved alerts and rules documents ([80d2939](https://github.com/nclarity/backend/commit/80d29391a520bcec2b642a488da019ac3ff8f02b))
- **database:** improved refrigerant models ([d1916e4](https://github.com/nclarity/backend/commit/d1916e4e8785184dfeb0c507e40e6d2fa365cd7d))
- **database:** improved rule model ([4f76212](https://github.com/nclarity/backend/commit/4f762127cc269d26d6f6307649b9451988f582ee))
- **database:** improved rule model with mixed type ([6f1a321](https://github.com/nclarity/backend/commit/6f1a3218815086260cea30bdaee4d8416af0e3d8))
- **database:** improved telemetry data object ([5528ca9](https://github.com/nclarity/backend/commit/5528ca9180dab197aba9463155a14613e99e2010))
- **database:** imrpoved rule definition and added alert model ([375cc93](https://github.com/nclarity/backend/commit/375cc934747d082bec0eb1e6fb20ca2820225111))
- **database:** migrated to esm type ([52b471f](https://github.com/nclarity/backend/commit/52b471f13ae18f9589a8080d3281bc55cdc3fbbe))
- **database:** model synchronization ([a5b5090](https://github.com/nclarity/backend/commit/a5b5090e402d36e069993f080088b4e46f9f86e0))
- **database:** split rules validations ([0b16136](https://github.com/nclarity/backend/commit/0b16136bf9bffd9c13c4bfe8db1dd5efd620b20d))
- **database:** typescript checks improvements ([34ea281](https://github.com/nclarity/backend/commit/34ea2813c2d31089ae03e2a09a39278b333135d5))
- **database:** updated models according to rulesets ([985a195](https://github.com/nclarity/backend/commit/985a1952c8dbe10ed2062763ab5ded3ac09c3aee))
- **database:** updated rule to match requirements ([5aa47e4](https://github.com/nclarity/backend/commit/5aa47e44b6f4aa22cf637fe1b2fef011b01a9425))
- **database:** updated target value labels ([f64b5dc](https://github.com/nclarity/backend/commit/f64b5dcd475077759a9b2f3ef289d158aa2fcd2d))
- **database:** updated target values to be separated ([387b920](https://github.com/nclarity/backend/commit/387b920ef031ac4cb09aed74e96d05307455b75d))
- **database:** updated validation schemas ([f19016c](https://github.com/nclarity/backend/commit/f19016c8b054a342657063079a1ec9723046b020))
- **DEV-28:** custom insights backend integration ([#234](https://github.com/nclarity/backend/issues/234)) ([47a1f16](https://github.com/nclarity/backend/commit/47a1f16980642ec6c671f2d5773bd6e1707b67e8))
- equipment addition and updates made ([07dc1ef](https://github.com/nclarity/backend/commit/07dc1ef80848dd5d9bd336e1535282d225b29300))
- **equipment:** added create equipment use case ([50ab1fe](https://github.com/nclarity/backend/commit/50ab1fe882ff0cc4a61eb22d0d933d7fdc34df1c))
- **equipment:** added deletion of equipment ([5144b45](https://github.com/nclarity/backend/commit/5144b4597d7ee04e8897b0d3925b74854b5f941d))
- **equipment:** added equipment router ([499db04](https://github.com/nclarity/backend/commit/499db04146a8075ed7f574f893ff6f2e842da416))
- **equipment:** created controller and use to get equipment by id ([e7c98f0](https://github.com/nclarity/backend/commit/e7c98f0b33e000cc831dab135e05d727bc69f9df))
- **equipment:** created use case to update equipment profile ([0a6a92c](https://github.com/nclarity/backend/commit/0a6a92cf913bffca9e1bc98c11525bd5f4ee131f))
- **equipmentProfile:** enabled virtual to refer to system profile ([ab2bc15](https://github.com/nclarity/backend/commit/ab2bc15338f01db00199d3f136f68ac28e2a0e42))
- **equipmentTags:** delete tags endpoint ([c99f2df](https://github.com/nclarity/backend/commit/c99f2df2601f3b7e5dbf86f050336e51b582b9a5))
- **factories:** added factories for users and pulses with typings ([bcd9068](https://github.com/nclarity/backend/commit/bcd90683f55156bffc847a58a27d499c633dfbfb))
- **functions:** added back eventhub name ([530b6aa](https://github.com/nclarity/backend/commit/530b6aa1dca7063964bf55ab44a5f4cc46290144))
- **functions:** added device config fuction ([fd6d2a5](https://github.com/nclarity/backend/commit/fd6d2a587f278fced4b08c5b5bff6e6bd650f306))
- **functions:** added endpoint to get pulses report ([79142be](https://github.com/nclarity/backend/commit/79142be0f1a73dafec3339de634892a7c0f67229))
- **functions:** added functions from backend function repo ([25c1334](https://github.com/nclarity/backend/commit/25c1334512dd806be33b99a00726f67e51e4c1b3))
- **functions:** added recoveredTelemetry exporter ([c6048a6](https://github.com/nclarity/backend/commit/c6048a6fbde32246c349d1a29759f04f0f70ed91))
- **functions:** change prod eventhub ([d5dbd67](https://github.com/nclarity/backend/commit/d5dbd67f756a454f5574636168e3433cb8f24723))
- **functions:** device init config ([7b3e186](https://github.com/nclarity/backend/commit/7b3e186f3ab83f2f5ae39b3d67a7cd00eeb133c5))
- **functions:** esm enablement for functions app ([df64e32](https://github.com/nclarity/backend/commit/df64e32151a95e2875e29e18467fab8524593c27))
- **functions:** esm support ([1622ec7](https://github.com/nclarity/backend/commit/1622ec735bd90f5fe2454fe134262ebdb87ddbca))
- **functions:** improved functions configuration ([d437cb4](https://github.com/nclarity/backend/commit/d437cb4119c8f5663cebdee4a20a65bf67ab40e5))
- **functions:** reduced logging for database exporter ([badd736](https://github.com/nclarity/backend/commit/badd736abb2597e1a384f75494750d965b329a40))
- **functions:** reduced logging for database exporter ([#246](https://github.com/nclarity/backend/issues/246)) ([a19e44f](https://github.com/nclarity/backend/commit/a19e44f4e5a6b5719661f6d9d5ddc9973e311fdd))
- **functions:** reduced the size for the batches in the event hub ([001facc](https://github.com/nclarity/backend/commit/001facca0d8844084acf63f9384320b179bb5416))
- **functions:** refactored to be a esm ([ea204af](https://github.com/nclarity/backend/commit/ea204af2ab43e48e25ee29f49b178bc55d211559))
- **functions:** removed undesired prop assignment ([42b1d90](https://github.com/nclarity/backend/commit/42b1d90946471ca7ec1094af285feaa181099f2f))
- **functions:** updated deployment function ([12866f0](https://github.com/nclarity/backend/commit/12866f03ac5493346d4be6b21f46e7fa7835d47b))
- **functions:** updated device template id in device init ([36a4e70](https://github.com/nclarity/backend/commit/36a4e70c81e5e95275a9825fa85187fa67a91ead))
- **functions:** updated event hub name ([7049bb7](https://github.com/nclarity/backend/commit/7049bb71f3bc037e1ca706aecda38cf49c14f677))
- **functions:** updated eventHubName ([93ac887](https://github.com/nclarity/backend/commit/93ac887b1bec89c9c8c6c76bba27b29eec3bb057))
- **functions:** updated frequency for pulses ([a4c3b00](https://github.com/nclarity/backend/commit/a4c3b0098930bb7bbd84e9fdb5dc443286c92f2a))
- **functions:** updated function def to use params and queries ([fcc8759](https://github.com/nclarity/backend/commit/fcc8759cc8c854489234787d4c35dcfb90800b6e))
- **functions:** updated host json and report endpoint ([a44a1dd](https://github.com/nclarity/backend/commit/a44a1ddd64b33fc308d4093ea4f65ec73e452747))
- **functions:** updated ruleInputGetter to use new types ([2078489](https://github.com/nclarity/backend/commit/207848998dca807ccc4eb2d73a388f652b2e04d7))
- **functions:** updated the event hub name for production ([fee8016](https://github.com/nclarity/backend/commit/fee80166565511fc2f0b5e0bc9cef03e89d37292))
- **getTelemetryData:** finished add chart data methods ([8eecd37](https://github.com/nclarity/backend/commit/8eecd370f5b415a14ebfafa022566eb60ea83581))
- **insights:** added account filtering and edge cases ([6941e8d](https://github.com/nclarity/backend/commit/6941e8db1397c0cb3a3964b6f8831a3774173bab))
- **insights:** added getInsightsByPulses service ([eadfacb](https://github.com/nclarity/backend/commit/eadfacb4c356994eea7bce9fb9497865b682436e))
- **insights:** improved pagination ([e82e783](https://github.com/nclarity/backend/commit/e82e7838fe0b59765650c80c0b6294a103bfc83c))
- **insights:** minimal endpoint to get insight history ([223fd59](https://github.com/nclarity/backend/commit/223fd593ceb4f1b6903d4e42b4149849c7f6c03f))
- **insights:** pulses by user selected ([857f30b](https://github.com/nclarity/backend/commit/857f30b164ffba3687767bf0fb3dd0e4883d921d))
- **logger:** made application insights transport optional locally ([913970c](https://github.com/nclarity/backend/commit/913970c39b49b4753d6ae2bc12a3037f89ed6d4b))
- **newUserCredentials:** changed emails ([62027c5](https://github.com/nclarity/backend/commit/62027c543de03a701cea869cd07eb27d740ab38d))
- no app deplpoy ([#259](https://github.com/nclarity/backend/issues/259)) ([81cc9bd](https://github.com/nclarity/backend/commit/81cc9bd364c25486ad01a6a39f406647e8ba6601))
- **opsUser:** reset password feature ([40bcd37](https://github.com/nclarity/backend/commit/40bcd37a18225708e0411c2aed84c92155267ab3))
- **opsUsers:** added edit ops user service ([0386249](https://github.com/nclarity/backend/commit/0386249c0807c51febcb03712d1de7cc8f6d4887))
- **opsUsers:** added router and service to get all ops users ([75ded15](https://github.com/nclarity/backend/commit/75ded1561da7d75ee13848b4fc4fe63b12d8ffb7))
- **opsUsers:** delete and get ops user by id service added ([3907161](https://github.com/nclarity/backend/commit/3907161a3aa6b61ff7edd0cff118320b77294e1f))
- **pulse:** added get pulse config endpoint ([d5edab1](https://github.com/nclarity/backend/commit/d5edab16835dc59c5fe2f8c6f326e8bb4f2c8f2e))
- **pulses:** changed type naming on pulses factory ([981c0cd](https://github.com/nclarity/backend/commit/981c0cd3990fa92ec76a1c3b6769dc0ee1978f5c))
- **pulses:** converted saturation temperature values to celcius ([3aae667](https://github.com/nclarity/backend/commit/3aae667c054a9494bbc64f4437b23a8a78973a69))
- **pulses:** only send eer data when there is a call for cool ([fbcc7ae](https://github.com/nclarity/backend/commit/fbcc7aec28de0622d4e7544dbe3a30a97024de56))
- **pulses:** pulses deletion corrected ([b0949e8](https://github.com/nclarity/backend/commit/b0949e810c96c89aa5f2ad9e371b467c6ad8a44c))
- **pulses:** type telemetry data and removed unnecessary code ([117f8c7](https://github.com/nclarity/backend/commit/117f8c7ffbd44935bc650374e118279098ec05c1))
- replaced bcrypt with bcryptjs ([a74c9b3](https://github.com/nclarity/backend/commit/a74c9b347354134b4776c2d777c46415e95c7f30))
- reviewed insights ([198e7ce](https://github.com/nclarity/backend/commit/198e7ce1e69dd9e69671ac4f41b46938f6818211))
- **services/createUser:** added new email for user credentials ([ade5e85](https://github.com/nclarity/backend/commit/ade5e85ec90cd86845fe3446dd0f9b3030b19b1f))
- share RTU Scorecard ([#61](https://github.com/nclarity/backend/issues/61)) ([5ffb4a9](https://github.com/nclarity/backend/commit/5ffb4a9a12a2deb0e47222b0326b7eada29625ca))
- **systemprofiled:** started entity ([3de1db3](https://github.com/nclarity/backend/commit/3de1db34a6e742b4f79ce6794e4fec39f2766e1e))
- **systemProfiles:** added router ([e08d943](https://github.com/nclarity/backend/commit/e08d9431c1957fcce7747bd2a45e19623b8fd5a9))
- **tags:** added services to get tags by pulses and account ([8f07919](https://github.com/nclarity/backend/commit/8f0791967113c790fd073a8e9d71108facd48d0e))
- **tags:** added tags services to create and list ([29c891d](https://github.com/nclarity/backend/commit/29c891d3227e5cb39899450b4769645ee33ad50e))
- **telemetry:** added calculations ([80022bf](https://github.com/nclarity/backend/commit/80022bf2de6ff433ed6d032e671b01efa3b8d38a))
- **telemetry:** added calculations ([8ca1564](https://github.com/nclarity/backend/commit/8ca15641e93a51e184a932b8206b58999a4decd0))
- **telemetry:** added customPsychometrics ([18686d6](https://github.com/nclarity/backend/commit/18686d68e7a8650789ffb807f3fd15511cfaca12))
- **telemetry:** added documentation for telemetry values ([919c66b](https://github.com/nclarity/backend/commit/919c66bfd5509de06359ce249e15eb1b1e3c06e0))
- **telemetry:** added more calculations ([9aed02a](https://github.com/nclarity/backend/commit/9aed02a37ed075effa158755e9035cb7a9343e72))
- **telemetry:** added sideEffects field in telemetry package ([fe2cab8](https://github.com/nclarity/backend/commit/fe2cab8d9ee6c4b133b24886f6ecbe28158ae9bc))
- **telemetryData:** modified getTelemetryData endpoint to manage lastCall data ([c255f2b](https://github.com/nclarity/backend/commit/c255f2b17ed62ac7d192bbc9ffbdfd30c5fefae1))
- **telemetry:** simplified calculations ([615ef4a](https://github.com/nclarity/backend/commit/615ef4abb6481fb2d0fad0559146bd430c9e3aa1))
- **telemetry:** started to add calculations ([0c5b794](https://github.com/nclarity/backend/commit/0c5b7949b63fabb0469dca2fada975bb615bc75e))
- **user:** added re-add users when deleted ([be515e2](https://github.com/nclarity/backend/commit/be515e2ce64ffca22f7d53e56ade4dc766f6fccd))
- **user:** modified display name ([22d448b](https://github.com/nclarity/backend/commit/22d448bb19c4392990d6a88508a337ea2017e368))
- **users:** added active clause when deleted and re-adding user ([74f7588](https://github.com/nclarity/backend/commit/74f75881617d8ffd8266602fec4118c494267940))
- **users:** added get users endpoint ([77e04f4](https://github.com/nclarity/backend/commit/77e04f4137821a5af058fd9540c001ff2ffe1698))
- **users:** added user addition endpoint ([0841a88](https://github.com/nclarity/backend/commit/0841a884d744c2a581252ededc0f57b7152d0f84))
- **users:** added user deletion endpoint dashboard[#394](https://github.com/nclarity/backend/issues/394) ([ccfc6bb](https://github.com/nclarity/backend/commit/ccfc6bbef9276250f8948c136b14f514f728aa4d))
- **users:** added user edition endpoint dashboard[#393](https://github.com/nclarity/backend/issues/393) ([55d908f](https://github.com/nclarity/backend/commit/55d908fe5cf7a1f8d96d6a12554bfdc6c3f22a9e))
- **users:** make service to not be able to delete self ([9f2a3b6](https://github.com/nclarity/backend/commit/9f2a3b600795339e0e408fb71a9e2ae23bb432f9))

### Reverts

- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([1d965b0](https://github.com/nclarity/backend/commit/1d965b0d9d377a30b567ad289c114be8076408a9))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([644ebd8](https://github.com/nclarity/backend/commit/644ebd8caebac67404ee4484c15e18e6dc599c87))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([037f7e2](https://github.com/nclarity/backend/commit/037f7e2d76d4a2d1ee5df4529ef4fc5fd8e865db))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([5ad3d2b](https://github.com/nclarity/backend/commit/5ad3d2bc2cb74f11c7a0ab8252b4045796d9119b))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([532af29](https://github.com/nclarity/backend/commit/532af29aba22a40aa36d7620b72585375ca47863))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.5@1.0.0-beta.5 [skip ci]" ([67f0049](https://github.com/nclarity/backend/commit/67f0049806941a8a948572ec7ff0fbea9cdceae1))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([fc35fd7](https://github.com/nclarity/backend/commit/fc35fd77d671f81c6c2f78e25154d170f63d3a93))
- Revert "bugfix(authentication): fixed auth middleware" ([d1ef2a3](https://github.com/nclarity/backend/commit/d1ef2a38c923223f3cca3f51b0d971bb14105de5))
- Revert "fix(pulses): update of pulse warranty data" ([b7a2965](https://github.com/nclarity/backend/commit/b7a29657ce443e5349027f5d76704992313b0be5))

### BREAKING CHANGES

- updated relational database models based in the prisma output
- this package is now esm

# [1.0.0-beta.3](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-beta.2...@nclarity/contracts-v1.0.0-beta.3) (2023-06-23)

### Bug Fixes

- module resolution for contracts package ([#273](https://github.com/nclarity/backend/issues/273)) ([a564fd0](https://github.com/nclarity/backend/commit/a564fd04ad7a9945e706847523f726b42e5d4a17))

# [1.0.0-beta.2](https://github.com/nclarity/backend/compare/@nclarity/contracts-v1.0.0-beta.1...@nclarity/contracts-v1.0.0-beta.2) (2023-06-23)

### Features

- **contracts:** updated readme in contracts package ([e2168c9](https://github.com/nclarity/backend/commit/e2168c9ef44e90b9a09efba4e4224bfc47f0580c))

# 1.0.0-beta.1 (2023-06-23)

### Bug Fixes

- [DEV-232] fixed the frequency undefined issue ([#261](https://github.com/nclarity/backend/issues/261)) ([71c1ac8](https://github.com/nclarity/backend/commit/71c1ac86b8368e5759928ff3d0ddf311e40af34e))
- added config fix for device config endpoint ([51f0288](https://github.com/nclarity/backend/commit/51f028840303f31290a7ce472dd633cb26d4fcea))
- **all:** fixed types for use cases ([#269](https://github.com/nclarity/backend/issues/269)) ([7090275](https://github.com/nclarity/backend/commit/7090275e3b1d1ba9af89121b82beff939a336ab1)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **api:** [DEV-183] fixed user creation ([#233](https://github.com/nclarity/backend/issues/233)) ([1426174](https://github.com/nclarity/backend/commit/142617480cd393602d339e27d69aa29c1e3e433f))
- **api:** [DEV-215] updated getallpulses service ([#248](https://github.com/nclarity/backend/issues/248)) ([a2ad24f](https://github.com/nclarity/backend/commit/a2ad24faa192771187d7edc0a4ac0296bad370d2))
- **api:** fixed account user creation for dev ([bcea6e2](https://github.com/nclarity/backend/commit/bcea6e2cd6b9556ca977875a06aa81a32ecf7b7a))
- **api:** fixed the token payload for the verify function ([#247](https://github.com/nclarity/backend/issues/247)) ([48f4504](https://github.com/nclarity/backend/commit/48f4504a07c997e9f665173c1552065515d95a4e))
- **api:** fixed the user creation with the group ([da25894](https://github.com/nclarity/backend/commit/da2589468a4cdccf8558a86e6fbae5fa4317e8c5))
- **api:** improved salt generation ([df8faf2](https://github.com/nclarity/backend/commit/df8faf2d839813823a9cf5f10429f7af80dd1e81))
- **charts:** added null chart point when there is no data ([abff391](https://github.com/nclarity/backend/commit/abff391c21ee37522d3c447aab7baba798abdfee))
- **contracts:** [DEV-180] updated package json in contracts ([#229](https://github.com/nclarity/backend/issues/229)) ([c20175e](https://github.com/nclarity/backend/commit/c20175ef5c468e31e233b7ca667e090fbf8c8f6b))
- **contracts:** removed unnecessary command in package.json ([5a37db1](https://github.com/nclarity/backend/commit/5a37db1cb65772a4c5692d0adcd67d44248f2c62))
- deployment and prisma generation for function ([#272](https://github.com/nclarity/backend/issues/272)) ([26b2d75](https://github.com/nclarity/backend/commit/26b2d750d41119a832084545b883b4856ec45f6f))
- email template id for rtu scorecard sharing ([#65](https://github.com/nclarity/backend/issues/65)) ([5ab5666](https://github.com/nclarity/backend/commit/5ab56669dce5e00eebd2bd178fcc9ca57629b817))
- template id for rtu scorecard ([#62](https://github.com/nclarity/backend/issues/62)) ([f2d5e9f](https://github.com/nclarity/backend/commit/f2d5e9f15158432baf24b82b41a39bd988fe9e46))

- feat(contracts)!: updated contracts based in prisma output ([68248ed](https://github.com/nclarity/backend/commit/68248ed0957f302b987c86997ad97984837ae00d))
- feat(contracts)!: changed from commonjs to esm ([c5b136c](https://github.com/nclarity/backend/commit/c5b136c7c8108b2a9625726781c10d276238effa))

### Features

- [DEV-177] add logger package ([#238](https://github.com/nclarity/backend/issues/238)) ([a008bc8](https://github.com/nclarity/backend/commit/a008bc8af3ec1962ca40e12dc5a1ff69de869526))
- [DEV-177] deployment to fix jwt token ([#236](https://github.com/nclarity/backend/issues/236)) ([13c1531](https://github.com/nclarity/backend/commit/13c153111f31ab0aaedbf56ff7e98be3dc6a0947))
- [DEV-177] no app deployment ([#237](https://github.com/nclarity/backend/issues/237)) ([14474f9](https://github.com/nclarity/backend/commit/14474f97a6ce9ebae44bdae4523d1e9ca5ae7d43))
- [DEV-177] updated contracts ([#240](https://github.com/nclarity/backend/issues/240)) ([dcdc8f5](https://github.com/nclarity/backend/commit/dcdc8f5b336656c53dd117365f3f21ddfe595e55))
- **accounts:** account deletion enhanced ([2586297](https://github.com/nclarity/backend/commit/258629752979a17f475ec30f5c9729514eceba81))
- **accounts:** deleted info from st-tech table ([0867e4f](https://github.com/nclarity/backend/commit/0867e4fd7222f1d7a96b7b058c79a3d03e0e3f78))
- added systemProfile model ([e165498](https://github.com/nclarity/backend/commit/e16549838c4046f749d55877f4ce66bd55d8b219))
- **all:** [DEV-7] Data Hierarchy ([#249](https://github.com/nclarity/backend/issues/249)) ([ea40ddc](https://github.com/nclarity/backend/commit/ea40ddc9440fff31d8b2a790c71cdb20a9544c71)), closes [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246) [#246](https://github.com/nclarity/backend/issues/246)
- **all:** added new packages and publish config ([#241](https://github.com/nclarity/backend/issues/241)) ([cc58fae](https://github.com/nclarity/backend/commit/cc58faed694c66f739ac754e4d4b68d0a97330f6))
- **all:** production readiness ([bf6656f](https://github.com/nclarity/backend/commit/bf6656fd1508f30e802d9bb8cb87dcb32d0886dc))
- **api:** [DEV-171] remove default values for equipment profile ([a75bb11](https://github.com/nclarity/backend/commit/a75bb11783bdad820647db53555d60510c031b12))
- **api:** [DEV-172] updated defaults for equipment profile ([53b6e0d](https://github.com/nclarity/backend/commit/53b6e0d33a32adcd1f440c2a0d5732663a896b6c))
- **api:** [DEV-229] updated blackbox helpers ([#260](https://github.com/nclarity/backend/issues/260)) ([c8ed5c2](https://github.com/nclarity/backend/commit/c8ed5c250db0c1684b4ea6a8ae6a1d9cb9d7a1ce))
- **api:** added config fix for device config endpoint ([05c3460](https://github.com/nclarity/backend/commit/05c3460d92e10c6e01568bd9148861463cc1f75e))
- **api:** added create rule endpoint ([3e2a623](https://github.com/nclarity/backend/commit/3e2a6236bd48c4780b1c7760c345444743a316e4))
- **api:** added endpoint to get alerts for an account ([f3839e8](https://github.com/nclarity/backend/commit/f3839e852e525687284ea655de6dbae2111feae7))
- **api:** added error expects for ts ([6cee564](https://github.com/nclarity/backend/commit/6cee56493853e4847c8bce6dad4b315867aecd77))
- **api:** added get alerts for pulse endpoint ([a3b1843](https://github.com/nclarity/backend/commit/a3b1843e21dcfc58dac61e21f69058c46b7c233c))
- **api:** added get rules endpoint ([81e2f0e](https://github.com/nclarity/backend/commit/81e2f0e3d557efd4d3302433fdd5f9424924cc18))
- **api:** device init configuration ([57ecffd](https://github.com/nclarity/backend/commit/57ecffd7edad6ede0506fcc509fd1604670a13f2))
- **api:** esm enablement fixes ([61074b3](https://github.com/nclarity/backend/commit/61074b35d01d172742a6851876eb40fcb3734d81))
- **api:** esm support ([eb27cf1](https://github.com/nclarity/backend/commit/eb27cf1be85f36ffaddd1fd1656221c076ffbbae))
- **api:** fixed account creation ([6356fd4](https://github.com/nclarity/backend/commit/6356fd47b922e6f2a2e495506703214a394867f7))
- **api:** fixes based on tests ([1a08d10](https://github.com/nclarity/backend/commit/1a08d107064325b718b4b69a33d8d6b28bb4220a))
- **api:** migrated from axios to got ([5dbdfc9](https://github.com/nclarity/backend/commit/5dbdfc9a46bd1bfc514a882d5e695aec87b849b1))
- **api:** refactored to be a esm package ([8804e73](https://github.com/nclarity/backend/commit/8804e73dbfd5786f53e52baf40f0bf1559efca48))
- **api:** updated blackbox helpers ([e443ff7](https://github.com/nclarity/backend/commit/e443ff7cac5ca4574990cb6e6651c9983ccdf7d5))
- **api:** updated getallpulses service ([806dc6f](https://github.com/nclarity/backend/commit/806dc6f5452a8e73f9e3898faffbd3b96eebc688))
- **api:** updated logging configuration and isolated database packages ([48bfeb5](https://github.com/nclarity/backend/commit/48bfeb55d6872be162e529df0fb80f0fd1be0bda))
- **api:** updated mysql middleware ([1e86dfd](https://github.com/nclarity/backend/commit/1e86dfdf48021bf4da40f723b6e44b1fe1d37665))
- **api:** updated ruleset ([d067859](https://github.com/nclarity/backend/commit/d06785962bdfbd6831389555d6e45875449a28ee))
- **auth:** added params to access token needed to get compability with cloud-api ([7210e24](https://github.com/nclarity/backend/commit/********************12a27adacad06980476e))
- **authentication:** added better authentication for dashboard ([6b93898](https://github.com/nclarity/backend/commit/6b938981e8387392e4326dcc753fdf9e8dae6fd2))
- **auth:** improved auth tokens ([516c993](https://github.com/nclarity/backend/commit/516c99348b6269578398d407b33b235c7d42b9b0))
- **auth:** removed unused items on model ([cb64fcc](https://github.com/nclarity/backend/commit/cb64fcc0106e55455ce6dd6aab644aa495ca338d))
- **authtentication:** added authentication for operations ([8b8ff40](https://github.com/nclarity/backend/commit/8b8ff40d1586a16bb27a6b9ffd28dd0df6a64d8f))
- **calculations:** [DEV-22] updated calculations library to use new names in target values ([1577dcf](https://github.com/nclarity/backend/commit/1577dcfb0d0722c5855e61fbeae7dd4efde36773))
- **calculations:** added condition to calculate values only where there is a call ([1572241](https://github.com/nclarity/backend/commit/15722411d596adfe865c08aaa84774b54c4ca5fe))
- **calculations:** added more calculations ([13711b2](https://github.com/nclarity/backend/commit/13711b2a0b86846e08fa002eb03672f3a74e5d3d))
- **calculations:** added target values calculations ([bc5d677](https://github.com/nclarity/backend/commit/bc5d677044c3d5ae6f02b791d7785844e6d09c1d))
- **calculations:** added telemetry examiner helper ([911e7ec](https://github.com/nclarity/backend/commit/911e7ec1f4acfd4399143831cf5b462c01524e76))
- **calculations:** change from commonjs to esm ([aa76833](https://github.com/nclarity/backend/commit/aa76833da0d09750a5af4c27a7ba139d51398b57))
- **calculations:** fixed to use new types in contracts ([1bc72f4](https://github.com/nclarity/backend/commit/1bc72f4651a7d6e7872b44e78eb31fb71d14376a))
- **calculations:** used new types defined in contracts ([3e8d5b6](https://github.com/nclarity/backend/commit/3e8d5b6b2dfd67c85f8275601d2d9465abe630e8))
- **chart-data:** added service to get chart data based on telemetry and timestamps ([448e7e9](https://github.com/nclarity/backend/commit/448e7e936b0cc4ccdf8061602c1a3e2825cd2ec9))
- **charts:** added compability for colors with frontend ([afe3a18](https://github.com/nclarity/backend/commit/afe3a1849c7567750b7c8cf5a650625126089df4))
- **charts:** added electrical coversion ([b5ebfe4](https://github.com/nclarity/backend/commit/b5ebfe41bbaeff4558c99530719278a403ae3a7b))
- **charts:** added endpoint to get chart data based on timestamps ([2888f28](https://github.com/nclarity/backend/commit/2888f2870b0f20d43ebbbcf6c1fe11e3bea27191))
- **charts:** added factories for chart data ([25f7325](https://github.com/nclarity/backend/commit/25f73253c46bcf4ad227d042962bcbd3c9904e99))
- **charts:** added global setting for luxon to be on utc timezone ([d80ebc2](https://github.com/nclarity/backend/commit/d80ebc27fd61ec111f37fc3ef05acc675f33559a))
- **charts:** added kilo scale to electrical chart ([b176b21](https://github.com/nclarity/backend/commit/b176b2192eb8d86d91f310d64e8b24b063e0d139))
- **charts:** added plotting rules for erroneous data ([51965c4](https://github.com/nclarity/backend/commit/51965c42e10529e9553db56027410e3cc6605acc))
- **charts:** added timestamps to each data point sent on chart data ([5117a6b](https://github.com/nclarity/backend/commit/5117a6b1b5450714bbddf033dcb00f28836ea13f))
- **charts:** changed colors from lowercased colors to uppercased colors ([f45845f](https://github.com/nclarity/backend/commit/f45845f8628c1977620b85ad03ff97cafea6f4c5))
- **charts:** now telemetry data is projected in a more efficient way ([156ffb8](https://github.com/nclarity/backend/commit/156ffb847064abfc58a5783283a00afd781226ff))
- **charts:** removed comments ([aa12740](https://github.com/nclarity/backend/commit/aa12740f84a12c590764a43b1803db10c1cae6e4))
- **charts:** removed projections ([24b38c4](https://github.com/nclarity/backend/commit/24b38c47147f088815df0e092499bffd3f8debd4))
- **charts:** removed temperature conversion on rule applier ([71fcf53](https://github.com/nclarity/backend/commit/71fcf53380cb0bb821ae04533f3c6be93246669c))
- **config:** added config endpoint ([25d6f75](https://github.com/nclarity/backend/commit/25d6f753de480eab5772553c5549e78f69a89697))
- **contracts:** [DEV-180] added contracts package ([#227](https://github.com/nclarity/backend/issues/227)) ([2330f5f](https://github.com/nclarity/backend/commit/2330f5fba9ba926f16e12a6ef1ddec60c22efa33))
- **contracts:** added calculations types ([b8dc6da](https://github.com/nclarity/backend/commit/b8dc6daf005305da0fe83bb1c9b991e7adb14dfc))
- **contracts:** added cjs and esm exports ([de3fbe5](https://github.com/nclarity/backend/commit/de3fbe5e731e4c8678397427479a3679b56d21db))
- **contracts:** added cjs and esm exports ([#231](https://github.com/nclarity/backend/issues/231)) ([adfb7f0](https://github.com/nclarity/backend/commit/adfb7f0486a2b10add7f65be357c32fafd4ce4c0))
- **contracts:** added dev branch for release ([4e49f2c](https://github.com/nclarity/backend/commit/4e49f2c2877f709692c6e4421fffeb9f03bf95b7))
- **contracts:** added new exports for pulse config data ([006af64](https://github.com/nclarity/backend/commit/006af6464943d18483d5dcc904722f30d9fe16e3))
- **contracts:** added new exports for pulse config data ([#235](https://github.com/nclarity/backend/issues/235)) ([c35f6ce](https://github.com/nclarity/backend/commit/c35f6cefe6c41e4f30a1cdf7a706402a439a87bc))
- **contracts:** automatic releases enablement ([b4e495f](https://github.com/nclarity/backend/commit/b4e495f5b97bcff7b186fb5c012f4cdcac4b05bd))
- **contracts:** updated release config file ([2beb609](https://github.com/nclarity/backend/commit/2beb609bc1ca8c268a28f60c36935488ec6030c4))
- **contracts:** updated release config file ([#230](https://github.com/nclarity/backend/issues/230)) ([62e460c](https://github.com/nclarity/backend/commit/62e460c3341985bba9662d4c4c61263e870ee91f))
- **contracts:** updated type for customer phone ([dadbb9f](https://github.com/nclarity/backend/commit/dadbb9fdc3f4a50674b13cd93e35f4d11d658319))
- **contracts:** updated workflow ([7ac8a71](https://github.com/nclarity/backend/commit/7ac8a71ce71f58e1de3242a6cbff63c6b83ae49a))
- **contracts:** updated workflow ([#228](https://github.com/nclarity/backend/issues/228)) ([a17f45a](https://github.com/nclarity/backend/commit/a17f45a5def648b8b24dd30958a9ce9d13f45b32))
- **customInisghts:** added cache adapter ([a84aa96](https://github.com/nclarity/backend/commit/a84aa96555ef23393d92ce34c26358422648fd7f))
- **customInsights:** [DEV-22] added better logging for ruleProcessor ([a2d048d](https://github.com/nclarity/backend/commit/a2d048d2c8192fcf8433b033261a53469db1f3bb))
- **customInsights:** [DEV-22] update rule processor ([8f39a80](https://github.com/nclarity/backend/commit/8f39a800a5717d13c4aaf131347a0f6b543c25f7))
- **customInsights:** [NP-22] updated ruleProcessor ([ef0bf88](https://github.com/nclarity/backend/commit/ef0bf88d9e0d99baad99331ad07d6167b91861e2))
- **customInsights:** added account id for enqueuing function ([2e9528f](https://github.com/nclarity/backend/commit/2e9528f9278c88c5ca733fcf19439ef5219aa983))
- **customInsights:** added alerts triggering for process rule ([c5c3173](https://github.com/nclarity/backend/commit/c5c3173bdf0347bc621a2bbb0a46308dd125a91a))
- **customInsights:** added building options ([cd6a630](https://github.com/nclarity/backend/commit/cd6a630e20b5ad1046dcbea41c8f339c39a30f0e))
- **customInsights:** added bulkGetRefrigerants adapter ([5e3d143](https://github.com/nclarity/backend/commit/5e3d143dc38df6f75f821fd3fb296a8d3ab236ae))
- **customInsights:** added calculated values getter ([91459b1](https://github.com/nclarity/backend/commit/91459b165a8dadb94f62d03e47eb27f7c0ade54f))
- **customInsights:** added correct usage for refrigerant ([ca0bbca](https://github.com/nclarity/backend/commit/ca0bbcad78b766272c4c5ca136cec0b54fb2fd5a))
- **customInsights:** added createRule endpoint ([fa6fb7f](https://github.com/nclarity/backend/commit/fa6fb7f14cd397f1677fe26d90e27c6f25ba763c))
- **customInsights:** added function to start processing all rules ([5d98a0e](https://github.com/nclarity/backend/commit/5d98a0e983dc49679542ae937c30261ca4047a1a))
- **customInsights:** added new apis for database adapter ([5779896](https://github.com/nclarity/backend/commit/57798968e4d1faeb736606020b3fa7174c91e604))
- **customInsights:** added sequelize adapter ([1d8388f](https://github.com/nclarity/backend/commit/1d8388f15f60ddedb9db1f83560fed85b47f971a))
- **customInsights:** added target values calculations ([cf731d7](https://github.com/nclarity/backend/commit/cf731d7506724f9a891cb35ad2e24f7a791be616))
- **customInsights:** added telemetry schema ([aad783e](https://github.com/nclarity/backend/commit/aad783e0b093554606ea91570c49c0b6eda6ab46))
- **customInsights:** added telemetryTransformations ([9b0e40a](https://github.com/nclarity/backend/commit/9b0e40a58de1a959b9611d733ad39062df3fa05c))
- **customInsights:** added transformer ([8fd4514](https://github.com/nclarity/backend/commit/8fd4514a2b49bb8fb9913d1d645ea853c576223e))
- **customInsights:** added transformer usage ([51de564](https://github.com/nclarity/backend/commit/51de564e29b5f96d5be342121fce28edb7241702))
- **customInsights:** added weather adapter ([80892fd](https://github.com/nclarity/backend/commit/80892fdcd56958b5da9d72b9e215edee04bf48cf))
- **customInsights:** changed business rules based on new rule set model ([c1ba87e](https://github.com/nclarity/backend/commit/c1ba87ed6956d0e0343c4acb132f1887f74275fb))
- **customInsights:** changed rule model ([c6d30e2](https://github.com/nclarity/backend/commit/c6d30e2ccae2566100cbc5b22b0abbe4e418c900))
- **customInsights:** improved adapters based on tsconfig changes ([55be4a8](https://github.com/nclarity/backend/commit/55be4a89b56b5efb8466b857a82dd0dea263fdcb))
- **customInsights:** improved connections to services ([bf6366a](https://github.com/nclarity/backend/commit/bf6366a265b7e417e718cfadbbf459d9c7d64c70))
- **customInsights:** improved model and added module type ([df87697](https://github.com/nclarity/backend/commit/df87697b857c8cde6c13edd2819582f136c1adda))
- **customInsights:** initial rule processor created ([7aa42aa](https://github.com/nclarity/backend/commit/7aa42aa73aa3c2b1fddd4e62d6459eda05dd0dc6))
- **customInsights:** installed telemetry package ([3f2ddc8](https://github.com/nclarity/backend/commit/3f2ddc8e61c3ab4569a02251423dc0954db45d14))
- **customInsights:** modified adapter to use account id ([98e8beb](https://github.com/nclarity/backend/commit/98e8bebb1a2ee53c390d8006ac30435654455b9f))
- **customInsights:** updated params for processRule function ([998cdde](https://github.com/nclarity/backend/commit/998cdde50bbf463bbd1ddd564df7be9bd2a5e9a2))
- **customInsights:** usage for processRule improved ([de372ea](https://github.com/nclarity/backend/commit/de372eac4f8b9d078ffec0fa0737c4ff44fbe43f))
- **database:** [DEV-167] updated references to foreign key in account model ([2953a47](https://github.com/nclarity/backend/commit/2953a479ab0975ddf34324a9f338dbf68fd86438))
- **database:** [NP-20] updated mongo model ([5781d11](https://github.com/nclarity/backend/commit/5781d11f8459795cebe088ed0a31e00b37f1032f))
- **database:** [NP-21] update schema to use groups and sets ([aa2813a](https://github.com/nclarity/backend/commit/aa2813a89bcfc916fbb11c96b913ca58ae2acff4))
- **database:** added accountId field for alerts ([1b18510](https://github.com/nclarity/backend/commit/1b185102ba1dabb0ddb1e3cad0805ce8b4f18626))
- **database:** added model for a rule ([920e8f8](https://github.com/nclarity/backend/commit/920e8f81546378aa2e97aa3f9ae68c227adc2ab0))
- **database:** added readme and updated package json ([3047917](https://github.com/nclarity/backend/commit/30479178544bd705abcf0c77f564285a80e8ba74))
- **database:** added refrigerants model ([29bbd1a](https://github.com/nclarity/backend/commit/29bbd1a1277f57423e7dd84cb32cc8bc8a630585))
- **database:** added semantic-release ([8a20478](https://github.com/nclarity/backend/commit/8a204782a324a508788b32b281d7b126049522d1))
- **database:** added telemetry schema validation ([14085d3](https://github.com/nclarity/backend/commit/14085d355f903c5885e7d2a35990e035f0416495))
- **database:** added usage of groups and account id ([68415d3](https://github.com/nclarity/backend/commit/68415d3ffccfad783e32285d2ac90f4d20db63a3))
- **database:** added validation for comparisonValueLabel ([c59563e](https://github.com/nclarity/backend/commit/c59563e4933ae5449eff4ef82ccddd985e3022fe))
- **database:** added validationSchemas folder ([94c01c7](https://github.com/nclarity/backend/commit/94c01c7d2e8c9b1d885caf4875fce0b4594d6e09))
- **database:** improved alerts and rules documents ([80d2939](https://github.com/nclarity/backend/commit/80d29391a520bcec2b642a488da019ac3ff8f02b))
- **database:** improved refrigerant models ([d1916e4](https://github.com/nclarity/backend/commit/d1916e4e8785184dfeb0c507e40e6d2fa365cd7d))
- **database:** improved rule model ([4f76212](https://github.com/nclarity/backend/commit/4f762127cc269d26d6f6307649b9451988f582ee))
- **database:** improved rule model with mixed type ([6f1a321](https://github.com/nclarity/backend/commit/6f1a3218815086260cea30bdaee4d8416af0e3d8))
- **database:** improved telemetry data object ([5528ca9](https://github.com/nclarity/backend/commit/5528ca9180dab197aba9463155a14613e99e2010))
- **database:** imrpoved rule definition and added alert model ([375cc93](https://github.com/nclarity/backend/commit/375cc934747d082bec0eb1e6fb20ca2820225111))
- **database:** migrated to esm type ([52b471f](https://github.com/nclarity/backend/commit/52b471f13ae18f9589a8080d3281bc55cdc3fbbe))
- **database:** model synchronization ([a5b5090](https://github.com/nclarity/backend/commit/a5b5090e402d36e069993f080088b4e46f9f86e0))
- **database:** split rules validations ([0b16136](https://github.com/nclarity/backend/commit/0b16136bf9bffd9c13c4bfe8db1dd5efd620b20d))
- **database:** typescript checks improvements ([34ea281](https://github.com/nclarity/backend/commit/34ea2813c2d31089ae03e2a09a39278b333135d5))
- **database:** updated models according to rulesets ([985a195](https://github.com/nclarity/backend/commit/985a1952c8dbe10ed2062763ab5ded3ac09c3aee))
- **database:** updated rule to match requirements ([5aa47e4](https://github.com/nclarity/backend/commit/5aa47e44b6f4aa22cf637fe1b2fef011b01a9425))
- **database:** updated target value labels ([f64b5dc](https://github.com/nclarity/backend/commit/f64b5dcd475077759a9b2f3ef289d158aa2fcd2d))
- **database:** updated target values to be separated ([387b920](https://github.com/nclarity/backend/commit/387b920ef031ac4cb09aed74e96d05307455b75d))
- **database:** updated validation schemas ([f19016c](https://github.com/nclarity/backend/commit/f19016c8b054a342657063079a1ec9723046b020))
- **DEV-28:** custom insights backend integration ([#234](https://github.com/nclarity/backend/issues/234)) ([47a1f16](https://github.com/nclarity/backend/commit/47a1f16980642ec6c671f2d5773bd6e1707b67e8))
- equipment addition and updates made ([07dc1ef](https://github.com/nclarity/backend/commit/07dc1ef80848dd5d9bd336e1535282d225b29300))
- **equipment:** added create equipment use case ([50ab1fe](https://github.com/nclarity/backend/commit/50ab1fe882ff0cc4a61eb22d0d933d7fdc34df1c))
- **equipment:** added deletion of equipment ([5144b45](https://github.com/nclarity/backend/commit/5144b4597d7ee04e8897b0d3925b74854b5f941d))
- **equipment:** added equipment router ([499db04](https://github.com/nclarity/backend/commit/499db04146a8075ed7f574f893ff6f2e842da416))
- **equipment:** created controller and use to get equipment by id ([e7c98f0](https://github.com/nclarity/backend/commit/e7c98f0b33e000cc831dab135e05d727bc69f9df))
- **equipment:** created use case to update equipment profile ([0a6a92c](https://github.com/nclarity/backend/commit/0a6a92cf913bffca9e1bc98c11525bd5f4ee131f))
- **equipmentProfile:** enabled virtual to refer to system profile ([ab2bc15](https://github.com/nclarity/backend/commit/ab2bc15338f01db00199d3f136f68ac28e2a0e42))
- **equipmentTags:** delete tags endpoint ([c99f2df](https://github.com/nclarity/backend/commit/c99f2df2601f3b7e5dbf86f050336e51b582b9a5))
- **factories:** added factories for users and pulses with typings ([bcd9068](https://github.com/nclarity/backend/commit/bcd90683f55156bffc847a58a27d499c633dfbfb))
- **functions:** added back eventhub name ([530b6aa](https://github.com/nclarity/backend/commit/530b6aa1dca7063964bf55ab44a5f4cc46290144))
- **functions:** added device config fuction ([fd6d2a5](https://github.com/nclarity/backend/commit/fd6d2a587f278fced4b08c5b5bff6e6bd650f306))
- **functions:** added endpoint to get pulses report ([79142be](https://github.com/nclarity/backend/commit/79142be0f1a73dafec3339de634892a7c0f67229))
- **functions:** added functions from backend function repo ([25c1334](https://github.com/nclarity/backend/commit/25c1334512dd806be33b99a00726f67e51e4c1b3))
- **functions:** added recoveredTelemetry exporter ([c6048a6](https://github.com/nclarity/backend/commit/c6048a6fbde32246c349d1a29759f04f0f70ed91))
- **functions:** change prod eventhub ([d5dbd67](https://github.com/nclarity/backend/commit/d5dbd67f756a454f5574636168e3433cb8f24723))
- **functions:** device init config ([7b3e186](https://github.com/nclarity/backend/commit/7b3e186f3ab83f2f5ae39b3d67a7cd00eeb133c5))
- **functions:** esm enablement for functions app ([df64e32](https://github.com/nclarity/backend/commit/df64e32151a95e2875e29e18467fab8524593c27))
- **functions:** esm support ([1622ec7](https://github.com/nclarity/backend/commit/1622ec735bd90f5fe2454fe134262ebdb87ddbca))
- **functions:** improved functions configuration ([d437cb4](https://github.com/nclarity/backend/commit/d437cb4119c8f5663cebdee4a20a65bf67ab40e5))
- **functions:** reduced logging for database exporter ([badd736](https://github.com/nclarity/backend/commit/badd736abb2597e1a384f75494750d965b329a40))
- **functions:** reduced logging for database exporter ([#246](https://github.com/nclarity/backend/issues/246)) ([a19e44f](https://github.com/nclarity/backend/commit/a19e44f4e5a6b5719661f6d9d5ddc9973e311fdd))
- **functions:** reduced the size for the batches in the event hub ([001facc](https://github.com/nclarity/backend/commit/001facca0d8844084acf63f9384320b179bb5416))
- **functions:** refactored to be a esm ([ea204af](https://github.com/nclarity/backend/commit/ea204af2ab43e48e25ee29f49b178bc55d211559))
- **functions:** removed undesired prop assignment ([42b1d90](https://github.com/nclarity/backend/commit/42b1d90946471ca7ec1094af285feaa181099f2f))
- **functions:** updated deployment function ([12866f0](https://github.com/nclarity/backend/commit/12866f03ac5493346d4be6b21f46e7fa7835d47b))
- **functions:** updated device template id in device init ([36a4e70](https://github.com/nclarity/backend/commit/36a4e70c81e5e95275a9825fa85187fa67a91ead))
- **functions:** updated event hub name ([7049bb7](https://github.com/nclarity/backend/commit/7049bb71f3bc037e1ca706aecda38cf49c14f677))
- **functions:** updated eventHubName ([93ac887](https://github.com/nclarity/backend/commit/93ac887b1bec89c9c8c6c76bba27b29eec3bb057))
- **functions:** updated frequency for pulses ([a4c3b00](https://github.com/nclarity/backend/commit/a4c3b0098930bb7bbd84e9fdb5dc443286c92f2a))
- **functions:** updated function def to use params and queries ([fcc8759](https://github.com/nclarity/backend/commit/fcc8759cc8c854489234787d4c35dcfb90800b6e))
- **functions:** updated host json and report endpoint ([a44a1dd](https://github.com/nclarity/backend/commit/a44a1ddd64b33fc308d4093ea4f65ec73e452747))
- **functions:** updated ruleInputGetter to use new types ([2078489](https://github.com/nclarity/backend/commit/207848998dca807ccc4eb2d73a388f652b2e04d7))
- **functions:** updated the event hub name for production ([fee8016](https://github.com/nclarity/backend/commit/fee80166565511fc2f0b5e0bc9cef03e89d37292))
- **getTelemetryData:** finished add chart data methods ([8eecd37](https://github.com/nclarity/backend/commit/8eecd370f5b415a14ebfafa022566eb60ea83581))
- **insights:** added account filtering and edge cases ([6941e8d](https://github.com/nclarity/backend/commit/6941e8db1397c0cb3a3964b6f8831a3774173bab))
- **insights:** added getInsightsByPulses service ([eadfacb](https://github.com/nclarity/backend/commit/eadfacb4c356994eea7bce9fb9497865b682436e))
- **insights:** improved pagination ([e82e783](https://github.com/nclarity/backend/commit/e82e7838fe0b59765650c80c0b6294a103bfc83c))
- **insights:** minimal endpoint to get insight history ([223fd59](https://github.com/nclarity/backend/commit/223fd593ceb4f1b6903d4e42b4149849c7f6c03f))
- **insights:** pulses by user selected ([857f30b](https://github.com/nclarity/backend/commit/857f30b164ffba3687767bf0fb3dd0e4883d921d))
- **logger:** made application insights transport optional locally ([913970c](https://github.com/nclarity/backend/commit/913970c39b49b4753d6ae2bc12a3037f89ed6d4b))
- **newUserCredentials:** changed emails ([62027c5](https://github.com/nclarity/backend/commit/62027c543de03a701cea869cd07eb27d740ab38d))
- no app deplpoy ([#259](https://github.com/nclarity/backend/issues/259)) ([81cc9bd](https://github.com/nclarity/backend/commit/81cc9bd364c25486ad01a6a39f406647e8ba6601))
- **opsUser:** reset password feature ([40bcd37](https://github.com/nclarity/backend/commit/40bcd37a18225708e0411c2aed84c92155267ab3))
- **opsUsers:** added edit ops user service ([0386249](https://github.com/nclarity/backend/commit/0386249c0807c51febcb03712d1de7cc8f6d4887))
- **opsUsers:** added router and service to get all ops users ([75ded15](https://github.com/nclarity/backend/commit/75ded1561da7d75ee13848b4fc4fe63b12d8ffb7))
- **opsUsers:** delete and get ops user by id service added ([3907161](https://github.com/nclarity/backend/commit/3907161a3aa6b61ff7edd0cff118320b77294e1f))
- **pulse:** added get pulse config endpoint ([d5edab1](https://github.com/nclarity/backend/commit/d5edab16835dc59c5fe2f8c6f326e8bb4f2c8f2e))
- **pulses:** changed type naming on pulses factory ([981c0cd](https://github.com/nclarity/backend/commit/981c0cd3990fa92ec76a1c3b6769dc0ee1978f5c))
- **pulses:** converted saturation temperature values to celcius ([3aae667](https://github.com/nclarity/backend/commit/3aae667c054a9494bbc64f4437b23a8a78973a69))
- **pulses:** only send eer data when there is a call for cool ([fbcc7ae](https://github.com/nclarity/backend/commit/fbcc7aec28de0622d4e7544dbe3a30a97024de56))
- **pulses:** pulses deletion corrected ([b0949e8](https://github.com/nclarity/backend/commit/b0949e810c96c89aa5f2ad9e371b467c6ad8a44c))
- **pulses:** type telemetry data and removed unnecessary code ([117f8c7](https://github.com/nclarity/backend/commit/117f8c7ffbd44935bc650374e118279098ec05c1))
- replaced bcrypt with bcryptjs ([a74c9b3](https://github.com/nclarity/backend/commit/a74c9b347354134b4776c2d777c46415e95c7f30))
- reviewed insights ([198e7ce](https://github.com/nclarity/backend/commit/198e7ce1e69dd9e69671ac4f41b46938f6818211))
- **services/createUser:** added new email for user credentials ([ade5e85](https://github.com/nclarity/backend/commit/ade5e85ec90cd86845fe3446dd0f9b3030b19b1f))
- share RTU Scorecard ([#61](https://github.com/nclarity/backend/issues/61)) ([5ffb4a9](https://github.com/nclarity/backend/commit/5ffb4a9a12a2deb0e47222b0326b7eada29625ca))
- **systemprofiled:** started entity ([3de1db3](https://github.com/nclarity/backend/commit/3de1db34a6e742b4f79ce6794e4fec39f2766e1e))
- **systemProfiles:** added router ([e08d943](https://github.com/nclarity/backend/commit/e08d9431c1957fcce7747bd2a45e19623b8fd5a9))
- **tags:** added services to get tags by pulses and account ([8f07919](https://github.com/nclarity/backend/commit/8f0791967113c790fd073a8e9d71108facd48d0e))
- **tags:** added tags services to create and list ([29c891d](https://github.com/nclarity/backend/commit/29c891d3227e5cb39899450b4769645ee33ad50e))
- **telemetry:** added calculations ([80022bf](https://github.com/nclarity/backend/commit/80022bf2de6ff433ed6d032e671b01efa3b8d38a))
- **telemetry:** added calculations ([8ca1564](https://github.com/nclarity/backend/commit/8ca15641e93a51e184a932b8206b58999a4decd0))
- **telemetry:** added customPsychometrics ([18686d6](https://github.com/nclarity/backend/commit/18686d68e7a8650789ffb807f3fd15511cfaca12))
- **telemetry:** added documentation for telemetry values ([919c66b](https://github.com/nclarity/backend/commit/919c66bfd5509de06359ce249e15eb1b1e3c06e0))
- **telemetry:** added more calculations ([9aed02a](https://github.com/nclarity/backend/commit/9aed02a37ed075effa158755e9035cb7a9343e72))
- **telemetry:** added sideEffects field in telemetry package ([fe2cab8](https://github.com/nclarity/backend/commit/fe2cab8d9ee6c4b133b24886f6ecbe28158ae9bc))
- **telemetryData:** modified getTelemetryData endpoint to manage lastCall data ([c255f2b](https://github.com/nclarity/backend/commit/c255f2b17ed62ac7d192bbc9ffbdfd30c5fefae1))
- **telemetry:** simplified calculations ([615ef4a](https://github.com/nclarity/backend/commit/615ef4abb6481fb2d0fad0559146bd430c9e3aa1))
- **telemetry:** started to add calculations ([0c5b794](https://github.com/nclarity/backend/commit/0c5b7949b63fabb0469dca2fada975bb615bc75e))
- **user:** added re-add users when deleted ([be515e2](https://github.com/nclarity/backend/commit/be515e2ce64ffca22f7d53e56ade4dc766f6fccd))
- **user:** modified display name ([22d448b](https://github.com/nclarity/backend/commit/22d448bb19c4392990d6a88508a337ea2017e368))
- **users:** added active clause when deleted and re-adding user ([74f7588](https://github.com/nclarity/backend/commit/74f75881617d8ffd8266602fec4118c494267940))
- **users:** added get users endpoint ([77e04f4](https://github.com/nclarity/backend/commit/77e04f4137821a5af058fd9540c001ff2ffe1698))
- **users:** added user addition endpoint ([0841a88](https://github.com/nclarity/backend/commit/0841a884d744c2a581252ededc0f57b7152d0f84))
- **users:** added user deletion endpoint dashboard[#394](https://github.com/nclarity/backend/issues/394) ([ccfc6bb](https://github.com/nclarity/backend/commit/ccfc6bbef9276250f8948c136b14f514f728aa4d))
- **users:** added user edition endpoint dashboard[#393](https://github.com/nclarity/backend/issues/393) ([55d908f](https://github.com/nclarity/backend/commit/55d908fe5cf7a1f8d96d6a12554bfdc6c3f22a9e))
- **users:** make service to not be able to delete self ([9f2a3b6](https://github.com/nclarity/backend/commit/9f2a3b600795339e0e408fb71a9e2ae23bb432f9))

### Reverts

- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([644ebd8](https://github.com/nclarity/backend/commit/644ebd8caebac67404ee4484c15e18e6dc599c87))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([037f7e2](https://github.com/nclarity/backend/commit/037f7e2d76d4a2d1ee5df4529ef4fc5fd8e865db))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([5ad3d2b](https://github.com/nclarity/backend/commit/5ad3d2bc2cb74f11c7a0ab8252b4045796d9119b))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.1@1.0.0-beta.1 [skip ci]" ([532af29](https://github.com/nclarity/backend/commit/532af29aba22a40aa36d7620b72585375ca47863))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.5@1.0.0-beta.5 [skip ci]" ([67f0049](https://github.com/nclarity/backend/commit/67f0049806941a8a948572ec7ff0fbea9cdceae1))
- Revert "chore(release): @nclarity/contracts-v1.0.0-beta.2@1.0.0-beta.2 [skip ci]" ([fc35fd7](https://github.com/nclarity/backend/commit/fc35fd77d671f81c6c2f78e25154d170f63d3a93))
- Revert "bugfix(authentication): fixed auth middleware" ([d1ef2a3](https://github.com/nclarity/backend/commit/d1ef2a38c923223f3cca3f51b0d971bb14105de5))
- Revert "fix(pulses): update of pulse warranty data" ([b7a2965](https://github.com/nclarity/backend/commit/b7a29657ce443e5349027f5d76704992313b0be5))

### BREAKING CHANGES

- updated relational database models based in the prisma output
- this package is now esm
