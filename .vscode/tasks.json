{"version": "2.0.0", "tasks": [{"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "pnpm build (functions)", "options": {"cwd": "${workspaceFolder}/apps/functions"}}, {"type": "shell", "label": "prerequisites", "command": "nvm use 18", "problemMatcher": "$tsc", "options": {"cwd": "${workspaceFolder}/apps/functions"}}, {"type": "shell", "label": "pnpm build (functions)", "command": "pnpm run build", "problemMatcher": "$tsc", "dependsOn": "prerequisites", "options": {"cwd": "${workspaceFolder}/apps/functions"}}, {"type": "shell", "label": "pnpm install (functions)", "command": "pnpm install", "options": {"cwd": "${workspaceFolder}/apps/functions"}}, {"type": "shell", "label": "pnpm prune (functions)", "command": "pnpm prune --production", "dependsOn": "pnpm build (functions)", "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/apps/functions"}}]}