{"eslint.workingDirectories": ["./packages/database", "./apps/api", "./apps/nexus", "./apps/signalwave", "./packages/scripts", "./packages/calculations", "./packages/contracts", "./packages/logger", "./packages/functions", "./packages/eslint-config", "./packages/emails", "./apps/labs", "./apps/coldstorage", "./packages/adapters"], "azureFunctions.projectSubpath": "apps/nexus", "azureFunctions.deploySubpath": "apps/nexus", "azureFunctions.projectLanguage": "TypeScript", "azureFunctions.projectRuntime": "~4", "typescript.tsdk": "node_modules/typescript/lib", "azureFunctions.postDeployTask": "pnpm install (functions)", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.preDeployTask": "pnpm prune (functions)", "json.schemas": [{"fileMatch": ["/package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["function.json"], "url": "https://json.schemastore.org/function.json"}], "prettier.prettierPath": "node_modules/prettier", "tailwindCSS.classAttributes": ["className", "tw", "variants", "clsx", "enterTo", "enterFrom", "enter", "leaveTo", "leaveFrom", "leave", "classes"], "tailwindCSS.experimental.configFile": {"./packages/emails/config/tailwind.ts": ["./packages/emails/**/*.tsx"]}, "cSpell.words": ["AAON", "Accesories", "accum", "ACHP", "ahri", "airflowactive", "airhandler", "AOXXXXX", "APPINSIGHTS", "ASHRAE", "autoincrement", "avsc", "azureiotcentral", "btuh", "buildingspictures", "camelcase", "ceadb", "cearh", "chunkify", "cladb", "clarh", "Classname", "coldstorage", "commitlint", "condensor", "conventionalcommits", "cuid", "cust", "customerslogos", "cwrp", "cwrt", "cwsp", "cwst", "databasecert", "deepmerge", "delat", "Derated", "dluser", "DTDCTOA", "dtmi", "emailimages", "endregion", "equipmentpictures", "errmsg", "Evap", "EVENTHUB", "fkey", "frequen", "GWXXXXX", "hfunc", "hfuncs", "hyrious", "ianvs", "IDDB", "imanifold", "inalert", "influxdata", "inservice", "INSTRUMENTATIONKEY", "iotc", "iotcentral", "kbtuh", "<PERSON><PERSON>", "kwatts", "llpc", "llpe", "lltc", "lltcf", "llte", "loglevel", "madb", "Mandt", "marh", "maxpagesize", "<PERSON><PERSON>", "mkct", "mongoexport", "mongoimport", "mongotools", "mtvannynwc", "nclarity", "nclarityuser", "Neosync", "netdog", "nocheck", "nodenext", "oadb", "oadbf", "oapercent", "oarh", "oawb", "odatemp", "openfeature", "oplog", "outdir", "paralleldrive", "parquetjs", "peakk", "posthog", "Psychro", "psychrolib", "Psychrometrics", "pulsealerts", "pulsealldata", "pulseconfigdata", "pulseconfigimages", "pulseresolutedemodata", "pulseresolvedalerts", "pulsewarrantyd<PERSON>", "PXXXXX", "radb", "radbf", "rapercent", "<PERSON><PERSON><PERSON>", "rarh", "rawb", "rawbf", "rediss", "Refg", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "r<PERSON>son", "rssi", "ruleprocessor", "rulesets", "rulesettoequipments", "rulesprocessor", "sadb", "sadbf", "sarh", "sawb", "sawbf", "<PERSON><PERSON><PERSON>", "Sendgrid", "signalwave", "<PERSON><PERSON>en", "sltc", "slte", "snakecase", "Subcooling", "suborchestrators", "Sytem", "telemetryeventcapture", "telemetryreports", "Tesp", "timewindow", "totalk", "Traceparent", "Treshold", "triggernotificationworkflowqueue", "TSDB", "Tstar", "TXXXXX", "typecheck", "unitless", "Unserialized", "updatedequipmentprofiles", "Upserted", "uuidv", "<PERSON><PERSON>a", "v<PERSON><PERSON>d", "virtuals", "webapps", "workdir", "Wsstar", "Wstar"]}