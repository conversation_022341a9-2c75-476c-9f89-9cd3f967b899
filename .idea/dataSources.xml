<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="nClarity Local" uuid="41c5c616-b779-48c7-9344-807456ac62bc">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************/</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="nClarity Azure Production" uuid="6ca54aa2-99c8-42d4-b6bb-dca00b211b08">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*****************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="nClarity Azure Development" uuid="55ab8495-cd09-432c-94fc-15fa39d7870a">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>****************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="nClarity Azure Staging" uuid="11a94ace-a9e3-49ac-86c2-7d2a4a64c0ec">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Mongo Production" uuid="e5d51f7e-1170-4129-9c03-602ba1dced9a">
      <driver-ref>mongo</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.dbschema.MongoJdbcDriver</jdbc-driver>
      <jdbc-url>mongodb+srv://cluster0.zkjfi.mongodb.net</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Mongo Staging" uuid="6aac058b-4be2-4655-9f6e-720b0699dfcc">
      <driver-ref>mongo</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.dbschema.MongoJdbcDriver</jdbc-driver>
      <jdbc-url>mongodb+srv://mongo-cluster-stg-02.zkjfi.mongodb.net</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>