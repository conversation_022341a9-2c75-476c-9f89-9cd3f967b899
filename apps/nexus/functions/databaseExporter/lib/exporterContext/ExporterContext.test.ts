import { getDatabaseAdapter } from '@nclarity/adapters/database/index.js';
import { EquipmentType } from '@nclarity/database';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Import after mocking
import { TelemetryProcessor } from './ExporterContext.js';

// Mock the dependencies
vi.mock('@nclarity/adapters/database/index.js', () => ({
  getDatabaseAdapter: vi.fn(() => ({
    relational: {
      getDeviceByDeviceId: vi.fn(),
    },
  })),
}));

vi.mock('@/lib/appInsightsTracker/appInsights.js', () => ({
  getAppInsightsTracker: vi.fn(() => ({
    trackEvent: vi.fn(),
    trackMetric: vi.fn(),
  })),
}));

vi.mock('@/lib/contextManager.js', () => ({
  default: {
    getContext: vi.fn(() => ({
      debug: vi.fn(),
      info: vi.fn(),
    })),
  },
}));

describe('TelemetryProcessor', () => {
  let mockGetDeviceByDeviceId: any;

  beforeEach(() => {
    vi.clearAllMocks();

    // Get the mocked functions
    mockGetDeviceByDeviceId = (
      getDatabaseAdapter({
        transactional: 'mock-connection-string',
        analytics: 'mock-connection-string',
      }) as any
    ).relational.getDeviceByDeviceId;
  });

  describe('getMappingStrategy', () => {
    it('should use correct mapping strategy based on equipment type', async () => {
      // Test with Chiller equipment type
      mockGetDeviceByDeviceId.mockResolvedValue({
        type: {
          iotCentralTemplateId: 'dtmi:nclarity:iPulse347;4',
        },
        deviceId: 'test-device-id',
        equipment: {
          type: EquipmentType.Chiller,
        },
      });

      const chillerResult = await TelemetryProcessor.getMappingStrategy(
        'test-device-id',
      );
      expect(chillerResult).toBeDefined();

      // Test with Packaged equipment type
      mockGetDeviceByDeviceId.mockResolvedValue({
        type: {
          iotCentralTemplateId: 'dtmi:nclarity:iPulse347;4',
        },
        deviceId: 'test-device-id',
        equipment: {
          type: EquipmentType.Packaged,
        },
      });

      const packagedResult = await TelemetryProcessor.getMappingStrategy(
        'test-device-id',
      );
      expect(packagedResult).toBeDefined();

      // Verify that different mapping strategies are used
      expect(chillerResult).not.toBe(packagedResult);
    });

    it('should use 800 series mapping strategy when no device is found', async () => {
      // Setup mock to return null (no device found)
      mockGetDeviceByDeviceId.mockResolvedValue(null);

      const result = await TelemetryProcessor.getMappingStrategy(
        'test-device-id',
      );
      expect(result).toBeDefined();
    });

    it('should use 800 series mapping strategy when device has no equipment', async () => {
      // Setup mock to return a device with no equipment
      mockGetDeviceByDeviceId.mockResolvedValue({
        type: {
          iotCentralTemplateId: 'unknown-template',
        },
        deviceId: 'test-device-id',
      });

      const result = await TelemetryProcessor.getMappingStrategy(
        'test-device-id',
      );
      expect(result).toBeDefined();
    });
  });
});
