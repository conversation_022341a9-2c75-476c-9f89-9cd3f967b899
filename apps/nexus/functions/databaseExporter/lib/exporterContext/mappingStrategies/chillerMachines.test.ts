import { getDatabaseAdapter } from '@nclarity/adapters';
import type { EquipmentResponse } from '@nclarity/adapters/database/apis/relational/getEquipmentByPulseId/index.js';
import { EquipmentType, type TelemetryData } from '@nclarity/database';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { env } from '@/env.mjs';
import { OutdoorAirSensorsReplacer } from '../../telemetryTransformationsChain/transformers/outdoorAirSensorsReplacer.js';
import { MappingChiller } from './chillerMachines.js';

vi.mock('@nclarity/adapters', () => ({
  getDatabaseAdapter: vi.fn(),
}));

vi.mock(
  '../../telemetryTransformationsChain/transformers/outdoorAirSensorsReplacer.js',
  () => ({
    OutdoorAirSensorsReplacer: {
      getOaReadings: vi.fn(),
    },
  }),
);

vi.mock('@/env.mjs', () => ({
  env: {
    MysqlConnectionString: 'mysql://test',
    AnalyticsDbConnectionString: 'mysql://analytics',
  },
}));

type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};

describe('MappingChiller', () => {
  let mappingChiller: MappingChiller;
  let mockTelemetry: Partial<TelemetryData>;
  let mockEquipment: DeepPartial<EquipmentResponse>;
  let mockDatabaseAdapter: any;

  beforeEach(() => {
    mappingChiller = new MappingChiller();

    mockTelemetry = {
      to: 0,
      ty: 0,
      watts: 100,
      ts: Date.now(),
      pf: 0.9,
      fw: '1.0',
      mac: '00:00:00:00:00:00',
      rreason: 1,
      seq: 1,
      'amps-1': 10,
      'amps-2': 11,
      'amps-3': 12,
      'volts-1': 120,
      'volts-2': 120,
      'volts-3': 120,
      tw: 75,
      'sltc-4': 76,
      'spc-4': 77,
      'lltc-4': 78,
      'llpc-4': 79,
      'sltc-3': 80,
      'spc-3': 81,
      'lltc-3': 82,
      'llpc-3': 83,
      'llpc-1': 84,
      'lltc-1': 85,
      'spc-1': 86,
      'sltc-1': 87,
      'llpc-2': 88,
      'lltc-2': 89,
      'spc-2': 90,
      'sltc-2': 91,
    };

    mockEquipment = {
      building: {
        latitude: 33.75,
        longitude: -84.39,
      },
      documentId: 'test-profile-id',
    };

    mockDatabaseAdapter = {
      relational: {
        getEquipmentProfileById: vi.fn(),
      },
    };

    (getDatabaseAdapter as any).mockReturnValue(mockDatabaseAdapter);

    (OutdoorAirSensorsReplacer.getOaReadings as any).mockResolvedValue({
      relativeHumidity: 50,
      dryBulb: 75,
    });
  });

  it('should correctly map telemetry for a chiller with oil loop', async () => {
    mockDatabaseAdapter.relational.getEquipmentProfileById.mockResolvedValue({
      data: {
        equipmentInfo: {
          kind: EquipmentType.Chiller,
        },
      },
      systemProfile: {
        oilLoop: true,
      },
    });

    const result = await mappingChiller.mapTelemetry(
      mockTelemetry as TelemetryData,
      mockEquipment as EquipmentResponse,
    );

    expect(result).toMatchObject({
      to: mockTelemetry.to,
      ty: mockTelemetry.ty,
      watts: mockTelemetry.watts,
      ts: mockTelemetry.ts,
      pf: mockTelemetry.pf,
      fw: mockTelemetry.fw,
      mac: mockTelemetry.mac,
      rreason: mockTelemetry.rreason,
      seq: mockTelemetry.seq,
      'amps-1': mockTelemetry['amps-1'],
      'amps-2': mockTelemetry['amps-2'],
      'amps-3': mockTelemetry['amps-3'],
      'volts-1': mockTelemetry['volts-1'],
      'volts-2': mockTelemetry['volts-2'],
      'volts-3': mockTelemetry['volts-3'],
      'oarh-1': 50,
      'oadb-1': 75,
      pc: mockTelemetry.tw,
      wrt: mockTelemetry['sltc-4'],
      wrp: mockTelemetry['spc-4'],
      wst: mockTelemetry['lltc-4'],
      wsp: mockTelemetry['llpc-4'],
      ort: mockTelemetry['sltc-3'],
      orp: mockTelemetry['spc-3'],
      ost: mockTelemetry['lltc-3'],
      osp: mockTelemetry['llpc-3'],
      'llpc-1': mockTelemetry['llpc-1'],
      'lltc-1': mockTelemetry['lltc-1'],
      'spc-1': mockTelemetry['spc-1'],
      'sltc-1': mockTelemetry['sltc-1'],
      'llpc-2': mockTelemetry['llpc-2'],
      'lltc-2': mockTelemetry['lltc-2'],
      'spc-2': mockTelemetry['spc-2'],
      'sltc-2': mockTelemetry['sltc-2'],
    });

    expect(getDatabaseAdapter).toHaveBeenCalledWith({
      transactional: env.MysqlConnectionString,
      analytics: env.AnalyticsDbConnectionString,
    });

    expect(
      mockDatabaseAdapter.relational.getEquipmentProfileById,
    ).toHaveBeenCalledWith({
      profileId: mockEquipment.documentId,
    });

    expect(OutdoorAirSensorsReplacer.getOaReadings).toHaveBeenCalledWith(
      mockEquipment.building?.latitude,
      mockEquipment.building?.longitude,
    );
  });

  it('should correctly map telemetry for a chiller without oil loop', async () => {
    mockDatabaseAdapter.relational.getEquipmentProfileById.mockResolvedValue({
      data: {
        equipmentInfo: {
          kind: EquipmentType.Chiller,
        },
      },
      systemProfile: {
        oilLoop: false,
      },
    });

    const result = await mappingChiller.mapTelemetry(
      mockTelemetry as TelemetryData,
      mockEquipment as EquipmentResponse,
    );

    expect(result).toMatchObject({
      'sltc-3': mockTelemetry['sltc-3'],
      'spc-3': mockTelemetry['spc-3'],
      'lltc-3': mockTelemetry['lltc-3'],
      'llpc-3': mockTelemetry['llpc-3'],
    });

    expect(result).not.toHaveProperty('ort');
    expect(result).not.toHaveProperty('orp');
    expect(result).not.toHaveProperty('ost');
    expect(result).not.toHaveProperty('osp');
  });

  it('should return original telemetry if equipment is not a chiller', async () => {
    mockDatabaseAdapter.relational.getEquipmentProfileById.mockResolvedValue({
      data: {
        equipmentInfo: {
          kind: EquipmentType.Packaged,
        },
      },
      systemProfile: {
        oilLoop: false,
      },
    });

    const result = await mappingChiller.mapTelemetry(
      mockTelemetry as TelemetryData,
      mockEquipment as EquipmentResponse,
    );

    expect(result).toBe(mockTelemetry);
  });
});
