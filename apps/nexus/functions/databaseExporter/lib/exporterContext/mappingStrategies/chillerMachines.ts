import { getDatabaseAdapter } from '@nclarity/adapters';
import type { EquipmentResponse } from '@nclarity/adapters/database/apis/relational/getEquipmentByPulseId/index.js';
import { EquipmentType, type TelemetryData } from '@nclarity/database';

import { env } from '@/env.mjs';
import { OutdoorAirSensorsReplacer } from '../../telemetryTransformationsChain/transformers/outdoorAirSensorsReplacer.js';
import type { TelemetryMappingStrategy } from '../ExporterContext.js';

export class MappingChiller implements TelemetryMappingStrategy {
  static deviceTemplate = 'dtmi:nclarity:iPulse347;4' as const;

  /**
   * Implements the mapping requested in: https://nclarity.atlassian.net/browse/DEV-1291
   * and https://nclarity.atlassian.net/wiki/spaces/SD/pages/337477633/Siemens+Project+System+Type+vs+Charts#Chilled-Fluid-Loop-Chart
   *
   * @param telemetry
   * @returns mapped telemetry
   */
  async mapTelemetry(
    telemetry: TelemetryData,
    equipment: EquipmentResponse,
  ): Promise<TelemetryData> {
    const weatherReadings = await OutdoorAirSensorsReplacer.getOaReadings(
      equipment?.building.latitude,
      equipment?.building.longitude,
    );
    const databaseAdapter = getDatabaseAdapter({
      transactional: env.MysqlConnectionString,
      analytics: env.AnalyticsDbConnectionString,
    });

    const profile = await databaseAdapter.relational.getEquipmentProfileById({
      profileId: equipment.documentId,
    });

    if (profile?.data.equipmentInfo?.kind === EquipmentType.Chiller) {
      const mapped: TelemetryData = {
        to: telemetry.to,
        ty: telemetry.ty,
        watts: telemetry.watts,
        ts: telemetry.ts,
        pf: telemetry.pf,
        fw: telemetry.fw,
        mac: telemetry.mac,
        rreason: telemetry.rreason,
        seq: telemetry.seq,
        'amps-1': telemetry['amps-1'],
        'amps-2': telemetry['amps-2'],
        'amps-3': telemetry['amps-3'],
        'volts-1': telemetry['volts-1'],
        'volts-2': telemetry['volts-2'],
        'volts-3': telemetry['volts-3'],
        'oarh-1': weatherReadings.relativeHumidity,
        'oadb-1': weatherReadings.dryBulb,
        pc: telemetry.tw,
        wrt: telemetry['sltc-4'],
        wrp: telemetry['spc-4'],
        wst: telemetry['lltc-4'],
        wsp: telemetry['llpc-4'],
      };

      if (profile.systemProfile.oilLoop) {
        mapped.ort = telemetry['sltc-3'];
        mapped.orp = telemetry['spc-3'];
        mapped.ost = telemetry['lltc-3'];
        mapped.osp = telemetry['llpc-3'];
      } else {
        mapped['sltc-3'] = telemetry['sltc-3'];
        mapped['spc-3'] = telemetry['spc-3'];
        mapped['lltc-3'] = telemetry['lltc-3'];
        mapped['llpc-3'] = telemetry['llpc-3'];
      }

      mapped['llpc-1'] = telemetry['llpc-1'];
      mapped['lltc-1'] = telemetry['lltc-1'];
      mapped['spc-1'] = telemetry['spc-1'];
      mapped['sltc-1'] = telemetry['sltc-1'];
      mapped['llpc-2'] = telemetry['llpc-2'];
      mapped['lltc-2'] = telemetry['lltc-2'];
      mapped['spc-2'] = telemetry['spc-2'];
      mapped['sltc-2'] = telemetry['sltc-2'];

      return mapped as TelemetryData;
    }

    return telemetry;
  }
}
