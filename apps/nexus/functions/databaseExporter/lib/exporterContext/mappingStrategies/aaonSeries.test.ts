import { DateTime } from 'luxon';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getAppInsightsTracker } from '@/lib/appInsightsTracker/appInsights.js';
import { MappingAAONSeries, type GwTelemetryData } from './aaonSeries.js';

vi.mock('@/lib/appInsightsTracker/appInsights.js', () => ({
  getAppInsightsTracker: vi.fn(),
}));

vi.mock('luxon', () => ({
  DateTime: {
    now: vi.fn(),
  },
}));

describe('MappingAAONSeries', () => {
  let mappingAAONSeries: MappingAAONSeries;
  let mockTelemetry: GwTelemetryData;
  let mockAppInsights: any;
  const gwInternalId = '15000';
  const deviceFriendlyId = 'AO00000';
  const mockTimestamp = 1625097600000; // Example timestamp

  beforeEach(() => {
    mappingAAONSeries = new MappingAAONSeries(gwInternalId, deviceFriendlyId);

    mockTelemetry = {
      [`${gwInternalId}_AI9_SADB-1`]: '11.722',
      [`${gwInternalId}_AI14_RADB-1`]: '20.056',
      [`${gwInternalId}_AI15_RARH-1`]: '2.000',
      [`${gwInternalId}_AI16_OADB-1`]: '25.389',
      [`${gwInternalId}_AI17_OARH-1`]: '0.000',
      [`${gwInternalId}_AI48_SPC-1`]: '154.600',
      [`${gwInternalId}_AI50_LLPC-1`]: '380.200',
      [`${gwInternalId}_AI54_SLTC-1`]: '-17.778',
      [`${gwInternalId}_AI66_LLTC-1`]: '67.556',
      [`${gwInternalId}_AI73_SPC-2`]: '113.600',
      [`${gwInternalId}_AI75_LLPC-2`]: '155.300',
      [`${gwInternalId}_AI79_SLTC-2`]: '-17.778',
      [`${gwInternalId}_AI91_LLTC-2`]: '33.611',
      [`${gwInternalId}_AI98_SPC-3`]: '0.000',
      [`${gwInternalId}_AI100_LLPC-3`]: '0.000',
      [`${gwInternalId}_AI104_SLTC-3`]: '-17.778',
      [`${gwInternalId}_AI116_LLTC-3`]: '-17.778',
      [`${gwInternalId}_AI123_SPC-4`]: '0.000',
      [`${gwInternalId}_AI125_LLPC-4`]: '0.000',
      [`${gwInternalId}_AI129_SLTC-4`]: '-17.778',
      [`${gwInternalId}_AI141_LLTC-4`]: '-17.778',
      [`${gwInternalId}_AI4_MODE`]: '2.000',
    };

    mockAppInsights = {
      trackEvent: vi.fn(),
      trackMetric: vi.fn(),
    };

    (getAppInsightsTracker as any).mockReturnValue(mockAppInsights);
    (DateTime.now as any).mockReturnValue({
      toMillis: () => mockTimestamp,
    });
  });

  it('should correctly map telemetry with valid measures', async () => {
    const result = await mappingAAONSeries.mapTelemetry(mockTelemetry);

    expect(result).toMatchObject({
      ts: mockTimestamp,
      ['oarh-1']: 0,
      ['oadb-1']: 25.389,
      ['sadb-1']: 11.722,
      ['radb-1']: 20.056,
      ['rarh-1']: 2,
      mode: 2,
      ty: 1,
      tw: 0,
      to: 0,
    });

    expect(getAppInsightsTracker).not.toHaveBeenCalled();
  });

  it('should map mode field correctly for mode = 2', async () => {
    mockTelemetry[`${gwInternalId}_AI4_MODE`] = '2';

    const result = await mappingAAONSeries.mapTelemetry(mockTelemetry);

    expect(result).toMatchObject({
      mode: 2,
      ty: 1,
      tw: 0,
      to: 0,
    });
  });

  it('should map mode field correctly for mode = 3', async () => {
    mockTelemetry[`${gwInternalId}_AI4_MODE`] = '3';

    const result = await mappingAAONSeries.mapTelemetry(mockTelemetry);

    expect(result).toMatchObject({
      mode: 3,
      ty: 0,
      tw: 1,
      to: 0,
    });
  });

  it('should map mode field correctly for mode = 4', async () => {
    mockTelemetry[`${gwInternalId}_AI4_MODE`] = '4';

    const result = await mappingAAONSeries.mapTelemetry(mockTelemetry);

    expect(result).toMatchObject({
      mode: 4,
      ty: 0,
      tw: 0,
      to: 0,
    });
  });

  it('should track error event for invalid key format', async () => {
    const invalidTelemetry = {
      [`${gwInternalId}_invalid`]: '100',
    };

    await mappingAAONSeries.mapTelemetry(invalidTelemetry);

    expect(mockAppInsights.trackEvent).toHaveBeenCalledWith({
      name: 'AAONSeriesTelemetryMappingError',
      properties: {
        message: expect.stringContaining('Invalid key format'),
        key: `${gwInternalId}_invalid`,
        gwInternalId,
        deviceFriendlyId,
      },
      contextObjects: {
        telemetry: invalidTelemetry,
        gwInternalId,
        deviceFriendlyId,
      },
    });

    expect(mockAppInsights.trackMetric).toHaveBeenCalledWith({
      name: 'AAONSeriesTelemetryMappingError',
      value: 1,
      count: 1,
    });
  });

  it('should ignore keys that do not include the gwInternalId', async () => {
    const mixedTelemetry = {
      [`${gwInternalId}_AI2_OARH-1`]: '50',
      other_device_circuit_oadb_1: '75',
    };

    const result = await mappingAAONSeries.mapTelemetry(mixedTelemetry);

    expect(result).toMatchObject({
      ts: mockTimestamp,
      ['oarh-1']: 50,
    });

    expect(result).not.toHaveProperty('oadb-1');
  });

  it('should ignore measures not in MEASURE_KEYS', async () => {
    const invalidMeasureTelemetry = {
      [`${gwInternalId}_AI2_INVALID`]: '100',
    };

    const result = await mappingAAONSeries.mapTelemetry(
      invalidMeasureTelemetry,
    );

    expect(result).toMatchObject({
      ts: mockTimestamp,
    });

    expect(result).not.toHaveProperty('invalid-measure');
  });

  it('should add circuit number to single circuit measures', async () => {
    // Create telemetry with single circuit measures without circuit numbers
    const singleCircuitTelemetry = {
      [`${gwInternalId}_AI2_OARH-1`]: '50',
    };

    const result = await mappingAAONSeries.mapTelemetry(singleCircuitTelemetry);

    expect(result).toMatchObject({
      ts: mockTimestamp,
      ['oarh-1']: 50,
    });
  });
});
