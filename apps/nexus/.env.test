# Test environment variables

FUNCTIONS_WORKER_RUNTIME=node
FUNCTIONS_EXTENSION_VERSION=~4
FUNCTIONS_NODE_DEFAULT_VERSION=18
FUNCTIONS_NODE_DEBUGGER_PORT=9229
APPINSIGHTS_INSTRUMENTATIONKEY=cbe555f7-ea5a-4a69-af27-683fbdce1439
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=cbe555f7-ea5a-4a69-af27-683fbdce1439;IngestionEndpoint=https://eastus2-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus2.livediagnostics.monitor.azure.com/

# Required environment variables for tests
EventHubConnection=test-connection-string
EventHubWriteConnection=test-connection-string
OpenWeatherApiKey=test-api-key
AzureIotCentralApplicationApiToken=test-token
MongoDatabaseConnectionString=test-connection-string
BackendStorageConnectionString=test-connection-string
EmailServiceUrlClient=test-url
EmailServiceApiKey=test-api-key
RestfulApiUrl=test-url
DashboardUrl=test-url
TSDB_URL=test-url
TSDB_TOKEN=test-token
TSDB_ORG=test-org
TSDB_BUCKET=test-bucket
TRENDY_INSIGHTS_EVENTHUB_NAME=test-eventhub
POSTHOG_API_KEY=test-api-key
POSTHOG_PROJECT_ID=test-project-id
MysqlConnectionString=mysql://test
AnalyticsDbConnectionString=mysql://analytics
