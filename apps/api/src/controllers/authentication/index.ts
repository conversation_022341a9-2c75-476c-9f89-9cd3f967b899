import { Router } from 'express';

import type { ComponentRoutes } from '../../types.js';
import { dashboardGetMeController } from './dashboardGetMe.js';
import { dashboardLoginController } from './dashboardLogin.js';
import { dashboardRefreshTokenController } from './dashboardToken.js';
import { operationsGetMeController } from './operationsGetMe.js';
import { operationsLoginController } from './operationsLogin.js';
import { operationsRefreshTokenController } from './operationsToken.js';

export class AuthenticationRouter implements ComponentRoutes {
  readonly name: string = 'authentication';
  readonly router: Router = Router();
  private readonly dashboardRouter: Router = Router();
  private readonly operationsRouter: Router = Router();

  constructor() {
    this.initRoutes();
  }

  initRoutes(): void {
    this.dashboardRouter.route('/login').post(dashboardLoginController);
    this.dashboardRouter.route('/get-me').get(dashboardGetMeController);
    this.dashboardRouter.route('/token').post(dashboardRefreshTokenController);

    this.operationsRouter.route('/login').post(operationsLoginController);
    this.operationsRouter.route('/get-me').get(operationsGetMeController);
    this.operationsRouter
      .route('/token')
      .post(operationsRefreshTokenController);

    this.router.use('/dashboard', this.dashboardRouter);
    this.router.use('/operations', this.operationsRouter);
  }
}
