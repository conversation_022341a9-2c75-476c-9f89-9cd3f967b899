import type { NextFunction, Request, Response } from 'express';
import type { JwtPayload } from 'jsonwebtoken';

import { operationsGetMeUseCase } from '../../use-cases/authentication/operations/get-me/useCase.js';

export interface UserTokenPayload extends JwtPayload {
  user: {
    email: string;
    id: string;
  };
}

export async function operationsGetMeController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const user = await operationsGetMeUseCase(
      req.headers.authorization as string,
    );

    res.send({
      status: 'OK',
      data: {
        ...user,
      },
    });
  } catch (error) {
    next(error);
  }
}
