import type { NextFunction, Request, Response } from 'express';

import { operationsRefreshTokenUseCase } from '../../use-cases/authentication/operations/token/useCase.js';

type TokenParams = {
  refreshToken: string;
};

export async function operationsRefreshTokenController(
  req: Request<any, any, TokenParams>,
  res: Response,
  next: NextFunction,
) {
  try {
    const data = await operationsRefreshTokenUseCase(req.body);

    res.send({
      status: 'OK',
      data,
    });
  } catch (error) {
    next(error);
  }
}
