import type { NextFunction, Request, Response } from 'express';

import { dashboardLoginUseCase } from '../../use-cases/authentication/dashboard/login/useCase.js';

type LoginBodyParams = {
  username: string;
  password: string;
};

export async function dashboardLoginController(
  req: Request<any, any, LoginBodyParams>,
  res: Response,
  next: NextFunction,
) {
  try {
    const data = await dashboardLoginUseCase(req.body);

    res.send({
      status: 'OK',
      data,
    });
  } catch (error) {
    next(error);
  }
}
