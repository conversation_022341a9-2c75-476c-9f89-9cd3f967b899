import type { NextFunction, Request, Response } from 'express';

import { dashboardGetMeUseCase } from '../../use-cases/authentication/dashboard/get-me/useCase.js';

export async function dashboardGetMeController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const user = await dashboardGetMeUseCase(
      req.headers.authorization as string,
    );

    res.send({
      status: 'OK',
      data: {
        ...user,
      },
    });
  } catch (error) {
    next(error);
  }
}
