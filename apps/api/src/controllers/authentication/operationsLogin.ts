import type { NextFunction, Request, Response } from 'express';

import { operationsLoginUseCase } from '../../use-cases/authentication/operations/login/useCase.js';

type LoginBodyParams = {
  username: string;
  password: string;
};

export async function operationsLoginController(
  req: Request<any, any, LoginBodyParams>,
  res: Response,
  next: NextFunction,
) {
  try {
    const data = await operationsLoginUseCase(req.body);

    res.send({
      status: 'OK',
      data,
    });
  } catch (error) {
    next(error);
  }
}
