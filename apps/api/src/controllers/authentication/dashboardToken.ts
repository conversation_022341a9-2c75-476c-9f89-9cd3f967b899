import type { NextFunction, Request, Response } from 'express';

import { dashboardRefreshTokenUseCase } from '../../use-cases/authentication/dashboard/token/useCase.js';

type TokenParams = {
  refreshToken: string;
};

export async function dashboardRefreshTokenController(
  req: Request<any, any, TokenParams>,
  res: Response,
  next: NextFunction,
) {
  try {
    const data = await dashboardRefreshTokenUseCase(req.body);

    res.send({
      status: 'OK',
      data,
    });
  } catch (error) {
    next(error);
  }
}
