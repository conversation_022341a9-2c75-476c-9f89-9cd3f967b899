import express from 'express';
import request from 'supertest';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AuthenticationRouter } from './index.js';

// Mock the use cases
vi.mock('../../use-cases/authentication/dashboard/login/useCase.js', () => ({
  dashboardLoginUseCase: vi.fn().mockResolvedValue({
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
  }),
}));

vi.mock('../../use-cases/authentication/operations/login/useCase.js', () => ({
  operationsLoginUseCase: vi.fn().mockResolvedValue({
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
  }),
}));

describe('Authentication Controller', () => {
  let app: express.Application;

  beforeEach(() => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Register the authentication router
    const authRouter = new AuthenticationRouter();

    app.use('/v3/authentication', authRouter.router);
  });

  describe('Dashboard Login', () => {
    it('should return access and refresh tokens on successful login', async () => {
      const response = await request(app)
        .post('/v3/authentication/dashboard/login')
        .send({
          username: '<EMAIL>',
          password: 'password123',
        });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        status: 'OK',
        data: {
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token',
        },
      });
    });
  });

  describe('Operations Login', () => {
    it('should return access and refresh tokens on successful login', async () => {
      const response = await request(app)
        .post('/v3/authentication/operations/login')
        .send({
          username: '<EMAIL>',
          password: 'password123',
        });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        status: 'OK',
        data: {
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token',
        },
      });
    });
  });
});
