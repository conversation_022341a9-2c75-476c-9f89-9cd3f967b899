import { EquipmentType } from '@nclarity/database';
import express from 'express';
import request from 'supertest';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';

import { createTestApp, initTestDatabase } from '../../../tests/utils/app.js';
import {
  TEST_USERS,
  createUserToken,
  getAuthHeader,
} from '../../../tests/utils/auth.js';
import { TestDatabaseManager } from '../../../tests/utils/database.js';
import { authenticatedMiddleware } from '../../middlewares/authentication.js';
import { createDeviceUseCase } from '../../use-cases/device/create-device/useCase.js';
import { EquipmentRouter } from './index.js';

describe('Equipment Router', () => {
  let app: express.Application;
  let adminToken: string;
  let testData: {
    corporate: any;
    customer: any;
    building: any;
    equipment: any;
    device: any;
  };

  beforeAll(async () => {
    // Initialize the test database
    initTestDatabase();

    // Set up test data
    testData = await TestDatabaseManager.setupTestData();
  });

  beforeEach(() => {
    // Create a fresh Express app for each test
    app = createTestApp((app) => {
      const equipmentRouter = new EquipmentRouter();
      app.use(authenticatedMiddleware);
      app.use('/v3/equipment', equipmentRouter.router);
    });

    // Create tokens for different user roles
    adminToken = createUserToken(TEST_USERS.admin);
  });

  afterAll(async () => {
    // Clean up after tests
    await TestDatabaseManager.cleanup();
  });

  describe('GET /v3/equipment', () => {
    it('should return a list of all equipment when authenticated with proper permissions', async () => {
      const response = await request(app)
        .get('/v3/equipment')
        .set(getAuthHeader(adminToken));

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        status: 'OK',
        data: expect.arrayContaining([
          expect.objectContaining({
            id: testData.equipment.id,
            pulseId: 'TEST-DEVICE-ID',
          }),
        ]),
      });
    });
  });

  describe('POST /v3/equipment', () => {
    it('should create a new packaged equipment with a device', async () => {
      // Create a test device first
      const { relational: db } = TestDatabaseManager.getInstances();
      const deviceType = await db.deviceType.findFirstOrThrow({
        where: {
          iotCentralTemplateId: 'dtmi:nclarity:iPulse347;4',
        },
      });

      const device = await createDeviceUseCase({
        deviceId: 'P12345',
        corporateId: testData.corporate.id,
        typeId: deviceType.id,
        isGateway: false,
      });

      const response = await request(app)
        .post('/v3/equipment')
        .set(getAuthHeader(adminToken))
        .send({
          buildingId: testData.building.id,
          deviceId: device.id,
          kind: EquipmentType.Packaged,
        });

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        status: 'OK',
        data: expect.objectContaining({
          pulseId: 'P12345',
          type: EquipmentType.Packaged,
          buildingId: testData.building.id,
          deviceId: device.id,
        }),
      });

      // Verify the equipment was created in the database
      const createdEquipment = await db.equipment.findUnique({
        where: { pulseId: 'P12345' },
      });
      expect(createdEquipment).not.toBeNull();
      expect(createdEquipment?.type).toBe(EquipmentType.Packaged);
    });

    it('should create a new chiller equipment with a device', async () => {
      // Create a test device first
      const { relational: db } = TestDatabaseManager.getInstances();
      const deviceType = await db.deviceType.findFirstOrThrow({
        where: {
          iotCentralTemplateId: 'dtmi:nclarity:iPulse347;4',
        },
      });

      const device = await createDeviceUseCase({
        deviceId: 'P67890',
        corporateId: testData.corporate.id,
        typeId: deviceType.id,
        isGateway: false,
      });

      const response = await request(app)
        .post('/v3/equipment')
        .set(getAuthHeader(adminToken))
        .send({
          buildingId: testData.building.id,
          deviceId: device.id,
          kind: EquipmentType.Chiller,
        });

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        status: 'OK',
        data: expect.objectContaining({
          pulseId: 'P67890',
          type: EquipmentType.Chiller,
          buildingId: testData.building.id,
          deviceId: device.id,
        }),
      });

      // Verify the equipment was created in the database
      const createdEquipment = await db.equipment.findUnique({
        where: { pulseId: 'P67890' },
      });
      expect(createdEquipment).not.toBeNull();
      expect(createdEquipment?.type).toBe(EquipmentType.Chiller);
    });

    it('should return 409 when trying to create equipment with an already assigned device', async () => {
      // Try to create equipment with the same device ID that was already used
      const response = await request(app)
        .post('/v3/equipment')
        .set(getAuthHeader(adminToken))
        .send({
          buildingId: testData.building.id,
          deviceId: testData.device.id, // This device is already assigned to an equipment
          kind: EquipmentType.Packaged,
        });

      expect(response.status).toBe(409);
      expect(response.body).toEqual(
        expect.objectContaining({
          message:
            'EquipmentEntity - An equipment with the pulseId provided is already registered.',
        }),
      );
    });

    it('should create a new packaged equipment without a device', async () => {
      const { relational: db } = TestDatabaseManager.getInstances();

      const response = await request(app)
        .post('/v3/equipment')
        .set(getAuthHeader(adminToken))
        .send({
          buildingId: testData.building.id,
          kind: EquipmentType.Packaged,
        });

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        status: 'OK',
        data: expect.objectContaining({
          type: EquipmentType.Packaged,
          buildingId: testData.building.id,
        }),
      });

      // Verify the equipment was created in the database
      const createdEquipment = await db.equipment.findUnique({
        where: { id: response.body.data.id },
      });
      expect(createdEquipment).not.toBeNull();
      expect(createdEquipment?.type).toBe(EquipmentType.Packaged);
      expect(createdEquipment?.deviceId).toBeNull();
    });

    it('should create a new chiller equipment without a device', async () => {
      const { relational: db } = TestDatabaseManager.getInstances();

      const response = await request(app)
        .post('/v3/equipment')
        .set(getAuthHeader(adminToken))
        .send({
          buildingId: testData.building.id,
          kind: EquipmentType.Chiller,
        });

      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        status: 'OK',
        data: expect.objectContaining({
          type: EquipmentType.Chiller,
          buildingId: testData.building.id,
        }),
      });

      // Verify the equipment was created in the database
      const createdEquipment = await db.equipment.findUnique({
        where: { id: response.body.data.id },
      });
      expect(createdEquipment).not.toBeNull();
      expect(createdEquipment?.type).toBe(EquipmentType.Chiller);
      expect(createdEquipment?.deviceId).toBeNull();
    });
  });
});
