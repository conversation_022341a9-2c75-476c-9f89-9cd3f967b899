import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { unassignTagUseCase } from '../../use-cases/equipment/unassign-tag/useCase.js';

export async function unassignTagController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const parsedQuery = z
      .object({
        tagId: z.string(),
        equipmentId: z.string(),
      })
      .safeParse(req.query);

    if (!parsedQuery.success) {
      throw new HttpError('Invalid tag id or equipment id', 'Tags', 400);
    }

    const result = await unassignTagUseCase(
      parsedQuery.data.tagId,
      parsedQuery.data.equipmentId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
