import express from 'express';
import request from 'supertest';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';

import { createTestApp, initTestDatabase } from '../../../tests/utils/app.js';
import {
  TEST_USERS,
  createUserToken,
  getAuthHeader,
} from '../../../tests/utils/auth.js';
import { TestDatabaseManager } from '../../../tests/utils/database.js';
import { authenticatedMiddleware } from '../../middlewares/authentication.js';
import { DeviceSimulationRouter } from './index.js';

describe('Calculate Data Controller', () => {
  let app: express.Application;
  let adminToken: string;
  let testData: {
    corporate: any;
    customer: any;
    building: any;
    equipment: any;
  };

  beforeAll(async () => {
    // Initialize the test database
    initTestDatabase();

    // Set up test data
    testData = await TestDatabaseManager.setupTestData();
  });

  beforeEach(() => {
    // Create a fresh Express app for each test
    app = createTestApp((app) => {
      const deviceSimulationRouter = new DeviceSimulationRouter();
      app.use(authenticatedMiddleware);
      app.use(deviceSimulationRouter.router);
    });

    // Create tokens for different user roles
    adminToken = createUserToken(TEST_USERS.admin);
  });

  afterAll(async () => {
    // Clean up after tests
    await TestDatabaseManager.cleanup();
  });

  it('should calculate telemetry data and return valid message when no telemetry data is provided', async () => {
    const ts = Date.now();
    const response = await request(app)
      .post('/calculate')
      .set(getAuthHeader(adminToken))
      .send({
        equipmentId: testData.equipment.id,
        telemetry: [
          {
            ts,
          },
        ],
      });

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      status: 'OK',
      data: expect.arrayContaining([
        expect.objectContaining({
          ts: ts,
          callForCool: false,
          callForHeat: false,
          targetAmperage: { low: 0, high: 0 },
          targetSubcool: {
            low: -3,
            high: 3,
          },
          targetSuperheat: {},
        }),
      ]),
    });
  });

  it('should calculate telemetry data when telemetry data is provided', async () => {
    const ts = Date.now();
    const response = await request(app)
      .post('/calculate')
      .set(getAuthHeader(adminToken))
      .send({
        equipmentId: testData.equipment.id,
        telemetry: [
          {
            ts,
            mac: 'simulated',
            to: 0,
            tw: 0,
            ty: 1,
            watts: 9600,
            rreason: undefined,
            'amps-1': 21,
            'amps-2': 21,
            'amps-3': 21,
            fw: 'simulated',
            'llpc-1': 321,
            'lltc-1': 31,
            'oadb-1': 27,
            'oarh-1': 75,
            pf: 0.82,
            'radb-1': 24,
            'rarh-1': 65,
            'sadb-1': 13,
            'sarh-1': 90,
            'sltc-1': 11,
            'spc-1': 101,
            'volts-1': 121,
            'volts-2': 121,
            'volts-3': 121,
            'llpc-2': 322,
            'spc-2': 102,
            'sltc-2': 13,
            'lltc-2': 32,
            'spc-3': 103,
            'llpc-3': 323,
            'sltc-3': 14,
            'lltc-3': 33,
            'spc-4': 104,
            'llpc-4': 324,
            'sltc-4': 15,
            'lltc-4': 34,
          },
        ],
      });

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      status: 'OK',
      data: expect.arrayContaining([
        expect.objectContaining({
          ts: ts,
          callForHeat: false,
          mac: 'simulated',
          to: 0,
          tw: 0,
          ty: 1,
          watts: 9600,
          'amps-1': 21,
          'amps-2': 21,
          'amps-3': 21,
          fw: 'simulated',
          'llpc-1': 321,
          'lltc-1': 31,
          'oadb-1': 27,
          'oarh-1': 75,
          pf: 0.82,
          'radb-1': 24,
          'rarh-1': 65,
          'sadb-1': 13,
          'sarh-1': 90,
          'sltc-1': 11,
          'spc-1': 101,
          'volts-1': 121,
          'volts-2': 121,
          'volts-3': 121,
          'llpc-2': 322,
          'spc-2': 102,
          'sltc-2': 13,
          'lltc-2': 32,
          'spc-3': 103,
          'llpc-3': 323,
          'sltc-3': 14,
          'lltc-3': 33,
          'spc-4': 104,
          'llpc-4': 324,
          'sltc-4': 15,
          'lltc-4': 34,
          'LLCApproach-1': 4,
          'LLCSatTemp-1': 37.84,
          'LLCSubCooling-1': -10.94,
          'SLCSatTemp-1': -0.55,
          'SLCSuperHeat-1': -6.23,
          callForCool: true,
          delatH: 7.22,
          deltaHf: 12.99,
          deltaT: 11,
          deltaTf: 19.8,
          oaDewPnt: 22.19,
          oaDty: 1.12,
          oaEnt: 38.79,
          oaVlm: 0.92,
          oawb: 23.48,
          raDewPnt: 17.02,
          raDty: 1.13,
          raEnt: 32.01,
          raVlm: 0.9,
          rawb: 19.27,
          saDewPnt: 11.4,
          saDty: 1.18,
          saEnt: 22.85,
          saVlm: 0.86,
          sawb: 12.05,
          targetLiquidPressure: {
            high: 360.1257370250033,
            low: 331.2257044853113,
          },
          targetSubcool: {
            high: 3,
            low: -3,
          },

          targetSuctionPressure: {
            high: 126.48916868005227,
            low: 112.78718754604127,
          },
          targetSuperheat: {
            high: 1.1111111111111112,
            low: -2.2222222222222223,
          },
          targetAmperage: {
            high: 0,
            low: 0,
          },
        }),
      ]),
    });
  });
});
