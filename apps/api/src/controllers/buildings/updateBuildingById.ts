import { updateBuildingValidationSchema } from '@nclarity/database';
import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { updateBuildingByIdUseCase } from '../../use-cases/buildings/update/useCase.js';

export async function updateBuildingByIdController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('User not allowed', 'BuildingsEntity', 403);
    }

    const buildingId = z.string().uuid().safeParse(req.params.buildingId);

    if (!buildingId.success) {
      throw new HttpError('Invalid building id', 'BuildingsEntity', 400);
    }

    const buildingPayload = updateBuildingValidationSchema.parse(req.body);

    const building = await updateBuildingByIdUseCase(
      buildingId.data,
      buildingPayload,
      req.user,
    );

    res.status(201).json({
      status: 'OK',
      data: building,
    });
  } catch (error) {
    next(error);
  }
}
