import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { unrollBuildingUseCase } from '../../use-cases/buildings/unroll/useCase.js';

export async function unrollBuildingController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('User not allowed', 'BuildingsEntity', 401);
    }

    const buildingIdParsed = z.string().uuid().safeParse(req.params.buildingId);

    if (!buildingIdParsed.success) {
      throw new HttpError('Invalid building Id', 'BuildingsEntity', 400);
    }

    const result = unrollBuildingUseCase(buildingIdParsed.data);

    res.json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
