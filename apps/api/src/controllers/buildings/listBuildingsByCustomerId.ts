import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { paginationParams } from '../../helpers/paginate.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { listAllBuildingsByCorporateUseCase } from '../../use-cases/buildings/read-all-by-corporate-id/useCase.js';

export async function listAllBuildingsByCorporateController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('Account not found', 'BuildingsEntity', 404);
    }

    const filters = z
      .object({
        // 0: all buildings
        // 1: only the enrolled buildings
        filter: z.literal('0').or(z.literal('1')).optional().default('0'),
      })
      .parse(req.query);

    const pagination = paginationParams.parse(req.query);
    const buildings = await listAllBuildingsByCorporateUseCase(
      req.user,
      pagination,
      filters.filter,
    );

    res.status(200).json({
      status: 'OK',
      ...buildings,
    });
  } catch (error) {
    next(error);
  }
}
