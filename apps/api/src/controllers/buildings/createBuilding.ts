import { buildingValidationSchema } from '@nclarity/database';
import type { NextFunction, Request, Response } from 'express';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { createBuildingUseCase } from '../../use-cases/buildings/create/useCase.js';

export async function createBuildingController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('Account not found', 'BuildingsEntity', 404);
    }

    const buildingPayload = buildingValidationSchema.parse(req.body);

    const building = await createBuildingUseCase(buildingPayload);

    res.status(201).json({
      status: 'OK',
      data: building,
    });
  } catch (error) {
    next(error);
  }
}
