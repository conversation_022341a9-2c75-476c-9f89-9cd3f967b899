import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { getOneBuildingUseCase } from '../../use-cases/buildings/get-one/useCase.js';

export async function getBuildingByIdController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('User not allowed', 'BuildingsEntity', 403);
    }

    const buildingId = z.string().uuid().safeParse(req.params.buildingId);

    if (!buildingId.success) {
      throw new HttpError('Invalid building id', 'BuildingsEntity', 400);
    }

    const building = await getOneBuildingUseCase(buildingId.data, req.user);

    res.status(200).json({
      status: 'OK',
      data: building,
    });
  } catch (error) {
    next(error);
  }
}
