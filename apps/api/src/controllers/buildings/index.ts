/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { Router } from 'express';

import { permissionsMiddleware } from '../../middlewares/permissions.js';
import type { ComponentRoutes } from '../../types.js';
import { createBuildingController } from './createBuilding.js';
import { deleteBuildingByIdController } from './deleteBuildingById.js';
import { enrollBuildingController } from './enrollBuilding.js';
import { getBuildingByIdController } from './getBuildingById.js';
import { getSeverityForBuildingsController } from './getSeverityForBuildingsController.js';
import { listAllBuildingsByCorporateController } from './listBuildingsByCustomerId.js';
import { unrollBuildingController } from './unrollBuildings.js';
import { updateBuildingByIdController } from './updateBuildingById.js';
import { uploadBuildingPictureController } from './uploadBuildingPicture.js';

export class BuildingsRouter implements ComponentRoutes {
  readonly name: string = 'buildings';
  readonly router: Router = Router();

  constructor() {
    this.initRoutes();
  }

  initRoutes(): void {
    this.router
      .route('/')
      .get(
        permissionsMiddleware('buildings.list'),
        listAllBuildingsByCorporateController,
      )
      .post(
        permissionsMiddleware('buildings.create'),
        createBuildingController,
      );

    this.router
      .route('/condition')
      .get(
        permissionsMiddleware('buildings.read'),
        getSeverityForBuildingsController,
      );

    this.router
      .route('/:buildingId')
      .get(permissionsMiddleware('buildings.read'), getBuildingByIdController)
      .put(
        permissionsMiddleware('buildings.update'),
        updateBuildingByIdController,
      )
      .delete(
        permissionsMiddleware('buildings.delete'),
        deleteBuildingByIdController,
      );

    this.router
      .route('/:buildingId/picture')
      .post(
        permissionsMiddleware('buildings.update'),
        uploadBuildingPictureController,
      );

    this.router
      .route('/:buildingId/enroll')
      .post(
        permissionsMiddleware('buildings.update'),
        enrollBuildingController,
      );

    this.router
      .route('/:buildingId/unroll')
      .delete(
        permissionsMiddleware('buildings.update'),
        unrollBuildingController,
      );
  }
}
