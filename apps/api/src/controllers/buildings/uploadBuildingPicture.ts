import { Readable } from 'stream';
import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import {
  ContainerNames,
  FileStorageService,
} from '../../services/blob-storage/FileStorage.js';
import { getOneBuildingUseCase } from '../../use-cases/buildings/get-one/useCase.js';
import { updateBuildingByIdUseCase } from '../../use-cases/buildings/update/useCase.js';
import { uploadBuildingPictureUseCase } from '../../use-cases/buildings/upload-building-picture/uploadBuildingPicture.js';

export async function uploadBuildingPictureController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('Account not found', 'BuildingsEntity', 404);
    }

    const buildingId = z.string().uuid().safeParse(req.params.buildingId);
    const contentType = z
      .enum(['image/jpeg', 'image/png', 'image/jpg'])
      .safeParse(req.headers['content-type']);

    if (buildingId.success === false) {
      throw new HttpError('Building id is required', 'BuildingsEntity', 400);
    }

    if (contentType.success === false) {
      throw new HttpError('Content type is required', 'BuildingsEntity', 400);
    }

    const building = await getOneBuildingUseCase(buildingId.data, req.user);

    const readableStream = new Readable().wrap(req);

    const response = await uploadBuildingPictureUseCase(
      building,
      readableStream,
      contentType.data,
    );

    const updatedBuilding = await updateBuildingByIdUseCase(
      building.id,
      {
        image: response.fileName,
      },
      req.user,
    );

    const fileStorageClient = new FileStorageService(
      ContainerNames.BUILDINGS_PICTURES,
    );

    const buildingWithPicture = {
      ...updatedBuilding,
      logo: fileStorageClient.getFileUrl(response.fileName),
    };

    res.send({
      status: 'OK',
      data: buildingWithPicture,
    });
  } catch (error) {
    next(error);
  }
}
