import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { enrollBuildingUseCase } from '../../use-cases/buildings/enroll/useCase.js';
import { safeGetBuildingUseCase } from '../../use-cases/buildings/get-one/safeUseCase.js';

export async function enrollBuildingController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('User not allowed', 'BuildingsEntity', 403);
    }

    const buildingIdParsed = z.string().uuid().safeParse(req.params.buildingId);

    if (!buildingIdParsed.success) {
      throw new HttpError('Invalid building id', 'BuildingsEntity', 400);
    }

    const building = await safeGetBuildingUseCase(buildingIdParsed.data);

    if (!building) {
      throw new HttpError('Building not found', 'BuildingsEntity', 404);
    }

    const result = await enrollBuildingUseCase(building.id, req.user.accountId);

    res.json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
