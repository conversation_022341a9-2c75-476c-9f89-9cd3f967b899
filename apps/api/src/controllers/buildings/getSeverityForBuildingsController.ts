import type { NextFunction, Request, Response } from 'express';

import { HttpError } from '../../configuration/error.config.js';
import { isUserGuard } from '../../middlewares/authentication.js';
import { getBuildingsSeverityUseCase } from '../../use-cases/buildings/get-severity/useCase.js';

export async function getSeverityForBuildingsController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (!isUserGuard(req.user)) {
      throw new HttpError('Invalid user', 'BuildingsEntity', 403);
    }

    const result = await getBuildingsSeverityUseCase(req.user);

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
