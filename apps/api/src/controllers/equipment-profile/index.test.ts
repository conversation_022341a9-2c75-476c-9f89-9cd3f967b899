import express from 'express';
import request from 'supertest';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';

import { createTestApp, initTestDatabase } from '../../../tests/utils/app.js';
import {
  TEST_USERS,
  createUserToken,
  getAuthHeader,
} from '../../../tests/utils/auth.js';
import { TestDatabaseManager } from '../../../tests/utils/database.js';
import { EquipmentProfileRouter } from './index.js';

// PATCH endpoint: /v3/equipment-profile/:equipmentProfileId

describe('EquipmentProfileRouter (updateEquipmentProfileController)', () => {
  let app: express.Application;
  let adminToken: string;
  let testData: {
    corporate: any;
    user: any;
    building: any;
    equipment: any;
  };

  beforeAll(async () => {
    initTestDatabase();
    testData = await TestDatabaseManager.setupTestData();
  });

  afterAll(async () => {
    await TestDatabaseManager.cleanup();
  });

  beforeEach(() => {
    app = createTestApp((app) => {
      const router = new EquipmentProfileRouter();
      app.use('/v3/equipment-profile', router.router);
    });
    adminToken = createUserToken(TEST_USERS.admin);
  });

  describe('PATCH /v3/equipment-profile/:equipmentProfileId', () => {
    it('should update a packaged equipment profile', async () => {
      const { relational: db } = TestDatabaseManager.getInstances();

      const existingEquipment = await db.equipment.findUniqueOrThrow({
        where: {
          id: testData.equipment.id,
        },
        include: {
          profile: {
            include: {
              systemProfile: true,
            },
          },
        },
      });

      const equipmentProfileId = existingEquipment.documentId;
      const existingEquipmentId = existingEquipment.id;

      const patchData = {
        equipmentId: existingEquipmentId,
        equipmentProfile: {
          ...existingEquipment.profile,
          data: {
            ...existingEquipment.profile.data,
            equipmentInfo: {
              ...existingEquipment.profile.data.equipmentInfo,
              System: {
                ...existingEquipment.profile.data.equipmentInfo.System,
                Name: 'Updated Packaged Name',
                nominalTonnage: 1,
                ratedCapacity: 1,
              },
            },
          },
        },
        systemProfile: {
          ...existingEquipment.profile.systemProfile,
        },
      };

      const response = await request(app)
        .patch(`/v3/equipment-profile/${equipmentProfileId}`)
        .set(getAuthHeader(adminToken))
        .send(patchData);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(
        expect.objectContaining({
          status: 'OK',
          data: expect.objectContaining({
            voltsSensorOptional: false,
            data: expect.objectContaining({
              equipmentInfo: expect.objectContaining({
                System: expect.objectContaining({
                  Name: 'Updated Packaged Name',
                }),
              }),
            }),
          }),
        }),
      );

      const updated = await db.equipmentProfile.findUnique({
        where: { id: equipmentProfileId },
      });

      expect(updated?.data.equipmentInfo.System.Name).toBe(
        'Updated Packaged Name',
      );
      expect(updated?.voltsSensorOptional).toBe(false);
    });
  });
});
