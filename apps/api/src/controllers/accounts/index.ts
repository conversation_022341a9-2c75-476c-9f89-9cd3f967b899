/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { Router } from 'express';

import { permissionsMiddleware } from '../../middlewares/permissions.js';
import type { ComponentRoutes } from '../../types.js';
import { createAccountController } from './createAccount.js';
import { deleteAccountController } from './deleteAccount.js';
import { editAccountController } from './editAccount.js';
import { getAccountsController } from './getAll.js';
import { getUsersByAccountAndRoleController } from './getUsersByAccountAndRole.js';

export class AccountsRouter implements ComponentRoutes {
  readonly name: string = 'accounts';
  readonly router: Router = Router();

  constructor() {
    this.initRoutes();
  }

  initRoutes(): void {
    this.router
      .route('/')
      .get(permissionsMiddleware('corporate.list'), getAccountsController)
      .post(permissionsMiddleware('corporate.create'), createAccountController);

    this.router
      .route('/:id')
      .put(permissionsMiddleware('corporate.update'), editAccountController)
      .delete(
        permissionsMiddleware('corporate.delete'),
        deleteAccountController,
      );

    this.router
      .route('/users')
      .get(
        permissionsMiddleware('corporate.list'),
        getUsersByAccountAndRoleController,
      );
  }
}
