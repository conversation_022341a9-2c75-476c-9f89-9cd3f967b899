import type { NextFunction, Request, Response } from 'express';

import { HttpError } from '../../configuration/error.config.js';
import { deleteAccountUseCase } from '../../use-cases/account/delete/useCase.js';
import { findAccountUseCase } from '../../use-cases/account/find-an-account/useCase.js';

export async function deleteAccountController(
  req: Request<{ id: string }>,
  res: Response,
  next: NextFunction,
) {
  if (!req.params.id) {
    throw new HttpError('Account id not provided', 'Accounts', 400);
  }

  try {
    // Find account and users
    const account = await findAccountUseCase({ id: Number(req.params.id) });

    if (!account) {
      throw new HttpError('Unable to find account', 'Accounts', 400);
    }

    const deletedAccountData = await deleteAccountUseCase(account.id);

    res.send({
      status: 'OK',
      data: deletedAccountData,
    });
  } catch (error) {
    next(error);
  }
}
