import { UserRole } from '@nclarity/database';
import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { findAccountUseCase } from '../../use-cases/account/find-an-account/useCase.js';
import { getUsersByAccountAndRoleUseCase } from '../../use-cases/account/get-users-by-account-and-role/useCase.js';

export async function getUsersByAccountAndRoleController(
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> {
  try {
    const { accountId, role } = req.query;

    const roleParsed = z.nativeEnum(UserRole).safeParse(role);

    if (!roleParsed.success) {
      throw new HttpError('Invalid role', 'Accounts', 401);
    }

    const account = await findAccountUseCase({
      id: Number(accountId),
    });

    if (!account) {
      throw new HttpError('Unable to find account', 'Accounts', 401);
    }

    const result = await getUsersByAccountAndRoleUseCase(
      Number(accountId),
      roleParsed.data,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
