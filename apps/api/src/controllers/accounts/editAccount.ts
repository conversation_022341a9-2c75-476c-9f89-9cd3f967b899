import type { AccountFormAttributes } from '@nclarity/database';
import type { NextFunction, Request, Response } from 'express';

import { HttpError } from '../../configuration/error.config.js';
import { editAccountUseCase } from '../../use-cases/account/edit/useCase.js';
import { findAccountUseCase } from '../../use-cases/account/find-an-account/useCase.js';

export async function editAccountController(
  req: Request<{ id: string }, any, AccountFormAttributes & { id: number }>,
  res: Response,
  next: NextFunction,
) {
  try {
    const account = await findAccountUseCase({
      id: Number(req.params.id),
    });

    if (!account) {
      throw new HttpError('Unable to find account', 'Accounts', 401);
    }

    const deletedAccount = await editAccountUseCase(req.body);

    res.send({
      status: 'OK',
      data: deletedAccount,
    });
  } catch (error) {
    next(error);
  }
}
