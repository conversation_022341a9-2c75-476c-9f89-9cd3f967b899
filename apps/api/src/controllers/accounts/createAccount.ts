import {
  accountCreationSchema,
  type AccountFormAttributes,
} from '@nclarity/database';
import type { NextFunction, Request, Response } from 'express';

import { HttpError } from '../../configuration/error.config.js';
import { createAccountUseCase } from '../../use-cases/account/create-account/useCase.js';
import { findAccountUseCase } from '../../use-cases/account/find-an-account/useCase.js';
import { findUserUseCase } from '../../use-cases/users/find-user/findUser.js';

export async function createAccountController(
  req: Request<never, any, AccountFormAttributes>,
  res: Response,
  next: NextFunction,
) {
  try {
    const foundUser = await findUserUseCase({
      email: req.body.contactEmail,
    });

    if (foundUser) {
      throw new HttpError('User already exists', 'Accounts', 401);
    }

    const foundAccount = await findAccountUseCase({
      companyName: req.body.companyName,
    });

    if (foundAccount) {
      throw new HttpError('Account already exists', 'Accounts', 401);
    }

    const body = accountCreationSchema.parse(req.body);

    const accountData = await createAccountUseCase(body);

    res.send({
      status: 'OK',
      data: accountData,
    });
  } catch (error) {
    next(error);
  }
}
