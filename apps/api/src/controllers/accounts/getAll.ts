import type { NextFunction, Request, Response } from 'express';

import paginate from '../../helpers/paginate.js';
import { getAllAccountsUseCase } from '../../use-cases/account/get-all/useCase.js';

type GetAccountsParams = {
  paginate?: string;
  page?: string;
  pageSize?: string;
};

export async function getAccountsController(
  req: Request<GetAccountsParams, any, any, GetAccountsParams>,
  res: Response,
  next: NextFunction,
) {
  try {
    const shouldPaginate = Boolean(Number(req.query.paginate));
    const pagination = shouldPaginate
      ? paginate(req.query.page, req.query.pageSize)
      : {};

    const results = await getAllAccountsUseCase(pagination);

    res.send({
      status: 'OK',
      data: results,
      ...(shouldPaginate ? { pagination } : {}),
    });
  } catch (error) {
    next(error);
  }
}
