import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { reviewOneCbmAlertUseCase } from '../../use-cases/cbm-insights/reviewOneCbmAlert/useCase.js';

export async function reviewOneCBMAlertController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const queryParsed = z
      .object({
        cbmHistoryId: z.string(),
      })
      .safeParse(req.params);

    if (!queryParsed.success) {
      throw new HttpError('Invalid payload', 'CBMAlerts', 400);
    }
    const result = await reviewOneCbmAlertUseCase(
      queryParsed.data.cbmHistoryId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
