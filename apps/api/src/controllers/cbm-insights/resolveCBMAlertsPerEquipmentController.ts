import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { resolveCBMAlertsPerEquipmentUseCase } from '../../use-cases/cbm-insights/resolveCBMAlertsPerEquipment/useCase.js';
import { safeGetOneEquipmentUseCase } from '../../use-cases/equipment/get-one/safeUseCase.js';

export async function resolveCBMAlertsPerEquipmentController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const queryParsed = z
      .object({
        equipmentId: z.string(),
      })
      .safeParse(req.query);

    if (!queryParsed.success) {
      throw new HttpError('Missing equipment id', 'CBMAlerts', 400);
    }

    const equipment = await safeGetOneEquipmentUseCase(
      queryParsed.data.equipmentId,
    );

    if (!equipment) {
      throw new HttpError("Equipment doesn't exist", 'CBMAlerts', 404);
    }

    const result = await resolveCBMAlertsPerEquipmentUseCase(
      queryParsed.data.equipmentId,
      equipment.buildingId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
