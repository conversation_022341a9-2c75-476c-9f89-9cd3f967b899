import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { resolveOneCbmAlertUseCase } from '../../use-cases/cbm-insights/resolveOneCbmAlert/useCase.js';

export async function resolveOneCBMAlertController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const queryParsed = z
      .object({
        cbmHistoryId: z.string(),
      })
      .safeParse(req.params);

    if (!queryParsed.success) {
      throw new HttpError('Missing equipment id', 'CBMAlerts', 400);
    }

    const result = await resolveOneCbmAlertUseCase(
      queryParsed.data.cbmHistoryId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
