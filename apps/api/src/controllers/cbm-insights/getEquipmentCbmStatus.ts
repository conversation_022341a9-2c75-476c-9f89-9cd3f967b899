import type { NextFunction, Request, Response } from 'express';
import z from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { getEquipmentCbmStatusUseCase } from '../../use-cases/cbm-insights/getEquipmentCBMStatus/useCase.js';
import { safeGetOneEquipmentUseCase } from '../../use-cases/equipment/get-one/safeUseCase.js';

export async function getCbmStatusByEquipmentId(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (req.query.buildingId) {
      next();

      return;
    }

    const queryParsed = z
      .object({
        equipmentId: z.string(),
      })
      .safeParse(req.query);

    if (!queryParsed.success) {
      throw new Error('Invalid equipment id');
    }

    const equipment = await safeGetOneEquipmentUseCase(
      queryParsed.data.equipmentId,
    );

    if (!equipment) {
      throw new HttpError("Equipment doesn't exist", 'CBMAlerts', 404);
    }

    const cbmStatus = await getEquipmentCbmStatusUseCase(
      queryParsed.data.equipmentId,
      equipment.buildingId,
    );

    res.status(200).json({
      status: 'OK',
      data: cbmStatus,
    });
  } catch (error) {
    next(error);
  }
}
