import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { safeGetBuildingUseCase } from '../../use-cases/buildings/get-one/safeUseCase.js';
import { reviewAllCBMAlertsPerBuildingUseCase } from '../../use-cases/cbm-insights/reviewAllCBMAlertsPerBuilding/useCase.js';

export async function reviewCBMAlertsPerBuildingController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (req.query.equipmentId) {
      next();

      return;
    }

    const queryParsed = z
      .object({
        buildingId: z.string(),
      })
      .safeParse(req.query);

    if (!queryParsed.success) {
      throw new HttpError('Missing building id', 'CBMAlerts', 400);
    }

    const building = await safeGetBuildingUseCase(queryParsed.data.buildingId);

    if (!building) {
      throw new HttpError("Building doesn't exist", 'CBMAlerts', 404);
    }

    const result = await reviewAllCBMAlertsPerBuildingUseCase(
      queryParsed.data.buildingId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
