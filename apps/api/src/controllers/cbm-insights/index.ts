import { Router } from 'express';

import { permissionsMiddleware } from '../../middlewares/permissions.js';
import type { ComponentRoutes } from '../../types.js';
import { createCbmInsightsController } from './createCBMInsights.js';
import { deleteMultipleCBMAlertsController } from './deleteMultiplesCBMAlerts.js';
import { deleteSingleCBMAlertController } from './deleteSingleCBMAlert.js';
import { getCbmStatusByBuildingId } from './getBuildingCbmStatus.js';
import { getCBMAlertsController } from './getCBMAlerts.js';
import { getCBMRelapsesController } from './getCBMRelapsesController.js';
import { getCbmHistoryCountController } from './getCbmHistoryCount.js';
import { getCbmStatusByEquipmentId } from './getEquipmentCbmStatus.js';
import { resolveCBMAlertsPerBuildingController } from './resolveCBMAlertsPerBuildingController.js';
import { resolveCBMAlertsPerEquipmentController } from './resolveCBMAlertsPerEquipmentController.js';
import { resolveOneCBMAlertController } from './resolveOneCbmAlert.js';
import { reviewCBMAlertsPerBuildingController } from './reviewCBMAlertsPerBuildingController.js';
import { reviewCBMAlertsPerEquipmentController } from './reviewCBMAlertsPerEquipmentController.js';
import { reviewOneCBMAlertController } from './reviewOneCbmAlert.js';

export class CBMInsightsRouter implements ComponentRoutes {
  readonly name: string = 'cbm-alerts';
  readonly router: Router = Router();
  constructor() {
    this.initRoutes();
  }

  initRoutes(): void {
    this.router
      .route('/')
      .post(
        permissionsMiddleware('cbm-insights.create'),
        createCbmInsightsController,
      )
      .get(permissionsMiddleware('cbm-insights.read'), getCBMAlertsController)
      .delete(
        permissionsMiddleware('cbm-insights.delete'),
        deleteMultipleCBMAlertsController,
      );

    this.router
      .route('/status')
      .get(
        permissionsMiddleware('cbm-insights.read'),
        getCbmStatusByEquipmentId,
        getCbmStatusByBuildingId,
      );

    this.router
      .route('/review')
      .patch(
        permissionsMiddleware('cbm-insights.update'),
        reviewCBMAlertsPerBuildingController,
        reviewCBMAlertsPerEquipmentController,
      );

    this.router
      .route('/resolve')
      .patch(
        permissionsMiddleware('cbm-insights.update'),
        resolveCBMAlertsPerBuildingController,
        resolveCBMAlertsPerEquipmentController,
      );

    this.router
      .route('/count')
      .get(
        permissionsMiddleware('cbm-insights.read'),
        getCbmHistoryCountController,
      );

    this.router
      .route('/:cbmHistoryId')
      .delete(
        permissionsMiddleware('cbm-insights.delete'),
        deleteSingleCBMAlertController,
      );

    this.router
      .route('/:cbmHistoryId/resolve')
      .patch(
        permissionsMiddleware('cbm-insights.update'),
        resolveOneCBMAlertController,
      );

    this.router
      .route('/:cbmHistoryId/review')
      .patch(
        permissionsMiddleware('cbm-insights.update'),
        reviewOneCBMAlertController,
      );

    this.router
      .route('/:cbmHistoryId/relapses')
      .get(
        permissionsMiddleware('cbm-insights.read'),
        getCBMRelapsesController,
      );
  }
}
