import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { resolveCBMAlertsPerBuildingsUseCase } from '../../use-cases/cbm-insights/resolveCBMAlertsPerBuildings/useCase.js';

export async function resolveCBMAlertsPerBuildingController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    if (req.query.equipmentId) {
      next();

      return;
    }

    const queryParsed = z
      .object({
        buildingId: z.string(),
      })
      .safeParse(req.query);

    if (!queryParsed.success) {
      throw new HttpError('Missing building id', 'CBMAlerts', 400);
    }

    const result = await resolveCBMAlertsPerBuildingsUseCase(
      queryParsed.data.buildingId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
