import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { HttpError } from '../../configuration/error.config.js';
import { reviewAllCBMAlertsPerEquipmentIdUseCase } from '../../use-cases/cbm-insights/reviewAllCBMAlertsPerEquipment/useCase.js';
import { safeGetOneEquipmentUseCase } from '../../use-cases/equipment/get-one/safeUseCase.js';

export async function reviewCBMAlertsPerEquipmentController(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const queryParsed = z
      .object({
        equipmentId: z.string(),
      })
      .safeParse(req.query);

    if (!queryParsed.success) {
      throw new HttpError('Missing Equipment Id', 'CBMAlerts', 400);
    }

    const equipment = await safeGetOneEquipmentUseCase(
      queryParsed.data.equipmentId,
    );

    if (!equipment) {
      throw new HttpError('Equipment not found', 'CBMAlerts', 404);
    }

    const result = await reviewAllCBMAlertsPerEquipmentIdUseCase(
      queryParsed.data.equipmentId,
    );

    res.status(200).json({
      status: 'OK',
      data: result,
    });
  } catch (error) {
    next(error);
  }
}
