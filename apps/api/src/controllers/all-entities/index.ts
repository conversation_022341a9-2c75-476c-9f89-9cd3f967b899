import { Router } from 'express';

import { permissionsMiddleware } from '../../middlewares/permissions.js';
import type { ComponentRoutes } from '../../types.js';
import { getAllEntities } from './allEntities.js';

export class AllEntitiesRouter implements ComponentRoutes {
  readonly name: string = 'all-entities';
  readonly router: Router = Router();

  constructor() {
    this.initRoutes();
  }

  initRoutes(): void {
    this.router
      .route('/')
      .get(permissionsMiddleware('users.create'), getAllEntities);
  }
}
