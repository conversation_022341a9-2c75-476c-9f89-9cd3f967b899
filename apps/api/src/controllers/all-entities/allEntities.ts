import type { NextFunction, Request, Response } from 'express';

import { DatabaseManager } from '../../database/index.js';

export async function getAllEntities(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  try {
    const { relational } = DatabaseManager.getInstances();

    const corporate = await relational.corporate.findFirst({
      where: {
        companyName: 'nClarity Development',
      },
      include: {
        regions: {
          take: 1,
        },
        devices: {
          take: 1,
        },
        users: {
          take: 1,
        },
        customers: {
          take: 1,
          include: {
            customerBuildings: {
              take: 1,
              include: {
                equipment: {
                  take: 1,
                },
              },
            },
          },
        },
      },
    });
    const [opsUser] = await relational.opsUser.findMany({
      take: 1,
    });

    res.status(200).json({
      status: 'OK',
      data: {
        mysql: {
          corporate,
          opsUser,
        },
      },
    });
  } catch (error) {
    next(error);
  }
}
