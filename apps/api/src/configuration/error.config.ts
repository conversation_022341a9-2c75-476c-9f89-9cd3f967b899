type Services =
  | 'Charts'
  | 'CBMAlerts'
  | 'Main'
  | 'Accounts'
  | 'Pulses'
  | 'Authentication'
  | 'Users'
  | 'DeviceConfig'
  | 'Insights'
  | 'Equipment'
  | 'OpsUsersEntity'
  | 'Tags'
  | 'Rules'
  | 'RegionsEntity'
  | 'CustomersEntity'
  | 'BuildingsEntity'
  | 'EquipmentEntity'
  | 'AlertsEntity'
  | 'EquipmentProfileEntity'
  | 'UnassignedPulsesEntity'
  | 'SystemProfilesEntity'
  | 'DeviceSimulationEntity'
  | 'UserPreferencesEntity'
  | 'EquipmentPerformanceEntity'
  | 'NotificationEntity'
  | 'TelemetryEntity'
  | 'DeviceConfigEntity'
  | 'DefaultRulesEntity'
  | 'DeviceEntity'
  | 'EquipmentNotes';

export class HttpError extends Error {
  public statusCode = 500;
  public service: Services = 'Main';
  public stack: string | undefined;

  constructor(message: string, service: Services, statusCode = 500) {
    super();
    this.name = 'HttpError';
    this.statusCode = statusCode;
    this.service = service;
    this.message = `${service} - ${message}`;
    Error.captureStackTrace(this, HttpError);

    Object.setPrototypeOf(this, HttpError.prototype);
  }
}
