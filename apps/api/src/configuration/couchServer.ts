import got from 'got';

export const couchServer = {
  protocol: process.env.couch_db_protocol as string,
  host: process.env.couch_db_host as string,
  user: {
    name: process.env.couch_db_username as string,
    password: process.env.couch_db_password as string,
  },
  url: `${process.env.couch_db_protocol as string}${
    process.env.couch_db_host as string
  }:5984/`,
  curl: encodeURI(
    `${process.env.couch_db_protocol as string}${
      process.env.couch_db_username as string
    }:${process.env.couch_db_password as string}@${
      process.env.couch_db_host as string
    }:5984/`,
  ),
  port: 5984,
  pouchOpts: {
    // eslint-disable-next-line camelcase
    skip_setup: true,
  },
};

const couchServerService = got.extend({
  prefixUrl: couchServer.url,
});

export default couchServerService;
