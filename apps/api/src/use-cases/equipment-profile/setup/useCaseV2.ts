import {
  EquipmentCondition,
  EquipmentStatus,
  type Corporate,
  type Prisma,
} from '@nclarity/database';
import { z } from 'zod';

import { DatabaseManager } from '../../../database/index.js';
import {
  EquipmentProfileSetupSchema,
  SystemProfileSchema,
} from './validations.js';

type Payload = {
  profile: Prisma.EquipmentProfileUncheckedUpdateInput & { id: string };
  systemProfile?: Prisma.SystemProfileUncheckedUpdateInput & { id: string };
};

export async function setupEquipmentProfileUseCaseV2(params: {
  equipmentProfileId: string;
  payload: Payload;
  corporate: Corporate;
}) {
  const { payload, equipmentProfileId, corporate } = params;
  const { relational } = DatabaseManager.getInstances();

  const parsed = z
    .object({
      equipmentProfile: EquipmentProfileSetupSchema,
    })
    .parse({ equipmentProfile: payload.profile });

  const result = await relational.$transaction(async (tx) => {
    if (payload.systemProfile) {
      const existingSystemProfile =
        payload.systemProfile?.id !== 'custom'
          ? await tx.systemProfile.findUnique({
              where: {
                id: payload.systemProfile.id,
              },
            })
          : undefined;

      if (existingSystemProfile) {
        parsed.equipmentProfile.systemProfileId = existingSystemProfile.id;
      } else {
        const systemProfileParsed = z
          .object({
            systemProfile: SystemProfileSchema,
          })
          .parse({
            systemProfile: payload.systemProfile
              ? { ...payload.systemProfile, corporateId: corporate.id }
              : {},
          });

        const systemProfileCreated = await tx.systemProfile.create({
          data: {
            ...systemProfileParsed.systemProfile,
            id: undefined,
          },
        });

        parsed.equipmentProfile.systemProfileId = systemProfileCreated.id;
      }
    }

    const result = await tx.equipmentProfile.update({
      where: {
        id: equipmentProfileId,
      },
      data: {
        ...(parsed.equipmentProfile as Prisma.EquipmentProfileUncheckedUpdateInput),
        isDefault: false,
      },
      include: {
        systemProfile: true,
      },
    });

    await tx.equipmentStatusRecord.update({
      where: {
        equipmentId: result.equipmentId,
      },
      data: {
        status: EquipmentStatus.AwaitingInstallation,
        condition: EquipmentCondition.Stable,
      },
    });

    return result;
  });

  return result;
}
