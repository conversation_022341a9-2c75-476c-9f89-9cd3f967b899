/* eslint-disable camelcase */

import { $Enums, Prisma, xEquipmentProfiles } from '@nclarity/database';
import { z } from 'zod';

const airHandlerFormSchema: z.ZodType<
  xEquipmentProfiles.EquipmentInfoAirHandler,
  z.ZodTypeDef
> = z.object({
  location: z.string().or(z.undefined()),
  Phase: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserPhase, {
      invalid_type_error: 'Air Handler Phase must be one of ${values}',
    })
    .default(xEquipmentProfiles.EquipmentInfoCondenserPhase.ThreePhase)
    .or(z.undefined()),
  NominalVoltage: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage)
    .default(
      xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage['208V/230'],
    )
    .or(z.undefined()),
  Manufacturer: z.string().or(z.undefined()),
  ModelNumber: z.string().or(z.undefined()),
  SerialNumber: z.string().or(z.undefined()),
});

const condenserFormSchema: z.ZodType<
  xEquipmentProfiles.EquipmentInfoCondenser,
  z.ZodTypeDef
> = z.object({
  Phase: z.nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserPhase, {
    invalid_type_error: 'Condenser Phase must be one of ${values}',
  }),
  NominalVoltage: z.nativeEnum(
    xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage,
  ),
  Manufacturer: z.string().or(z.undefined()),
  ModelNumber: z.string().or(z.undefined()),
  SerialNumber: z.string().or(z.undefined()),
});

const splitSystemEquipmentInfoSchema = z.object({
  systemType: z.literal(xEquipmentProfiles.EquipmentInfoSystemType.Split),
  Name: z.string().min(1),
  installedLineLength: z.number().or(z.undefined()),
  nominalTonnage: z.number({
    coerce: true,
    invalid_type_error: 'Nominal Tonnage is invalid',
    required_error: 'Nominal Tonnage is required',
  }),
  ratedTesp: z.number().or(z.undefined()),
  ratedAirflow: z.number().or(z.undefined()),
  ratedCapacity: z.number({
    invalid_type_error: 'Rated Capacity is required',
    required_error: 'Rated Capacity is required',
  }),
  geoTag: z.undefined().or(
    z.object({
      lat: z.number().or(z.undefined()),
      lng: z.number().or(z.undefined()),
    }),
  ),
  includes: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoIncludes)
    .or(z.literal(false))
    .or(z.undefined()),
  frequency: z.nativeEnum(xEquipmentProfiles.EquipmentInfoSystemFrequency),
  refNamePlateCharge: z.number().or(z.undefined()),
  lineSet: z.number().or(z.undefined()),
  cooling: z.boolean().or(z.undefined()),
  ifmPhase: z.nativeEnum(xEquipmentProfiles.EquipmentInfoIfmPhase),
  ifmVoltage: z.nativeEnum(xEquipmentProfiles.EquipmentInfoIfmVoltage),
  equipmentLocationImage: z.string().or(z.undefined()),
  furnaceUnitType: z
    .nativeEnum(xEquipmentProfiles.FurnaceUnitType)
    .or(z.undefined()),
});

const packageEquipmentInfoSystem = z.object({
  systemType: z.literal(xEquipmentProfiles.EquipmentInfoSystemType.Package),
  Name: z.string().min(1),
  Notes: z.string().or(z.undefined()),
  installedLineLength: z.number().or(z.undefined()),
  nominalTonnage: z.number({
    coerce: true,
    invalid_type_error: 'Nominal Tonnage is invalid',
    required_error: 'Nominal Tonnage is required',
  }),
  ratedTesp: z.number().or(z.undefined()),
  ratedAirflow: z.number().or(z.undefined()),
  ratedCapacity: z.number({
    invalid_type_error: 'Rated Capacity is required',
    required_error: 'Rated Capacity is required',
  }),
  blowerType: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoBlowerType)
    .or(z.undefined()),
  blowerSpeed: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoBlowerSpeed)
    .or(z.undefined()),
  blowerVoltage: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage)
    .or(z.undefined()),
  geoTag: z.undefined().or(
    z.object({
      lat: z.number().or(z.undefined()),
      lng: z.number().or(z.undefined()),
    }),
  ),
  includes: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoIncludes)
    .or(z.literal(false))
    .or(z.undefined()),
  frequency: z.nativeEnum(xEquipmentProfiles.EquipmentInfoSystemFrequency),
  refNamePlateCharge: z.number().or(z.undefined()),
  lineSet: z.number().or(z.undefined()),
  cooling: z.boolean().or(z.undefined()),
  equipmentLocationImage: z.string().or(z.undefined()),
  furnaceUnitType: z
    .nativeEnum(xEquipmentProfiles.FurnaceUnitType)
    .or(z.undefined()),
});

export const equipmentInfoSystem = z.discriminatedUnion('systemType', [
  splitSystemEquipmentInfoSchema,
  packageEquipmentInfoSystem,
]);

const furnaceFormSchema = z.object({
  Phase: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserPhase)
    .or(z.undefined()),
  NominalVoltage: z
    .nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage)
    .or(z.undefined()),
  ratedInput: z.number({ coerce: true }).or(z.undefined()),
  ratedOutput: z.number({ coerce: true }).or(z.undefined()),
  minGasPressure: z.number({ coerce: true }).or(z.undefined()),
  maxGasPressure: z.number({ coerce: true }).or(z.undefined()),
  // needs to be validated in super refine
  tempRise: z
    .object({
      value1: z.number({ coerce: true }),
      value2: z.number({ coerce: true }),
    })
    .or(z.undefined()),
  imanifoldPressureHigh: z.number({ coerce: true }).or(z.undefined()),
  imanifoldPressureLow: z.number({ coerce: true }).or(z.undefined()),
  switchCutoutHigh: z.number({ coerce: true }).or(z.undefined()),
  switchCutoutLow: z.number({ coerce: true }).or(z.undefined()),
  gasOrElectric: z.string().or(z.undefined()),
  FurnaceType: z.string().or(z.undefined()),
  Manufacturer: z.string().or(z.undefined()),
  ModelNumber: z.string().or(z.undefined()),
  SerialNumber: z.string().or(z.undefined()),
  location: z.string().or(z.undefined()),
});

const evaporatorSchema: z.ZodType<
  xEquipmentProfiles.EquipmentInfoEvaporator,
  z.ZodTypeDef
> = z.object({
  Manufacturer: z.string().or(z.undefined()),
  ModelNumber: z.string().or(z.undefined()),
  SerialNumber: z.string().or(z.undefined()),
});

export const equipmentInfoValidationSchema = z
  .object({
    equipmentId: z
      .string({
        coerce: true,
      })
      .min(1),
    System: equipmentInfoSystem,
    Condenser: condenserFormSchema,
    'Air Handler': airHandlerFormSchema.or(z.undefined()),
    Furnace: furnaceFormSchema.or(z.undefined()),
    Evaporator: evaporatorSchema.or(z.undefined()),
  })
  .superRefine((equipmentInfo, ctx) => {
    if (
      equipmentInfo.System.includes ===
      xEquipmentProfiles.EquipmentInfoIncludes['Furnace']
    ) {
      if (!equipmentInfo.Furnace?.tempRise?.value1) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          message: 'Allowable Temperature Rise From',
          expected: z.ZodParsedType.number,
          received: z.ZodParsedType.undefined,
          path: ['Furnace', 'tempRise', 'value1'],
        });
      }

      if (!equipmentInfo.Furnace?.tempRise?.value2) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          message: 'Allowable Temperature Rise To',
          expected: z.ZodParsedType.number,
          received: z.ZodParsedType.undefined,
          path: ['Furnace', 'tempRise', 'value2'],
        });
      }

      if (!equipmentInfo.Furnace?.ratedOutput) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          message: 'Rated Output',
          expected: z.ZodParsedType.number,
          received: z.ZodParsedType.undefined,
          path: ['Furnace', 'ratedOutput'],
        });
      }

      if (!equipmentInfo.Furnace?.Phase) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          message: 'Phase',
          expected: z.ZodParsedType.number,
          received: z.ZodParsedType.undefined,
          path: ['Furnace', 'Phase'],
        });
      }

      if (!equipmentInfo.Furnace?.NominalVoltage) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          message: 'Nominal Voltage',
          expected: z.ZodParsedType.number,
          received: z.ZodParsedType.undefined,
          path: ['Furnace', 'NominalVoltage'],
        });
      }
    }
  });

const EquipmentProfileDataSchema = z.object({
  atmPress: z.number({ coerce: true }).optional(),
  equipmentInfo: equipmentInfoValidationSchema.optional(),
  alertConfiguration: z
    .array(
      z.object({
        point: z.nativeEnum(xEquipmentProfiles.AlertConfigurationPoints),
        recommended: z.number({ coerce: true }),
        current: z.number({ coerce: true }),
      }),
    )
    .optional(),
});

export const EquipmentProfileSetupSchema = z.object({
  id: z.string().optional(),
  isDefault: z.boolean().optional(),
  inServiceDate: z.union([z.date(), z.string(), z.null()]).optional(),
  oaSensorOptional: z.boolean().optional(),
  voltsSensorOptional: z.boolean().optional(),
  systemProfileId: z.string().optional(),
  equipmentId: z.string().optional(),
  data: EquipmentProfileDataSchema.optional(),
  createdAt: z.union([z.date(), z.string()]).optional(),
  updatedAt: z.union([z.date(), z.string()]).optional(),
});

export const SystemProfileSchema: z.ZodSchema<Prisma.SystemProfileUncheckedCreateInput> =
  z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      isDefault: z.boolean().optional(),
      corporateId: z.number().optional(),
      profileName: z.string().optional(),
      systemType: z.nativeEnum($Enums.SystemProfileSystemType).optional(),
      heatingMode: z.boolean({ coerce: true }).optional(),
      meteringDeviceType: z
        .nativeEnum($Enums.SystemProfileMeteringDeviceType)
        .optional(),
      refrigerantId: z.number(),
      condenserName: z.string().optional().nullable(),
      customCondenser: z.boolean({ coerce: true }).optional(),
      condenserEfficiencyValue: z
        .nativeEnum($Enums.SystemProfileCondenserEfficiencyValue)
        .optional(),
      condenserDTDCTOA: z.number().optional(),
      headPressureControlId: z.number().optional(),
      minCondTemperature: z.number().optional(),
      minCondPressure: z.number().optional(),
      pressureDifferential: z.number().optional(),
      targetSubCooling: z.number().optional(),
      targetSystemSubCooling: z.number().optional(),
      evaporatorName: z.string().optional().nullable(),
      evaporatorEfficiencyValue: z
        .nativeEnum($Enums.SystemProfileEvaporatorEfficiencyValue)
        .optional(),
      evaporatorDTD: z.number().optional().nullable(),
      boxTemperature: z.number().optional().nullable(),
      targetSuperheat: z.number().optional().nullable(),
      targetSystemSuperheat: z.number().optional().nullable(),
      compressorCount: z.number().optional().nullable(),
      evaporatorCount: z.number().optional().nullable(),
      condensorCount: z.number().optional().nullable(),
      customEvaporator: z.boolean({ coerce: true }).optional(),
      createdAt: z.union([z.date(), z.string()]).optional(),
      updatedAt: z.union([z.date(), z.string()]).optional(),
    })
    .superRefine((data, ctx) => {
      if (
        (data.isDefault && data.id === 'custom') ||
        (data.isDefault && !!data.id)
      ) {
        ctx.addIssue({
          code: 'custom',
          message: 'System profile cannot be custom and default profile',
        });
      }
    });
