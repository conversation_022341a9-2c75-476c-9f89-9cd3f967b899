import { Prisma, type Corporate } from '@nclarity/database';
import { z } from 'zod';

import { DatabaseManager } from '../../../../database/index.js';
import { ChillerEquipmentProfileSetupSchema } from './validations.js';

export async function updateChillerProfileUseCase(params: {
  equipmentProfileId: string;
  payload: {
    profile: Prisma.EquipmentProfileUncheckedUpdateInput & { id: string };
    systemProfile?: Prisma.SystemProfileUncheckedUpdateInput & { id: string };
    corporate: Corporate;
  };
}) {
  const { equipmentProfileId, payload } = params;
  const { profile, systemProfile, corporate } = payload;
  const { relational } = DatabaseManager.getInstances();

  const parsed = z
    .object({
      equipmentProfile: ChillerEquipmentProfileSetupSchema,
    })
    .parse({ equipmentProfile: profile });

  const updatedProfile = await relational.$transaction(async (tx) => {
    let systemProfileId = parsed.equipmentProfile.systemProfileId;

    if (systemProfile) {
      const existingSystemProfile =
        systemProfile.id !== 'custom'
          ? await tx.systemProfile.findUnique({
              where: {
                id: systemProfile.id,
              },
            })
          : undefined;

      if (existingSystemProfile) {
        systemProfileId = existingSystemProfile.id;
      } else {
        const systemProfileParsed = z
          .object({
            systemProfile: z.any(),
          })
          .parse({
            systemProfile: systemProfile
              ? { ...systemProfile, corporateId: corporate.id }
              : {},
          });

        const systemProfileCreated = await tx.systemProfile.create({
          data: {
            ...(systemProfileParsed.systemProfile as Prisma.SystemProfileCreateInput),
            id: undefined,
          },
        });

        systemProfileId = systemProfileCreated.id;
      }
    }

    const updatePayload: Prisma.EquipmentProfileUncheckedUpdateInput = {
      ...parsed.equipmentProfile,
      ...(systemProfileId ? { systemProfileId } : {}),
    };

    return tx.equipmentProfile.update({
      where: { id: equipmentProfileId },
      data: updatePayload,
    });
  });

  return updatedProfile;
}
