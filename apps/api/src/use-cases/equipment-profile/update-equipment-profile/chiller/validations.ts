import { xEquipmentProfiles } from '@nclarity/database';
import { z } from 'zod';

// Chiller-specific validation schema
const chillerSystemSchema = z.object({
  systemType: z.literal(xEquipmentProfiles.EquipmentInfoSystemType.Chiller),
  Name: z.string().min(1).optional(),
  installedLineLength: z.number().optional(),
  nominalTonnage: z.number().optional(),
  ratedCapacity: z.number().optional(),
  frequency: z.nativeEnum(xEquipmentProfiles.EquipmentInfoSystemFrequency),
  modelNumber: z.string().optional(),
  manufacturer: z.string().optional(),
  chillerPhase: z.nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserPhase),
  chillerVoltage: z.nativeEnum(
    xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage,
  ),
  nominalVoltage: z.nativeEnum(
    xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage,
  ),
});

const chillerEquipmentInfoSchema = z.object({
  equipmentId: z.string().min(1),
  kind: z.literal(xEquipmentProfiles.ProfileDataKind.Chiller),
  System: chillerSystemSchema,
  manufacturer: z.string().optional(),
  modelNumber: z.string().optional(),
  serialNumber: z.string().optional(),
  location: z.string().optional(),
  chillerPhase: z.nativeEnum(xEquipmentProfiles.EquipmentInfoCondenserPhase),
  chillerVoltage: z.nativeEnum(
    xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage,
  ),
  nominalVoltage: z.nativeEnum(
    xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage,
  ),
});

const EquipmentProfileChillerDataSchema = z.object({
  atmPres: z.number(),
  equipmentInfo: chillerEquipmentInfoSchema,
  alertConfiguration: z
    .array(
      z.object({
        point: z.nativeEnum(xEquipmentProfiles.AlertConfigurationPoints),
        recommended: z.number(),
        current: z.number(),
      }),
    )
    .optional(),
});

export const ChillerEquipmentProfileSetupSchema = z.object({
  id: z.string().optional(),
  isDefault: z.boolean().optional(),
  inServiceDate: z.union([z.date(), z.string(), z.null()]).optional(),
  oaSensorOptional: z.boolean().optional(),
  voltsSensorOptional: z.boolean().optional(),
  systemProfileId: z.string().optional(),
  equipmentId: z.string().optional(),
  data: EquipmentProfileChillerDataSchema,
  createdAt: z.union([z.date(), z.string()]).optional(),
  updatedAt: z.union([z.date(), z.string()]).optional(),
});
