import type { Corporate, Prisma } from '@nclarity/database';
import logger from '@nclarity/logger';
import { z } from 'zod';

import { DatabaseManager } from '../../../../database/index.js';
import {
  EquipmentProfileSetupSchema,
  SystemProfileSchema,
} from '../../setup/validations.js';

export async function updateEquipmentProfileUseCaseV2(params: {
  equipmentProfileId: string;
  payload: {
    profile: Prisma.EquipmentProfileUncheckedUpdateInput & { id: string };
    systemProfile?: Prisma.SystemProfileUncheckedUpdateInput & { id: string };
    corporate: Corporate;
  };
}) {
  const { equipmentProfileId, payload } = params;

  const { profile, systemProfile, corporate } = payload;

  const { relational } = DatabaseManager.getInstances();

  const parsed = z
    .object({
      equipmentProfile: EquipmentProfileSetupSchema,
    })
    .parse({ equipmentProfile: profile });

  const updatedProfile = await relational.$transaction(async (tx) => {
    if (systemProfile) {
      const existingSystemProfile =
        systemProfile.id !== 'custom'
          ? await tx.systemProfile.findUnique({
              where: {
                id: systemProfile.id,
              },
            })
          : undefined;

      if (existingSystemProfile) {
        parsed.equipmentProfile.systemProfileId = existingSystemProfile.id;
      } else {
        const systemProfileParsed = z
          .object({
            systemProfile: SystemProfileSchema,
          })
          .parse({
            systemProfile: systemProfile
              ? { ...systemProfile, corporateId: corporate.id }
              : {},
          });

        const systemProfileCreated = await tx.systemProfile.create({
          data: {
            ...systemProfileParsed.systemProfile,
            id: undefined,
          },
        });

        parsed.equipmentProfile.systemProfileId = systemProfileCreated.id;
      }
    }

    // Update equipment profile
    const equipmentProfile = await tx.equipmentProfile.update({
      where: {
        id: equipmentProfileId,
      },
      data: {
        ...(parsed.equipmentProfile as Prisma.EquipmentProfileUncheckedUpdateInput),
        isDefault: false,
      },
      include: {
        systemProfile: true,
      },
    });

    return equipmentProfile;
  });

  logger.info('Equipment profile updated successfully');

  return updatedProfile;
}
