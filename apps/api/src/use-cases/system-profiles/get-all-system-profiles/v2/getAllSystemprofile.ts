import { DatabaseManager } from '../../../../database/index.js';

export async function getAllSytemProfilesUseCaseV2(corporateId: number) {
  const { relational: db } = DatabaseManager.getInstances();

  const systemProfiles = await db.systemProfile.findMany({
    where: { corporateId },
  });

  const defaultSystemProfiles = await db.systemProfile.findMany({
    where: { isDefault: true },
  });

  systemProfiles.push(...defaultSystemProfiles);

  return systemProfiles;
}
