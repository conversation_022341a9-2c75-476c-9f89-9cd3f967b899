import { DatabaseManager } from '../../../database/index.js';

export async function getOneEquipmentUseCaseV2(equipmentId: string) {
  const { relational: db } = DatabaseManager.getInstances();

  const equipment = await db.equipment.findUniqueOrThrow({
    where: { id: equipmentId },
    include: {
      building: {
        include: {
          customer: {
            include: {
              corporate: true,
            },
          },
        },
      },
      statusRecord: true,
      device: {
        include: {
          type: true,
        },
      },
      ruleSets: {
        include: {
          insightRule: {
            include: {
              alert: true,
            },
          },
        },
      },
      profile: {
        include: {
          systemProfile: {
            include: {
              refrigerant: true,
            },
          },
        },
      },
    },
  });

  const insightRules = equipment.ruleSets;

  equipment.ruleSets = [];

  return {
    ...equipment,
    insights: insightRules,
  };
}
