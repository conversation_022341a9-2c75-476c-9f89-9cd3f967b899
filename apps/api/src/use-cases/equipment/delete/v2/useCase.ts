import { DeviceState, DeviceStatus } from '@nclarity/database';

import { DatabaseManager } from '../../../../database/index.js';

export async function deleteEquipmentUseCaseV2(equipmentId: string) {
  const { relational: db } = DatabaseManager.getInstances();

  return await db.$transaction(async (tx) => {
    const equipment = await tx.equipment.findUniqueOrThrow({
      where: { id: equipmentId },
      include: {
        building: {
          include: {
            customer: {
              include: {
                corporate: true,
              },
            },
          },
        },
        device: {
          include: {
            type: true,
          },
        },
        ruleSets: true,
        cbmHistory: true,
        ExportRequestEquipmentIds: true,
        notes: true,
        tags: true,
      },
    });

    if (equipment.device) {
      await tx.device.update({
        where: {
          id: equipment.device.id,
        },
        data: {
          state: DeviceState.Unassigned,
          status: DeviceStatus.Offline,
          equipment: {
            disconnect: true,
          },
        },
      });
    }

    await tx.insightRuleToEquipment.deleteMany({
      where: { equipmentId: equipment.id },
    });

    await tx.exportRequestEquipmentIds.deleteMany({
      where: { equipmentId: equipment.id },
    });

    const deletedEquipment = await tx.equipment.delete({
      where: { id: equipmentId },
    });

    return deletedEquipment;
  });
}
