import { randomUUID } from 'crypto';
import { getDatabaseAdapter } from '@nclarity/adapters';
import {
  DeviceState,
  DeviceStatus,
  EquipmentCondition,
  EquipmentStatus,
  EquipmentType,
  xEquipmentProfiles,
  type EquipmentCreationSchemaPayload,
} from '@nclarity/database';

import { DatabaseManager } from '../../../../database/index.js';
import { env } from '../../../../env.mjs';

export async function createEquipmentUseCaseV2(
  payload: EquipmentCreationSchemaPayload,
) {
  const { relational: db } = DatabaseManager.getInstances();
  const databaseAdapter = getDatabaseAdapter({
    transactional: env.MysqlConnectionString,
    analytics: env.AnalyticsDbConnectionString,
  });

  const building = await db.building.findUniqueOrThrow({
    where: { id: payload.buildingId },
    include: {
      customer: {
        include: {
          corporate: {
            include: {
              users: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
      },
    },
  });

  const result = await databaseAdapter.relational.client.$transaction(
    async (tx) => {
      const equipmentId = randomUUID() as string;
      const statusRecordId = randomUUID() as string;

      const device = await tx.device.findUniqueOrThrow({
        where: {
          id: payload.deviceId, // this is the uuid of the device not friendly id
        },
        include: {
          type: true,
        },
      });

      // Change device state to assigned
      await tx.device.update({
        where: {
          id: device.id,
        },
        data: {
          state: DeviceState.Assigned,
          status: DeviceStatus.AwaitingInstallation,
        },
      });

      const statusRecord = await tx.equipmentStatusRecord.create({
        data: {
          status: EquipmentStatus.AwaitingInstallation,
          condition: EquipmentCondition.MissingProfile,
          equipmentId,
          id: statusRecordId,
        },
      });

      const profile = await tx.equipmentProfile.create({
        data: {
          equipmentId: equipmentId,
          data: {
            atmPres: 14,
            equipmentInfo: {
              kind: payload.kind,
              equipmentId: equipmentId,
              System:
                payload.kind === EquipmentType.Packaged
                  ? {
                      Name: device.deviceId,
                      systemType:
                        xEquipmentProfiles.EquipmentInfoSystemType.Package,
                      frequency:
                        xEquipmentProfiles.EquipmentInfoSystemFrequency['60Hz'], // Required field for Package type
                    }
                  : {
                      Name: device.deviceId,
                      systemType:
                        xEquipmentProfiles.EquipmentInfoSystemType.Chiller,
                      frequency:
                        xEquipmentProfiles.EquipmentInfoSystemFrequency['60Hz'], // Required field for Chiller type
                    },
              ...(payload.kind === EquipmentType.Packaged
                ? {
                    Condenser: {
                      NominalVoltage:
                        xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage[
                          '208V/230'
                        ],
                      Phase:
                        xEquipmentProfiles.EquipmentInfoCondenserPhase
                          .SinglePhase,
                    },
                  }
                : {
                    chillerPhase:
                      xEquipmentProfiles.EquipmentInfoCondenserPhase
                        .SinglePhase,
                    nominalVoltage:
                      xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage[
                        '208V/230'
                      ],
                  }),
            },
          },
          systemProfile: {
            connect: {
              id: '90005',
            },
          },
        },
      });

      const equipment = await tx.equipment.create({
        data: {
          id: equipmentId,
          pulseId: device.deviceId,
          type: payload.kind,
          profile: {
            connect: {
              id: profile.id,
            },
          },
          building: {
            connect: {
              id: building.id,
            },
          },
          statusRecord: {
            connect: {
              id: statusRecord.id,
            },
          },
          device: {
            connect: {
              id: device.id,
            },
          },
        },
        include: {
          profile: {
            include: {
              systemProfile: true,
            },
          },
        },
      });

      const rulesForCorporate =
        await databaseAdapter.relational.getRulesForAccount(
          building.customer.corporateId,
        );

      if (rulesForCorporate.length > 0) {
        await tx.insightRuleToEquipment.createMany({
          data: rulesForCorporate.map((rule) => ({
            insightRuleId: rule.id,
            equipmentId: equipment.id,
            isActive: rule.isActive,
          })),
        });
      }

      return {
        ...equipment,
        device: device,
      };
    },
  );

  return result;
}
