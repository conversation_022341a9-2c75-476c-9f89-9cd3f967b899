import { randomUUID } from 'crypto';
import { getDatabaseAdapter } from '@nclarity/adapters';
import {
  EquipmentCondition,
  EquipmentStatus,
  EquipmentType,
  xEquipmentProfiles,
} from '@nclarity/database';
import logger from '@nclarity/logger';

import { env } from '../../../../env.mjs';

export async function createEquipmentWithoutDeviceUseCaseV2(params: {
  buildingId: string;
  kind: EquipmentType;
}) {
  const databaseAdapter = getDatabaseAdapter({
    analytics: env.AnalyticsDbConnectionString,
    transactional: env.MysqlConnectionString,
  });

  const building = await databaseAdapter.relational.getOneBuildingById(
    params.buildingId,
  );

  const result = await databaseAdapter.relational.client.$transaction(
    async (tx) => {
      const equipmentId = randomUUID() as string;
      const statusRecordId = randomUUID() as string;
      const randomString = randomUUID() as string; //this is a temporary fix to avoid the error of missing pulseId

      const statusRecord = await tx.equipmentStatusRecord.create({
        data: {
          status: EquipmentStatus.AwaitingInstallation,
          condition: EquipmentCondition.MissingProfile,
          equipmentId,
          id: statusRecordId,
        },
      });

      const profile = await tx.equipmentProfile.create({
        data: {
          equipmentId: equipmentId,
          data: {
            atmPres: 14,
            equipmentInfo: {
              kind: params.kind,
              equipmentId: equipmentId,
              System:
                params.kind === EquipmentType.Packaged
                  ? {
                      Name: equipmentId,
                      systemType:
                        xEquipmentProfiles.EquipmentInfoSystemType.Package,
                      frequency:
                        xEquipmentProfiles.EquipmentInfoSystemFrequency['60Hz'], // Required field for Package type
                    }
                  : {
                      Name: equipmentId,
                      systemType:
                        xEquipmentProfiles.EquipmentInfoSystemType.Chiller,
                      frequency:
                        xEquipmentProfiles.EquipmentInfoSystemFrequency['60Hz'], // Required field for Chiller type
                    },
              ...(params.kind === EquipmentType.Packaged
                ? {
                    Condenser: {
                      NominalVoltage:
                        xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage[
                          '208V/230'
                        ],
                      Phase:
                        xEquipmentProfiles.EquipmentInfoCondenserPhase
                          .SinglePhase,
                    },
                  }
                : {
                    chillerPhase:
                      xEquipmentProfiles.EquipmentInfoCondenserPhase
                        .SinglePhase,
                    nominalVoltage:
                      xEquipmentProfiles.EquipmentInfoCondenserNominalVoltage[
                        '208V/230'
                      ],
                  }),
            },
          },
          systemProfile: {
            connect: {
              id: '90005',
            },
          },
        },
      });

      const equipment = await tx.equipment.create({
        data: {
          id: equipmentId,
          pulseId: randomString,
          deviceId: undefined,
          type: params.kind,
          profile: {
            connect: {
              id: profile.id,
            },
          },
          statusRecord: {
            connect: {
              id: statusRecord.id,
            },
          },
          building: {
            connect: {
              id: building.id,
            },
          },
        },
      });

      const rulesForCorporate =
        await databaseAdapter.relational.getRulesForAccount(
          building.customer.corporateId,
        );

      if (rulesForCorporate.length > 0) {
        await tx.insightRuleToEquipment.createMany({
          data: rulesForCorporate.map((rule) => ({
            insightRuleId: rule.id,
            equipmentId: equipment.id,
            isActive: rule.isActive,
          })),
        });
      }

      return equipment;
    },
  );

  logger.info('Created equipment without device and Profile', {
    equipmentId: result.id,
    buildingId: params.buildingId,
  });

  return result;
}
