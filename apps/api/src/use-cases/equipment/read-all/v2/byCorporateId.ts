import { UserRole } from '@nclarity/database';

import { DatabaseManager } from '../../../../database/index.js';
import type { PaginationParams } from '../../../../helpers/paginate.js';
import type { LoggedInUser } from '../../../../middlewares/authentication.js';

export async function listAllEquipmentByCorporateIdUseCaseV2(
  user: LoggedInUser,
  buildingId?: string,
  pagination?: PaginationParams,
) {
  const { relational: db } = DatabaseManager.getInstances();

  const equipment = await db.equipment.findMany({
    where: {
      buildingId,
      building: {
        customer: {
          corporateId: user.accountId,
        },
        OR: [
          ...(user.role === UserRole.SuperAdmin
            ? [
                {
                  // necessary because at the beginning the buildings didn't have a region
                  regionId: null,
                },
              ]
            : []),
          {
            region: {
              id: {
                in: user.regions.map((region) => region.id),
              },
            },
          },
        ],
      },
    },
    include: {
      building: {
        include: {
          customer: true,
        },
      },
      statusRecord: true,
      device: {
        include: {
          type: true,
        },
      },
      profile: {
        include: {
          systemProfile: {
            include: {
              refrigerant: true,
            },
          },
        },
      },
    },
    take: pagination ? (pagination?.take ?? 0) + 1 : undefined,
    skip: pagination?.cursor ? 1 : undefined,
    cursor: pagination?.cursor ? { id: pagination.cursor } : undefined,
    orderBy: {
      pulseId: 'asc',
    },
  });

  const equipmentMapped = equipment.map((item) => {
    const { profile, ...rest } = item;

    return {
      ...rest,
      equipmentProfile: profile, // kept for the mobile application compatibility
    };
  });

  if (!pagination) {
    return {
      data: equipmentMapped,
    };
  }

  const hasMore = equipment.length > (pagination?.take ?? 0);

  if (hasMore) {
    equipment.pop();
  }

  const lastEquipment = equipmentMapped[equipmentMapped.length - 1];
  const nextCursor = hasMore ? lastEquipment?.id : null;

  return {
    data: equipmentMapped,
    pagination: {
      nextCursor,
      take: pagination?.take,
    },
  };
}
