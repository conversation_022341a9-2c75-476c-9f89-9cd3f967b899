# Test environment variables for API integration tests

# Node environment
NODE_ENV=test

# Database connection strings
MysqlConnectionString="mysql://nclarityuser:secret-password@localhost:3306/nclarity-db-development-01?connection_limit=500"
AnalyticsDbConnectionString="mysql://nclarityuser:secret-password@localhost:3306/analytics-development-01?connection_limit=500"

# JWT token for authentication
jwt_token=test-jwt-token

# CouchDB connection details
couch_db_protocol=http://
couch_db_host=localhost:5984
couch_db_username=admin
couch_db_password=password

# API and dashboard URLs
nclarity_api_url=http://localhost:3021
dashboard_url=http://localhost:3000
operations_url=http://localhost:3001

# Azure IoT Central
azure_iot_central_device_template_id=dtmi:nclarity:iPulse347;4
AZURE_IOTC_APP_BASE_DOMAIN=azureiotcentral.com
AZURE_IOTC_APP_NAME=nclarity-test
AZURE_IOTC_API_SAS_TOKEN=test-token
AZURE_IOTC_API_VERSION=2022-10-31-preview

# Azure Storage
AzureStorageAccountName=devstoreaccount1
AzureStorageAccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==

# Azure Event Hub
AzureEventHubConnectionString=Endpoint=sb://test.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=test
AzureEventHubName=eh-test-01

# Backend Functions
BACKEND_FUNCTIONS_APP_URL=http://localhost:7071
TELEMETRY_EXPORTER_FUNCTION_KEY=test-key
TELEMETRY_HISTORY_ACCOUNT_NAME=devstoreaccount1
TELEMETRY_HISTORY_CONTAINER_NAME=telemetryeventcapture
TELEMETRY_HISTORY_ACCOUNT_KEY=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==

# Redis
REDIS_HOST=localhost
REDIS_PASSWORD=secret-redis-password

# Time Series Database
TSDB_URL=http://localhost:8086
TSDB_TOKEN=mysupersecrettoken
TSDB_ORG=nclarity
TSDB_BUCKET=devices_telemetry
NEXUS_ENV=test

# PostHog
POSTHOG_HOST=https://us.i.posthog.com
POSTHOG_PROJECT_ID=test-project-id
POSTHOG_API_KEY=test-api-key

# SendGrid
sendgrid_api_key=test-sendgrid-key

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=********-0000-0000-0000-********0000;IngestionEndpoint=https://eastus2-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus2.livediagnostics.monitor.azure.com/

# Feature flags
CAN_CREATE_DEFAULT_RULES=true

# Port
PORT=3021
