import express, { type Application } from 'express';
import { vi } from 'vitest';

import { authenticationMiddleware } from '../../src/middlewares/authentication.js';
import { registerErrorHandler } from '../../src/middlewares/index.js';
import { TestDatabaseManager } from './database.js';

// Mock the DatabaseManager in the app to use our TestDatabaseManager
vi.mock('../../src/database/index.js', () => ({
  DatabaseManager: {
    connect: () => TestDatabaseManager.connect(),
    getInstances: () => TestDatabaseManager.getInstances(),
  },
}));

// Create a test app with the given router
export function createTestApp(
  routerSetup: (app: Application) => void,
): Application {
  // Create Express app
  const app = express();

  // Add JSON middleware
  app.use(express.json());

  // Add authentication middleware
  app.use(authenticationMiddleware);

  // Add router
  routerSetup(app);

  // Add error handler
  registerErrorHandler(app);

  return app;
}

// Initialize the test database
export function initTestDatabase(): void {
  // Make sure environment variables are set
  if (
    !process.env.MysqlConnectionString ||
    !process.env.AnalyticsDbConnectionString
  ) {
    throw new Error(
      'Database connection strings not set. Make sure integration-setup.ts is loaded.',
    );
  }

  // Connect to the test database
  TestDatabaseManager.connect();
}

// Clean up after tests
export async function cleanupTestDatabase(): Promise<void> {
  await TestDatabaseManager.cleanup();
  console.log('Test database cleaned up');
}
