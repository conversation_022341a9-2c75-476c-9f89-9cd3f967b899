import { UserRole } from '@nclarity/database';
import jwt from 'jsonwebtoken';

import { UserProfile } from '../../src/use-cases/authentication/dashboard/login/useCase.js';

// JWT token for tests
const JWT_TOKEN = process.env.JWT_TOKEN || 'test-jwt-token';

export interface TestUser {
  id: number;
  email: string;
  displayName: string;
  role: UserRole;
  accountId: number;
}

export interface TestOpsUser {
  id: number;
  email: string;
  displayName: string;
  role: string;
}

// Create a JWT token for a regular user
export function createUserToken(user: TestUser): string {
  const token = jwt.sign(
    {
      user: {
        email: user.email,
        id: user.id,
        // The rest of this information is needed to keep compatibility
        // eslint-disable-next-line camelcase
        user_id: user.id,
        displayname: user.displayName,
        username: user.email,
        role: user.role,
        isAdmin: user.role === UserRole.SuperAdmin,
        // eslint-disable-next-line camelcase
        group_id: 1,
        compID: user.accountId,
      },
    },
    JWT_TOKEN,
    {
      expiresIn: '1h',
    },
  );

  return token;
}

// Create a JWT token for an ops user
export function createOpsUserToken(user: TestOpsUser): string {
  const token = jwt.sign(
    {
      user: {
        email: user.email,
        id: user.id,
        profile: UserProfile.OpsUser,
        // The rest of this information is needed to keep compatibility
        // eslint-disable-next-line camelcase
        user_id: user.id,
        displayname: user.displayName,
        username: user.email,
        role: user.role,
        isAdmin: user.role === 'Administrator',
        // eslint-disable-next-line camelcase
        group_id: 1,
        compID: 0,
      },
    },
    JWT_TOKEN,
    {
      expiresIn: '1h',
    },
  );

  return token;
}

// Predefined test users
export const TEST_USERS = {
  admin: {
    id: 1,
    email: '<EMAIL>',
    displayName: 'Test Admin',
    role: UserRole.SuperAdmin,
    accountId: 999,
  },
  serviceManager: {
    id: 2,
    email: '<EMAIL>',
    displayName: 'Test Service Manager',
    role: UserRole.ServiceManager,
    accountId: 999,
  },
  technician: {
    id: 3,
    email: '<EMAIL>',
    displayName: 'Test Technician',
    role: UserRole.Technician,
    accountId: 999,
  },
};

// Predefined test ops users
export const TEST_OPS_USERS = {
  admin: {
    id: 1,
    email: '<EMAIL>',
    displayName: 'Test Ops Admin',
    role: 'Administrator',
  },
};

// Get authorization header with token
export function getAuthHeader(token: string): { Authorization: string } {
  return { Authorization: `Bearer ${token}` };
}
