import { randomUUID } from 'node:crypto';
import { getDatabaseAdapter, type DatabaseAdapter } from '@nclarity/adapters';
import {
  UserRole,
  createAnalyticsDbClient,
  createRelationalClient,
  type Analytics,
  type SafeUser,
  type TransactionalDbClient,
} from '@nclarity/database';
import logger, { createCustomLogger } from '@nclarity/logger';

import { createDeviceUseCase } from '../../src/use-cases/device/create-device/useCase.js';
import { createEquipmentUseCaseV2 } from '../../src/use-cases/equipment/create/v2/withDeviceUseCase.js';
import { TEST_USERS } from './auth.js';

// This is a real database connection for integration tests
// It connects to the test database defined in docker-compose
export type RelationalDatabaseInterface = TransactionalDbClient.Client;

function initializeRelationalDatabase() {
  const databaseLogger = createCustomLogger({
    kind: 'appInsights',
    appName: '@nclarity/api-test',
    cloudRole: '@nclarity/api-test',
    operationName: 'Prisma',
  });

  const relationalClientCreated = createRelationalClient(databaseLogger, () => {
    // No-op for tests
  });

  return relationalClientCreated;
}

function initializeAnalyticsDatabase() {
  const analyticsClient = createAnalyticsDbClient(logger, () => {
    // No-op for tests
  });

  return analyticsClient;
}

type DatabaseInstances = {
  relational: RelationalDatabaseInterface;
  analytics: Analytics.Client;
  adapter: DatabaseAdapter;
};

// This class mimics the DatabaseManager in the main app
// but connects to the test database
export class TestDatabaseManager {
  static #instances: DatabaseInstances;

  public static connect(): void {
    // Use test database connection strings
    const relationalInstance = initializeRelationalDatabase();
    const analyticsInstance = initializeAnalyticsDatabase();
    const databaseAdapter = getDatabaseAdapter({
      analytics: process.env.AnalyticsDbConnectionString || '',
      transactional: process.env.MysqlConnectionString || '',
    });

    this.#instances = {
      relational: relationalInstance,
      analytics: analyticsInstance,
      adapter: databaseAdapter,
    };
  }

  public static getInstances(): DatabaseInstances {
    return this.#instances;
  }

  // Set up test data
  public static async setupTestData() {
    const { relational: db } = this.getInstances();
    const { admin } = TEST_USERS;

    const customerId = randomUUID();
    const buildingId = randomUUID();

    const type = await db.deviceType.findFirstOrThrow({
      where: {
        iotCentralTemplateId: 'dtmi:nclarity:iPulse347;4',
      },
    });

    const [corporate, user, , customer, building] = await db.$transaction([
      db.corporate.upsert({
        where: { id: 999 },
        update: {},
        create: {
          id: 999,
          companyName: 'Test Corporate',
          createdAt: Math.floor(Date.now() / 1000),
          updatedAt: Math.floor(Date.now() / 1000),
        },
      }),
      db.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          id: admin.id,
          email: '<EMAIL>',
          displayName: 'Test User',
          password: 'test',
          role: UserRole.SuperAdmin,
          status: 'Active',
          accountId: 999,
          userName: '<EMAIL>',
          salt: 'test',
          activationToken: 'test',
          lastActivationRequest: Math.floor(Date.now() / 1000),
          lostPasswordRequest: false,
          active: 1,
          lastSignInStamp: Math.floor(Date.now() / 1000),
          lastSignInStampApp: Math.floor(Date.now() / 1000),
          signUpStamp: Math.floor(Date.now() / 1000),
        },
      }),
      db.corporate.update({
        where: { id: 999 },
        data: {
          mainUserId: admin.id,
        },
      }),
      db.customer.upsert({
        where: { id: customerId },
        update: {},
        create: {
          id: customerId,
          name: 'Test Customer',
          logo: 'test-logo',
          corporateId: 999,
        },
      }),
      db.building.upsert({
        where: { id: buildingId },
        update: {},
        create: {
          id: buildingId,
          name: 'Test Building',
          customerId,
          longitude: 0,
          latitude: 0,
        },
      }),
    ]);

    const device = await createDeviceUseCase({
      deviceId: 'TEST-DEVICE-ID',
      corporateId: 999,
      typeId: type.id,
      isGateway: false,
    });

    const equipment = await createEquipmentUseCaseV2({
      buildingId,
      deviceId: device.id,
      kind: 'Packaged',
    });

    return {
      corporate,
      customer,
      building,
      equipment,
      device,
      user: user as unknown as SafeUser,
    };
  }

  // Clean up test data after tests
  public static async cleanup(): Promise<void> {
    const { relational: db } = this.getInstances();

    try {
      await db.$transaction([
        db.equipment.deleteMany(),
        db.device.deleteMany(),
        db.equipmentProfile.deleteMany(),
        db.equipmentStatusRecord.deleteMany(),
        db.deviceInitialConfig.deleteMany(),
        db.building.deleteMany(),
        db.customer.deleteMany(),
        db.user.deleteMany(),
        db.corporate.deleteMany(),
      ]);
    } catch (error) {
      console.error('Error cleaning up test data:', error);
    }
  }
}
