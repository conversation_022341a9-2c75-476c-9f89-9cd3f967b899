import express, { type Response } from 'express';
import request from 'supertest';
import { describe, expect, it } from 'vitest';

describe('API Application', () => {
  // Create a simple express app for testing
  const app = express();

  // Set up routes
  app.route('/').all((_, res: Response) => {
    res.send({ status: 'OK', data: 'API OK' });
  });
  app.get('/v3', (_, res: Response) => res.send('PING'));

  describe('Root Endpoint', () => {
    it('should return 200 OK with correct response', async () => {
      const response = (await request(app).get('/')) as request.Response;

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ status: 'OK', data: 'API OK' });
    });
  });

  describe('API Version Endpoint', () => {
    it('should return 200 OK with PING response', async () => {
      const response = (await request(app).get('/v3')) as request.Response;

      expect(response.status).toBe(200);
      expect(response.text).toBe('PING');
    });
  });
});
