// This file is used to set up the test environment
// It will be executed before each test file
import { vi } from 'vitest';

// Note: We're using the real database for integration tests
// The .env.test file should contain all necessary database connection strings

// Mock external services that we don't want to call in tests

// Mock the logger
vi.mock('@nclarity/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
  createCustomLogger: vi.fn().mockReturnValue({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  }),
  setLogger: vi.fn(),
}));

// Mock app insights tracker
vi.mock('../src/services/app-insights-tracker/index.js', () => ({
  getAppInsightsTracker: vi.fn().mockReturnValue({
    trackEvent: vi.fn(),
    trackException: vi.fn(),
    trackMetric: vi.fn(),
    trackTrace: vi.fn(),
  }),
}));

// Mock SendGrid
vi.mock('@sendgrid/mail', () => ({
  default: {
    setApiKey: vi.fn(),
    send: vi.fn().mockResolvedValue({}),
  },
}));

// Mock Azure Storage
vi.mock('@azure/storage-blob', () => ({
  BlobServiceClient: {
    fromConnectionString: vi.fn().mockReturnValue({
      getContainerClient: vi.fn().mockReturnValue({
        getBlockBlobClient: vi.fn().mockReturnValue({
          upload: vi.fn().mockResolvedValue({}),
          delete: vi.fn().mockResolvedValue({}),
        }),
      }),
    }),
  },
}));

// Mock Azure Event Hub
vi.mock('@azure/event-hubs', () => ({
  EventHubProducerClient: vi.fn().mockImplementation(() => ({
    createBatch: vi.fn().mockResolvedValue({
      tryAdd: vi.fn().mockReturnValue(true),
    }),
    sendBatch: vi.fn().mockResolvedValue({}),
    close: vi.fn().mockResolvedValue({}),
  })),
}));
