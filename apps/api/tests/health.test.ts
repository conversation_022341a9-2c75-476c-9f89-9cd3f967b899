import express from 'express';
import request from 'supertest';
import { describe, expect, it } from 'vitest';

// Create a simple express app for testing
const app = express();

app.get('/', (req, res) => {
  res.status(200).json({ status: 'OK', data: 'API OK' });
});

describe('Health Check Endpoint', () => {
  it('should return 200 OK with correct response', async () => {
    const response = await request(app).get('/');

    expect(response.status).toBe(200);
    expect(response.body).toEqual({ status: 'OK', data: 'API OK' });
  });
});
